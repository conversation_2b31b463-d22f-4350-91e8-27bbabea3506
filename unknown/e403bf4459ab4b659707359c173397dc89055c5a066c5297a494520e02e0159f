#!/usr/bin/env python3
"""
Crea utente di test per verificare il sistema di temi personalizzati
"""

from database import SessionLocal
from sqlalchemy import text
import bcrypt

def create_test_user():
    """Crea utente Felice per test temi"""
    db = SessionLocal()
    
    try:
        print("🧪 Creazione utente di test per temi personalizzati...")
        
        # Crea password hash
        password_hash = bcrypt.hashpw('test123'.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
        
        # Inserisci o aggiorna utente
        result = db.execute(text("""
            INSERT INTO "AGENTE" ("Nome", "Cognome", email, password, reparto, ruolo, visibile, tema_preferito)
            VALUES (:nome, :cognome, :email, :password, :reparto, :ruolo, :visibile, :tema)
            ON CONFLICT (email) DO UPDATE SET
            tema_preferito = :tema,
            password = :password
            RETURNING id_user
        """), {
            'nome': 'Felice',
            'cognome': 'Test',
            'email': '<EMAIL>',
            'password': password_hash,
            'reparto': 'OPERATIVO',
            'ruolo': 'USER',
            'visibile': 'si',
            'tema': 'dark'
        })
        
        user_id = result.fetchone()[0]
        db.commit()
        
        print(f"✅ Utente Felice creato/aggiornato:")
        print(f"   - ID: {user_id}")
        print(f"   - Email: <EMAIL>")
        print(f"   - Password: test123")
        print(f"   - Tema: dark")
        print(f"   - Reparto: OPERATIVO")
        
        # Verifica utenti esistenti e loro temi
        print("\n📊 Utenti e temi attuali:")
        users = db.execute(text("""
            SELECT id_user, "Cognome", email, tema_preferito
            FROM "AGENTE"
            ORDER BY id_user
            LIMIT 5
        """)).fetchall()
        
        for user in users:
            print(f"   ID: {user[0]:<3} | {user[1]:<15} | {user[2]:<25} | Tema: {user[3]}")
        
        print("\n🎯 Test da eseguire:")
        print("   1. <NAME_EMAIL> (tema: light)")
        print("   2. Cambia tema a 'dark' dal selettore")
        print("   3. Logout e <NAME_EMAIL> (tema: dark)")
        print("   4. Verifica che Felice veda il tema scuro")
        print("   5. Cambia tema di Felice a 'light'")
        print("   6. Accedi di nuovo come ettore e verifica che il suo tema sia rimasto 'dark'")
        
    except Exception as e:
        print(f"❌ Errore: {e}")
        db.rollback()
    finally:
        db.close()

if __name__ == "__main__":
    create_test_user()
