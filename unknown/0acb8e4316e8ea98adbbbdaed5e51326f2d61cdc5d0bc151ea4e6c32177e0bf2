{% extends "base_operativo.html" %}

{% block title %}Gestione Viaggi - M.A.P.{% endblock %}

{% block extra_css %}
<link href="/static/css/armatori.css" rel="stylesheet">
<style>
/* ===== TABELLA LISTA VIAGGI COMPATTA ===== */
.table-compact {
    font-size: 0.85rem !important;
}

.table-compact th {
    padding: 8px 12px !important;
    font-size: 0.8rem !important;
    font-weight: 600 !important;
    white-space: nowrap !important;
    vertical-align: middle !important;
}

.table-compact td {
    padding: 10px 12px !important;
    vertical-align: middle !important;
}

/* Avatar circle più piccolo */
.table-compact .avatar-circle {
    width: 28px !important;
    height: 28px !important;
    font-size: 0.7rem !important;
    margin-right: 8px !important;
}

/* Badge più piccoli */
.table-compact .badge {
    font-size: 0.7rem !important;
    padding: 2px 6px !important;
    border-radius: 8px !important;
    white-space: nowrap !important;
    display: inline-block !important;
}

/* Badge porto di gestione - NO TRONCAMENTO */
.table-compact .badge.bg-primary.porto-gestione-badge {
    white-space: normal !important;
    overflow: visible !important;
    text-overflow: unset !important;
    max-width: none !important;
    word-wrap: break-word !important;
    line-height: 1.3 !important;
    padding: 6px 10px !important;
    font-size: 0.75rem !important;
    font-weight: 500 !important;
    border-radius: 10px !important;
    display: inline-flex !important;
    align-items: center !important;
    min-height: 24px !important;
}

/* Icona nel badge porto */
.table-compact .badge.porto-gestione-badge i {
    font-size: 0.7rem !important;
    margin-right: 4px !important;
    flex-shrink: 0 !important;
}

/* Colonna porto di gestione specifica */
.table-compact td:nth-child(3) {
    min-width: 120px !important;
    max-width: 180px !important;
    word-wrap: break-word !important;
    vertical-align: middle !important;
}

/* Tooltip per nomi porto lunghi */
.porto-gestione-badge[title] {
    cursor: help !important;
    position: relative !important;
}

.porto-gestione-badge[title]:hover {
    transform: scale(1.02) !important;
    box-shadow: 0 2px 8px rgba(0,0,0,0.15) !important;
    z-index: 10 !important;
}

/* Pulsanti azioni più compatti */
.table-compact .action-buttons {
    display: flex !important;
    gap: 4px !important;
    flex-wrap: wrap !important;
}

.table-compact .action-buttons .btn {
    padding: 4px 8px !important;
    font-size: 0.7rem !important;
    border-radius: 4px !important;
}

.table-compact .action-buttons .btn i {
    font-size: 0.7rem !important;
    margin-right: 2px !important;
}

/* Icone header più piccole */
.table-compact th i {
    font-size: 0.75rem !important;
    margin-right: 4px !important;
}

/* Icone nelle celle più piccole */
.table-compact td i {
    font-size: 0.75rem !important;
    margin-right: 4px !important;
}

/* Testo più compatto */
.table-compact strong {
    font-size: 0.85rem !important;
}

/* Date complete ma compatte */
.table-compact .fw-bold {
    font-size: 0.8rem !important;
    white-space: nowrap !important;
}

/* Colonne date specifiche */
.table-compact th:nth-child(6),
.table-compact th:nth-child(7),
.table-compact td:nth-child(6),
.table-compact td:nth-child(7) {
    white-space: nowrap !important;
    text-align: center !important;
}

/* Responsive per tabella compatta */
@media (max-width: 1200px) {
    .table-compact {
        font-size: 0.8rem !important;
    }

    .table-compact th {
        padding: 6px 8px !important;
        font-size: 0.75rem !important;
    }

    .table-compact td {
        padding: 8px 8px !important;
    }

    .table-compact .action-buttons .btn {
        padding: 3px 6px !important;
        font-size: 0.65rem !important;
    }
}

@media (max-width: 768px) {
    .table-compact .action-buttons {
        flex-direction: column !important;
        gap: 2px !important;
    }

    .table-compact .action-buttons .btn {
        width: 100% !important;
        text-align: center !important;
    }
}

/* Card header più compatto */
.compact-card-header {
    padding: 12px 20px !important;
}

.compact-card-header h5 {
    font-size: 1.1rem !important;
    margin-bottom: 4px !important;
}

.compact-card-header small {
    font-size: 0.8rem !important;
}

/* Card body più compatto */
.compact-card-body {
    padding: 15px 20px !important;
}

/* Ottimizzazioni aggiuntive per spazio */
.table-compact .text-muted {
    font-size: 0.75rem !important;
}

.table-compact .fw-bold {
    font-weight: 500 !important;
}

/* Riduci margini tra elementi */
.table-compact .d-flex {
    gap: 4px !important;
}

/* Ottimizza larghezza colonne per mobile */
@media (max-width: 992px) {
    .table-compact th:nth-child(4),
    .table-compact td:nth-child(4),
    .table-compact th:nth-child(5),
    .table-compact td:nth-child(5) {
        display: none !important;
    }

    .table-compact th:nth-child(1) { width: 15% !important; }
    .table-compact th:nth-child(2) { width: 20% !important; }
    .table-compact th:nth-child(3) { width: 20% !important; } /* Porto di gestione più largo */
    .table-compact th:nth-child(6) { width: 18% !important; }
    .table-compact th:nth-child(7) { width: 18% !important; }
    .table-compact th:nth-child(8) { width: 9% !important; }

    /* Badge porto più piccolo su mobile */
    .table-compact .badge.porto-gestione-badge {
        font-size: 0.65rem !important;
        padding: 4px 6px !important;
        min-height: 20px !important;
    }
}

/* Per schermi molto piccoli, abbrevia le date */
@media (max-width: 576px) {
    .table-compact .fw-bold {
        font-size: 0.75rem !important;
    }
}

/* Hover effect più sottile */
.table-compact tbody tr:hover {
    background-color: rgba(0, 0, 0, 0.02) !important;
}

/* Bordi più sottili */
.table-compact {
    border-collapse: separate !important;
    border-spacing: 0 2px !important;
}

.table-compact td {
    border-top: 1px solid rgba(0, 0, 0, 0.05) !important;
}
</style>
{% endblock %}

{% block content %}
        <!-- Header della pagina migliorato -->
        <div class="page-header text-center">
            <h2><i class="fas fa-route me-3"></i>Gestione Viaggi</h2>
            <p class="subtitle">Gestisci tutti i viaggi del sistema M.A.P.</p>
        </div>

        <!-- Filtri di ricerca -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-search me-2"></i>Filtri di Ricerca</h5>
            </div>
            <div class="card-body">
                <form method="GET" action="/operativo/sof/da-realizzare" id="filtroForm">
                    <div class="row align-items-end">
                        <div class="col-md-4">
                            <label for="porto_gestione" class="form-label">
                                <i class="fas fa-anchor me-1"></i>Porto di Gestione
                            </label>
                            <select class="form-select" id="porto_gestione" name="porto_gestione">
                                <option value="">Tutti i porti</option>
                                {% for porto in porti_gestione %}
                                <option value="{{ porto }}" {% if porto_selezionato == porto %}selected{% endif %}>
                                    {{ porto }}
                                </option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-4">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search me-1"></i>Filtra
                            </button>
                            <a href="/operativo/sof/da-realizzare" class="btn btn-outline-secondary ms-2">
                                <i class="fas fa-times me-1"></i>Reset
                            </a>
                        </div>
                        <div class="col-md-4 text-end">
                            <small class="text-muted">
                                <i class="fas fa-info-circle me-1"></i>
                                {% if porto_selezionato %}
                                    Filtrato per: <strong>{{ porto_selezionato }}</strong>
                                {% else %}
                                    Mostrando tutti i viaggi
                                {% endif %}
                            </small>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- Tabella dei viaggi -->
        <div class="card">
            <div class="card-header compact-card-header">
                <div class="d-flex justify-content-between align-items-start">
                    <div class="flex-grow-1 me-4">
                        <h5 class="mb-0"><i class="fas fa-list me-2"></i>Lista Viaggi</h5>
                        <small class="text-muted">
                            <i class="fas fa-route me-1"></i>
                            {% if porto_selezionato %}
                                Mostrando {{ viaggi|length }} viaggi per {{ porto_selezionato }}
                            {% else %}
                                Mostrando tutti i {{ viaggi|length }} viaggi del sistema
                            {% endif %}
                        </small>
                    </div>

                    <div class="d-flex flex-column align-items-end gap-2">
                        <div class="d-flex gap-2">
                            <button class="btn btn-success btn-sm" data-bs-toggle="modal" data-bs-target="#nuovoViaggioModal">
                                <i class="fas fa-plus me-2"></i>Aggiungi Nuovo Viaggio
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card-body compact-card-body">
                <div class="table-responsive">
                    <table class="table table-hover table-compact">
                        <thead>
                            <tr>
                                <th style="width: 9%;"><i class="fas fa-barcode me-1"></i>Codice</th>
                                <th style="width: 12%;"><i class="fas fa-ship me-1"></i>Nave</th>
                                <th style="width: 14%;"><i class="fas fa-anchor me-1"></i>Porto di Gestione</th>
                                <th style="width: 9%;"><i class="fas fa-map-marker-alt me-1"></i>Arrivo</th>
                                <th style="width: 9%;"><i class="fas fa-flag-checkered me-1"></i>Destinazione</th>
                                <th style="width: 12%;"><i class="fas fa-calendar-alt me-1"></i>Data Arrivo</th>
                                <th style="width: 12%;"><i class="fas fa-calendar-alt me-1"></i>Data Partenza</th>
                                <th style="width: 23%;" class="text-center"><i class="fas fa-cogs me-1"></i>Azioni</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for viaggio in viaggi %}
                            <tr data-viaggio-id="{{ viaggio.id }}">
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="avatar-circle me-2">
                                            <i class="fas fa-route"></i>
                                        </div>
                                        <span class="badge bg-secondary">{{ viaggio.viaggio }}</span>
                                    </div>
                                </td>
                                <td class="cell-nave">
                                    <strong>{{ viaggio.nome_nave or 'N/A' }}</strong>
                                </td>
                                <td>
                                    {% if viaggio.nome_porto %}
                                        <span class="badge bg-primary porto-gestione-badge" title="{{ viaggio.nome_porto }}">
                                            <i class="fas fa-anchor me-1"></i>{{ viaggio.nome_porto }}
                                        </span>
                                    {% else %}
                                        <span class="text-muted">
                                            <i class="fas fa-minus me-1"></i>N/A
                                        </span>
                                    {% endif %}
                                </td>
                                <td class="cell-porto-arrivo" title="Codice: {{ viaggio.porto_arrivo_code or 'N/A' }}">
                                    {% if viaggio.porto_arrivo_nome %}
                                        <span class="badge bg-info text-dark">{{ viaggio.porto_arrivo_nome }}</span>
                                    {% elif viaggio.porto_arrivo_code %}
                                        <span class="badge bg-secondary text-light">{{ viaggio.porto_arrivo_code }}</span>
                                    {% else %}
                                        <span class="text-muted">N/A</span>
                                    {% endif %}
                                </td>
                                <td class="cell-porto-destinazione" title="Codice: {{ viaggio.porto_destinazione_code or 'N/A' }}">
                                    {% if viaggio.porto_destinazione_nome %}
                                        <span class="badge bg-warning text-dark">{{ viaggio.porto_destinazione_nome }}</span>
                                    {% elif viaggio.porto_destinazione_code %}
                                        <span class="badge bg-secondary text-light">{{ viaggio.porto_destinazione_code }}</span>
                                    {% else %}
                                        <span class="text-muted">N/A</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if viaggio.data_arrivo %}
                                        <span class="text-success fw-bold">
                                            {{ viaggio.data_arrivo.strftime('%d/%m/%Y') if viaggio.data_arrivo.strftime else viaggio.data_arrivo }}
                                        </span>
                                    {% else %}
                                        <span class="text-muted">N/A</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if viaggio.data_partenza %}
                                        <span class="text-warning fw-bold">
                                            {{ viaggio.data_partenza.strftime('%d/%m/%Y') if viaggio.data_partenza.strftime else viaggio.data_partenza }}
                                        </span>
                                    {% else %}
                                        <span class="text-muted">N/A</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="action-buttons">
                                        <a href="/operativo/sof/viaggio/{{ viaggio.id }}" class="btn btn-sm btn-outline-primary" title="Gestisci Viaggio">
                                            <i class="fas fa-cog"></i>
                                        </a>
                                        <button class="btn btn-sm btn-outline-warning" title="Modifica Viaggio"
                                                onclick="modificaViaggio({{ viaggio.id }})">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn btn-sm btn-outline-danger" title="Elimina Viaggio"
                                                onclick="eliminaViaggio({{ viaggio.id }}, '{{ viaggio.viaggio or '' }}')">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                            {% if not viaggi %}
                            <tr>
                                <td colspan="8" class="text-center py-5">
                                    <div class="text-muted">
                                        {% if porto_selezionato %}
                                        <i class="fas fa-search fa-3x mb-3 text-warning"></i>
                                        <h5>Nessun viaggio trovato</h5>
                                        <p>Non ci sono viaggi per il porto <strong>{{ porto_selezionato }}</strong>.<br>
                                        Prova a modificare il filtro o <a href="/operativo/sof/da-realizzare" class="text-decoration-none">visualizza tutti i viaggi</a>.</p>
                                        {% else %}
                                        <i class="fas fa-route fa-3x mb-3"></i>
                                        <h5>Nessun viaggio trovato</h5>
                                        <p>Utilizza il pulsante verde in alto a destra per aggiungere il primo viaggio</p>
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                            {% endif %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal Nuovo Viaggio -->
    <div class="modal fade" id="nuovoViaggioModal" tabindex="-1" aria-labelledby="nuovoViaggioModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-success text-white">
                    <h5 class="modal-title" id="nuovoViaggioModalLabel">
                        <i class="fas fa-plus me-2"></i>Aggiungi Nuovo Viaggio
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form id="nuovoViaggioForm">
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="porto_gestione_id" class="form-label">
                                        <i class="fas fa-anchor me-2"></i>Porto di Gestione
                                    </label>
                                    <select class="form-select" id="porto_gestione_id" name="porto_gestione_id" required>
                                        <option value="">Seleziona porto di gestione...</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="nave_id" class="form-label">
                                        <i class="fas fa-ship me-2"></i>Nave
                                    </label>
                                    <select class="form-select" id="nave_id" name="nave_id" required>
                                        <option value="">Seleziona nave...</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="viaggio_preview" class="form-label">
                                <i class="fas fa-barcode me-2"></i>Codice Viaggio
                            </label>
                            <input type="text" class="form-control" id="viaggio_preview" name="viaggio"
                                   placeholder="Inserisci il codice del viaggio..." required>
                        </div>

                        <!-- Sezione Porti -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="porto_arrivo" class="form-label">
                                        <i class="fas fa-map-marker-alt me-2"></i>Porto di Arrivo
                                    </label>
                                    <select class="form-select" id="porto_arrivo" name="porto_arrivo">
                                        <option value="">Seleziona porto di arrivo...</option>
                                    </select>
                                    <div class="form-text">
                                        <i class="fas fa-info-circle me-1"></i>Sarà automaticamente popolato nella pagina SOF
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="porto_destinazione" class="form-label">
                                        <i class="fas fa-flag-checkered me-2"></i>Porto di Destinazione
                                    </label>
                                    <select class="form-select" id="porto_destinazione" name="porto_destinazione">
                                        <option value="">Seleziona porto di destinazione...</option>
                                    </select>
                                    <div class="form-text">
                                        <i class="fas fa-info-circle me-1"></i>Sarà automaticamente popolato nella pagina SOF
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Sezione Date -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="data_arrivo" class="form-label">
                                        <i class="fas fa-calendar-alt me-2"></i>Data Arrivo
                                    </label>
                                    <input type="date" class="form-control" id="data_arrivo" name="data_arrivo" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="data_partenza" class="form-label">
                                        <i class="fas fa-calendar-alt me-2"></i>Data Partenza
                                    </label>
                                    <input type="date" class="form-control" id="data_partenza" name="data_partenza" required>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                            <i class="fas fa-times me-1"></i>Annulla
                        </button>
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-save me-1"></i>Salva Viaggio
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Modal Modifica Viaggio -->
    <div class="modal fade" id="modificaViaggioModal" tabindex="-1" aria-labelledby="modificaViaggioModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title" id="modificaViaggioModalLabel">
                        <i class="fas fa-edit me-2"></i>Modifica Viaggio
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form id="modificaViaggioForm">
                    <div class="modal-body">
                        <input type="hidden" id="modifica_viaggio_id" name="viaggio_id">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="modifica_porto_gestione_id" class="form-label">
                                        <i class="fas fa-anchor me-2"></i>Porto di Gestione
                                    </label>
                                    <select class="form-select" id="modifica_porto_gestione_id" name="porto_gestione_id" required>
                                        <option value="">Seleziona porto di gestione...</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="modifica_nave_id" class="form-label">
                                        <i class="fas fa-ship me-2"></i>Nave
                                        <span class="badge bg-success ms-2">
                                            <i class="fas fa-edit me-1"></i>Modificabile
                                        </span>
                                    </label>
                                    <select class="form-select" id="modifica_nave_id" name="nave_id" required
                                            title="Puoi cambiare la nave selezionando una diversa dalla lista">
                                        <option value="">Seleziona nave...</option>
                                    </select>
                                    <div class="form-text">
                                        <i class="fas fa-info-circle me-1"></i>
                                        Puoi cambiare la nave selezionando una diversa dalla lista
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="modifica_viaggio_preview" class="form-label">
                                <i class="fas fa-barcode me-2"></i>Codice Viaggio
                                <button type="button" class="btn btn-sm btn-outline-primary ms-2"
                                        id="aggiorna_codice_modifica"
                                        title="Aggiorna il codice viaggio con il prefisso della nave selezionata">
                                    <i class="fas fa-sync-alt me-1"></i>Auto-aggiorna
                                </button>
                            </label>
                            <input type="text" class="form-control" id="modifica_viaggio_preview" name="viaggio"
                                   placeholder="Inserisci il codice del viaggio..." required>
                            <div class="form-text">
                                <i class="fas fa-info-circle me-1"></i>
                                Il codice si aggiorna automaticamente quando cambi nave, oppure usa il pulsante "Auto-aggiorna"
                            </div>
                        </div>

                        <!-- Sezione Porti -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="modifica_porto_arrivo" class="form-label">
                                        <i class="fas fa-map-marker-alt me-2"></i>Porto di Arrivo
                                    </label>
                                    <select class="form-select" id="modifica_porto_arrivo" name="porto_arrivo">
                                        <option value="">Seleziona porto di arrivo...</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="modifica_porto_destinazione" class="form-label">
                                        <i class="fas fa-flag-checkered me-2"></i>Porto di Destinazione
                                    </label>
                                    <select class="form-select" id="modifica_porto_destinazione" name="porto_destinazione">
                                        <option value="">Seleziona porto di destinazione...</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- Sezione Date -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="modifica_data_arrivo" class="form-label">
                                        <i class="fas fa-calendar-alt me-2"></i>Data Arrivo
                                    </label>
                                    <input type="date" class="form-control" id="modifica_data_arrivo" name="data_arrivo" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="modifica_data_partenza" class="form-label">
                                        <i class="fas fa-calendar-alt me-2"></i>Data Partenza
                                    </label>
                                    <input type="date" class="form-control" id="modifica_data_partenza" name="data_partenza" required>
                                </div>
                            </div>
                        </div>
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            Modifica i dati del viaggio e clicca "Salva Modifiche" per confermare.
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                            <i class="fas fa-times me-1"></i>Annulla
                        </button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i>Salva Modifiche
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Modal Elimina Viaggio -->
    <div class="modal fade" id="eliminaViaggioModal" tabindex="-1" aria-labelledby="eliminaViaggioModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header bg-danger text-white">
                    <h5 class="modal-title" id="eliminaViaggioModalLabel">
                        <i class="fas fa-trash me-2"></i>Elimina Viaggio
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="text-center mb-4">
                        <i class="fas fa-exclamation-triangle text-warning fa-4x mb-3"></i>
                        <h5>Conferma Eliminazione</h5>
                        <p class="mb-0">Sei sicuro di voler eliminare il viaggio:</p>
                        <h6 class="text-primary mt-2" id="elimina_viaggio_nome"></h6>
                    </div>
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>Attenzione!</strong> Questa azione eliminerà anche tutti gli orari associati e non può essere annullata.
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-1"></i>Annulla
                    </button>
                    <button type="button" class="btn btn-danger" id="confermaEliminaViaggio">
                        <i class="fas fa-trash me-1"></i>Elimina Definitivamente
                    </button>
                </div>
            </div>
        </div>
    </div>

{% endblock %}

{% block extra_js %}
<script src="/static/js/viaggi.js?v={{ timestamp }}"></script>
<script src="/static/js/sof-filtri.js"></script>

<!-- SCRIPT SEMPLIFICATO PER PORTI -->
<script>
console.log('🚢 Sistema porti caricato');

// SISTEMA NORMALE - CARICAMENTO PORTI
window.caricaPortiNormale = async function() {
    console.log('🚢 CARICAMENTO PORTI NORMALE');

    try {
        const response = await fetch('/api/atlas?limit=1000');
        const data = await response.json();

        if (data.success && data.data) {
            console.log(`📋 Caricati ${data.data.length} porti`);

            // Trova e popola tutti gli elementi porto
            const selectors = ['porto_arrivo', 'porto_destinazione', 'modifica_porto_arrivo', 'modifica_porto_destinazione'];

            selectors.forEach(id => {
                const element = document.getElementById(id);
                if (element) {
                    console.log(`🔧 Popolamento ${id}...`);
                    element.innerHTML = '<option value="">Seleziona porto...</option>';

                    data.data.forEach(porto => {
                        const option = document.createElement('option');
                        option.value = porto.id_cod;
                        option.textContent = porto.porto; // Nome normale senza debug
                        element.appendChild(option);
                    });

                    console.log(`✅ ${id} popolato con ${data.data.length} porti`);
                } else {
                    console.log(`❌ ${id} non trovato`);
                }
            });

            return true;
        } else {
            console.error('❌ API non ha restituito dati validi');
            return false;
        }
    } catch (error) {
        console.error('❌ Errore caricamento normale:', error);
        return false;
    }
};

// CARICAMENTO QUANDO SI APRE LA MODALE
document.addEventListener('DOMContentLoaded', function() {
    const nuovoViaggioModal = document.getElementById('nuovoViaggioModal');
    if (nuovoViaggioModal) {
        nuovoViaggioModal.addEventListener('shown.bs.modal', function() {
            console.log('🚀 Modale aperta - caricamento porti normale');
            setTimeout(() => {
                window.caricaPortiNormale();
            }, 500);
        });
    }
});

console.log('✅ Sistema normale attivato!');

// 🚀🚀🚀 GESTIONE NUCLEARE AGGIORNAMENTO DOPO MODIFICA 🚀🚀🚀
document.addEventListener('DOMContentLoaded', function() {
    console.log('🔥 INIZIALIZZAZIONE NUCLEARE PAGINA SOF DA REALIZZARE');

    // 🔍 CHECK NUCLEARE AUTOMATICO (TEMPORANEAMENTE DISABILITATO)
    // let checkNucleareEseguito = false;
    // if (!checkNucleareEseguito) {
    //     setTimeout(() => {
    //         eseguiCheckNucleareTotale();
    //         checkNucleareEseguito = true;
    //     }, 2000); // Aspetta 2 secondi per il caricamento completo
    // }

    console.log('🔍 CHECK NUCLEARE AUTOMATICO DISABILITATO - Testando sistema base...');

    // Controlla se siamo tornati da una modifica
    const urlParams = new URLSearchParams(window.location.search);
    const viaggioAggiornato = urlParams.get('updated');

    if (viaggioAggiornato) {
        console.log('🔥 RILEVATO RITORNO DA MODIFICA VIAGGIO NUCLEARE:', viaggioAggiornato);

        // Aspetta che la pagina sia completamente caricata
        setTimeout(async () => {
            try {
                console.log('🚀 AGGIORNAMENTO FORZATO NUCLEARE DELLA RIGA:', viaggioAggiornato);

                // Chiama la funzione di aggiornamento riga nucleare
                if (typeof aggiornaRigaViaggio === 'function') {
                    await aggiornaRigaViaggio(parseInt(viaggioAggiornato));
                    console.log('✅ RIGA AGGIORNATA NUCLEARE CON SUCCESSO');
                } else {
                    console.log('⚠️ Funzione aggiornaRigaViaggio non disponibile, RICARICO NUCLEARE');
                    // Fallback: ricarica la pagina senza il parametro
                    const newUrl = window.location.pathname;
                    window.history.replaceState({}, document.title, newUrl);
                    window.location.reload(true);
                }

                // Rimuovi il parametro dall'URL senza ricaricare
                const newUrl = window.location.pathname;
                window.history.replaceState({}, document.title, newUrl);

            } catch (error) {
                console.error('💥 ERRORE NUCLEARE AGGIORNAMENTO FORZATO:', error);
                // Fallback: ricarica la pagina
                const newUrl = window.location.pathname;
                window.history.replaceState({}, document.title, newUrl);
                window.location.reload(true);
            }
        }, 1500); // Aspetta 1.5 secondi per essere sicuri che tutto sia caricato
    }
});

/**
 * 🚀🚀🚀 CHECK NUCLEARE TOTALE 🚀🚀🚀
 * Verifica e corregge automaticamente tutti i problemi della pagina
 */
async function eseguiCheckNucleareTotale() {
    console.log('🔥🔥🔥 INIZIO CHECK NUCLEARE TOTALE 🔥🔥🔥');

    try {
        // 🔍 VERIFICA TUTTE LE RIGHE DELLA TABELLA
        const righe = document.querySelectorAll('tr[data-viaggio-id]');
        console.log(`🔍 Trovate ${righe.length} righe da verificare`);

        let problemiRilevati = 0;
        let correzioniEffettuate = 0;

        for (const riga of righe) {
            const viaggioId = riga.getAttribute('data-viaggio-id');
            const cellaNave = riga.querySelector('.cell-nave');

            if (cellaNave) {
                const naveText = cellaNave.textContent.trim();
                console.log(`🔍 Verifica nucleare viaggio ${viaggioId}: nave = "${naveText}"`);

                // 🚨 SALTA SE GIÀ IN CORREZIONE
                if (riga.getAttribute('data-correzione-in-corso') === 'true') {
                    console.log(`⏭️ Viaggio ${viaggioId} già in correzione, salto...`);
                    continue;
                }

                // 🚨 PROBLEMA RILEVATO: ATLANTIC STAR
                if (naveText.includes('ATLANTIC STAR') || naveText.includes('﻿ATLANTIC STAR')) {
                    console.error(`🚨 PROBLEMA NUCLEARE RILEVATO: Viaggio ${viaggioId} ha nave ATLANTIC STAR!`);
                    problemiRilevati++;

                    try {
                        console.log(`🔧 CORREZIONE NUCLEARE AUTOMATICA per viaggio ${viaggioId}...`);

                        // 🚨 EVITA LOOP INFINITO - Segna la riga come "in correzione"
                        riga.setAttribute('data-correzione-in-corso', 'true');

                        await aggiornaRigaViaggio(parseInt(viaggioId));
                        correzioniEffettuate++;
                        console.log(`✅ CORREZIONE NUCLEARE COMPLETATA per viaggio ${viaggioId}`);

                        // Aspetta un po' tra le correzioni per non sovraccaricare
                        await new Promise(resolve => setTimeout(resolve, 1000));

                    } catch (error) {
                        console.error(`💥 ERRORE CORREZIONE NUCLEARE viaggio ${viaggioId}:`, error);
                        // Rimuovi il flag in caso di errore
                        riga.removeAttribute('data-correzione-in-corso');
                    }
                }

                // 🔍 VERIFICA PORTI N/A
                const cellaPortoArrivo = riga.querySelector('.cell-porto-arrivo');
                const cellaPortoDestinazione = riga.querySelector('.cell-porto-destinazione');

                if (cellaPortoArrivo && cellaPortoArrivo.textContent.trim() === 'N/A') {
                    console.log(`⚠️ Porto arrivo N/A per viaggio ${viaggioId}, potrebbe necessitare correzione`);
                }

                if (cellaPortoDestinazione && cellaPortoDestinazione.textContent.trim() === 'N/A') {
                    console.log(`⚠️ Porto destinazione N/A per viaggio ${viaggioId}, potrebbe necessitare correzione`);
                }
            }
        }

        // 📊 REPORT NUCLEARE
        console.log('📊 REPORT CHECK NUCLEARE TOTALE:');
        console.log(`   🔍 Righe verificate: ${righe.length}`);
        console.log(`   🚨 Problemi rilevati: ${problemiRilevati}`);
        console.log(`   🔧 Correzioni effettuate: ${correzioniEffettuate}`);

        if (problemiRilevati === 0) {
            console.log('🎉 CHECK NUCLEARE COMPLETATO: Nessun problema rilevato!');
        } else if (correzioniEffettuate === problemiRilevati) {
            console.log('🎉 CHECK NUCLEARE COMPLETATO: Tutti i problemi sono stati corretti!');
        } else {
            console.log('⚠️ CHECK NUCLEARE COMPLETATO: Alcuni problemi potrebbero persistere');
        }

    } catch (error) {
        console.error('💥 ERRORE CRITICO NEL CHECK NUCLEARE TOTALE:', error);
    }
}

// 🔄 La funzione aggiornaRigaViaggio è definita in static/js/viaggi.js
// e viene caricata automaticamente. Non serve ridefinirla qui.
console.log('🔄 Funzione aggiornaRigaViaggio sarà disponibile da viaggi.js');
</script>
{% endblock %}
