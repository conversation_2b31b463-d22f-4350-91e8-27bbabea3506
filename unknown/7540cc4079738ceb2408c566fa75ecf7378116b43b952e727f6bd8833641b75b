{% extends "base_operativo.html" %}

{% block title %}Dettag<PERSON> {{ viaggio.viaggio }} - M.A.P.{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{{ url_for('static', path='css/sof-preview-fixes.css') }}">
<style>
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-left: 8px;
        }
        .status-green { background-color: #28a745; }
        .status-red { background-color: #dc3545; }
        .card-orari {
            min-height: 400px;
        }
        .save-button-container {
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 400px;
        }

        /* ===== FIX CONTRASTO TESTI ===== */

        /* Fix testi troppo chiari nelle card */
        .card-body .text-white {
            color: #212529 !important; /* Testo scuro invece di bianco */
            font-weight: 600 !important;
        }

        /* Fix labels e testi nelle card colorate */
        .card-body label,
        .card-body .form-label {
            color: #212529 !important;
            font-weight: 600 !important;
        }

        /* Fix testi piccoli e descrizioni */
        .card-body small,
        .card-body .small {
            color: #495057 !important;
            font-weight: 500 !important;
        }

        /* Fix placeholder nei form */
        .form-control::placeholder {
            color: #6c757d !important;
            opacity: 0.8 !important;
        }

        /* Fix testi nei tab */
        .nav-tabs .nav-link {
            color: #495057 !important;
            font-weight: 600 !important;
        }

        .nav-tabs .nav-link.active {
            color: #212529 !important;
            font-weight: 700 !important;
        }

        /* Fix testi nelle card con gradienti */
        .card[style*="background: linear-gradient"] .card-body,
        .card[style*="background: linear-gradient"] .card-body * {
            color: #212529 !important;
        }

        /* Fix specifico per card con sfondo chiaro */
        .card[style*="background: linear-gradient"] .text-dark {
            color: #212529 !important;
            font-weight: 600 !important;
        }

        /* Fix per input e select nelle card colorate */
        .card[style*="background: linear-gradient"] .form-control,
        .card[style*="background: linear-gradient"] .form-select {
            background-color: rgba(255, 255, 255, 0.9) !important;
            color: #212529 !important;
            border: 2px solid rgba(0, 0, 0, 0.1) !important;
            font-weight: 500 !important;
        }

        /* Fix per i titoli delle card */
        .card-header h6,
        .card-header .card-title {
            color: #212529 !important;
            font-weight: 700 !important;
            text-shadow: none !important;
        }

        /* ===== FIX TABELLE IMPORT/EXPORT ===== */

        /* Fix header tabelle */
        .table thead th {
            background-color: #f8f9fa !important;
            color: #212529 !important;
            font-weight: 700 !important;
            border-bottom: 2px solid #dee2e6 !important;
        }

        /* Fix righe tabelle */
        .table tbody td {
            color: #212529 !important;
            font-weight: 500 !important;
            background-color: rgba(255, 255, 255, 0.95) !important;
        }

        /* Fix righe alternate */
        .table tbody tr:nth-child(even) td {
            background-color: rgba(248, 249, 250, 0.95) !important;
        }

        /* Fix hover righe */
        .table tbody tr:hover td {
            background-color: rgba(0, 123, 255, 0.1) !important;
            color: #212529 !important;
        }

        /* Fix pulsanti nelle tabelle */
        .table .btn {
            font-weight: 600 !important;
        }

        /* Fix badge nelle tabelle */
        .table .badge {
            font-weight: 600 !important;
            font-size: 0.8em !important;
        }

        /* ===== FIX ALERT E MESSAGGI ===== */

        .alert {
            color: #212529 !important;
            font-weight: 500 !important;
        }

        .alert-info {
            background-color: rgba(13, 202, 240, 0.1) !important;
            border-color: rgba(13, 202, 240, 0.2) !important;
            color: #055160 !important;
        }

        .alert-warning {
            background-color: rgba(255, 193, 7, 0.1) !important;
            border-color: rgba(255, 193, 7, 0.2) !important;
            color: #664d03 !important;
        }

        .alert-success {
            background-color: rgba(25, 135, 84, 0.1) !important;
            border-color: rgba(25, 135, 84, 0.2) !important;
            color: #0f5132 !important;
        }

        /* ===== TEMA SCURO - OVERRIDE SPECIFICI ===== */

        body.theme-dark .card-body .text-white,
        body.theme-dark .card-body label,
        body.theme-dark .card-body .form-label {
            color: #ecf0f1 !important; /* Testo chiaro per tema scuro */
            font-weight: 600 !important;
        }

        body.theme-dark .card-body small,
        body.theme-dark .card-body .small {
            color: #bdc3c7 !important; /* Grigio chiaro per tema scuro */
            font-weight: 500 !important;
        }

        /* Fix card con gradienti in tema scuro */
        body.theme-dark .card[style*="background: linear-gradient"] .card-body,
        body.theme-dark .card[style*="background: linear-gradient"] .card-body * {
            color: #ecf0f1 !important;
        }

        body.theme-dark .card[style*="background: linear-gradient"] .text-dark {
            color: #ecf0f1 !important;
            font-weight: 600 !important;
        }

        /* Fix titoli card in tema scuro */
        body.theme-dark .card-header h6,
        body.theme-dark .card-header .card-title {
            color: #ecf0f1 !important;
            font-weight: 700 !important;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.5) !important;
        }

        /* Fix input e form in tema scuro */
        body.theme-dark .card[style*="background: linear-gradient"] .form-control,
        body.theme-dark .card[style*="background: linear-gradient"] .form-select {
            background-color: rgba(44, 62, 80, 0.9) !important;
            color: #ecf0f1 !important;
            border: 2px solid rgba(255, 255, 255, 0.2) !important;
            font-weight: 500 !important;
        }

        body.theme-dark .form-control::placeholder {
            color: #95a5a6 !important;
            opacity: 0.8 !important;
        }

        /* Fix tab navigation in tema scuro */
        body.theme-dark .nav-tabs .nav-link {
            color: #bdc3c7 !important;
            font-weight: 600 !important;
        }

        body.theme-dark .nav-tabs .nav-link.active {
            color: #ecf0f1 !important;
            font-weight: 700 !important;
            background-color: rgba(52, 73, 94, 0.95) !important;
        }

        /* Fix tabelle in tema scuro */
        body.theme-dark .table thead th {
            background-color: rgba(44, 62, 80, 0.95) !important;
            color: #ecf0f1 !important;
            font-weight: 700 !important;
            border-bottom: 2px solid rgba(255, 255, 255, 0.2) !important;
        }

        body.theme-dark .table tbody td {
            color: #ecf0f1 !important;
            font-weight: 500 !important;
            background-color: rgba(52, 73, 94, 0.8) !important;
        }

        body.theme-dark .table tbody tr:nth-child(even) td {
            background-color: rgba(44, 62, 80, 0.8) !important;
        }

        body.theme-dark .table tbody tr:hover td {
            background-color: rgba(52, 152, 219, 0.3) !important;
            color: #ecf0f1 !important;
        }

        /* Fix alert in tema scuro */
        body.theme-dark .alert {
            color: #ecf0f1 !important;
            font-weight: 500 !important;
        }

        body.theme-dark .alert-info {
            background-color: rgba(52, 152, 219, 0.2) !important;
            border-color: rgba(52, 152, 219, 0.4) !important;
            color: #ecf0f1 !important;
        }

        body.theme-dark .alert-warning {
            background-color: rgba(241, 196, 15, 0.2) !important;
            border-color: rgba(241, 196, 15, 0.4) !important;
            color: #ecf0f1 !important;
        }

        body.theme-dark .alert-success {
            background-color: rgba(46, 204, 113, 0.2) !important;
            border-color: rgba(46, 204, 113, 0.4) !important;
            color: #ecf0f1 !important;
        }

        /* ===== FIX SPECIFICO STATISTICHE VIAGGIO TEMA SCURO ===== */

        /* Fix card statistiche con gradiente viola */
        body.theme-dark .card[style*="background: linear-gradient(135deg, #a29bfe"] .card-header,
        body.theme-dark .card[style*="background: linear-gradient(135deg, #a29bfe"] .card-header * {
            color: #ecf0f1 !important;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.7) !important;
        }

        body.theme-dark .card[style*="background: linear-gradient(135deg, #a29bfe"] .card-body,
        body.theme-dark .card[style*="background: linear-gradient(135deg, #a29bfe"] .card-body * {
            color: #ecf0f1 !important;
            font-weight: 600 !important;
        }

        /* Fix specifico per testi nelle statistiche */
        body.theme-dark #sof_statistics_container,
        body.theme-dark #sof_statistics_container * {
            color: #ecf0f1 !important;
        }

        /* Fix per numeri e valori nelle statistiche */
        body.theme-dark #sof_statistics_container .fw-bold,
        body.theme-dark #sof_statistics_container strong {
            color: #ffffff !important;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.5) !important;
        }

        /* Fix per badge nelle statistiche */
        body.theme-dark #sof_statistics_container .badge {
            background-color: rgba(255, 255, 255, 0.2) !important;
            color: #ecf0f1 !important;
            border: 1px solid rgba(255, 255, 255, 0.3) !important;
        }

        /* ===== FIX TAB SOF TEMA SCURO ===== */

        /* Fix scritta "SOF" nel tab */
        body.theme-dark .nav-tabs .nav-link[data-bs-target="#sof"] {
            color: #ecf0f1 !important;
            font-weight: 700 !important;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.5) !important;
        }

        body.theme-dark .nav-tabs .nav-link[data-bs-target="#sof"].active {
            color: #ffffff !important;
            font-weight: 800 !important;
            text-shadow: 1px 1px 3px rgba(0,0,0,0.7) !important;
        }

        /* ===== FIX DATI INTERNI STATISTICHE TEMA SCURO ===== */

        /* Fix per tutti i testi nelle statistiche */
        body.theme-dark #sof_statistics_container p,
        body.theme-dark #sof_statistics_container span,
        body.theme-dark #sof_statistics_container div,
        body.theme-dark #sof_statistics_container small {
            color: #ffffff !important;
            font-weight: 600 !important;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.5) !important;
        }

        /* Fix per valori numerici specifici */
        body.theme-dark #sof_statistics_container .text-center,
        body.theme-dark #sof_statistics_container .text-end,
        body.theme-dark #sof_statistics_container .text-start {
            color: #ffffff !important;
            font-weight: 700 !important;
        }

        /* Fix per liste e elementi */
        body.theme-dark #sof_statistics_container ul,
        body.theme-dark #sof_statistics_container li {
            color: #ffffff !important;
            font-weight: 600 !important;
        }

        /* Fix per tabelle nelle statistiche */
        body.theme-dark #sof_statistics_container table,
        body.theme-dark #sof_statistics_container td,
        body.theme-dark #sof_statistics_container th {
            color: #ffffff !important;
            font-weight: 600 !important;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.5) !important;
        }

        /* Fix per classi Bootstrap comuni */
        body.theme-dark #sof_statistics_container .text-muted {
            color: #ecf0f1 !important;
            font-weight: 600 !important;
        }

        body.theme-dark #sof_statistics_container .text-secondary {
            color: #bdc3c7 !important;
            font-weight: 600 !important;
        }

        /* Fix per elementi con colori specifici */
        body.theme-dark #sof_statistics_container .text-primary,
        body.theme-dark #sof_statistics_container .text-info,
        body.theme-dark #sof_statistics_container .text-success,
        body.theme-dark #sof_statistics_container .text-warning,
        body.theme-dark #sof_statistics_container .text-danger {
            color: #ffffff !important;
            font-weight: 700 !important;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.7) !important;
        }

        /* Effetti hover per le card */
        .card-orari:hover {
            transform: translateY(-5px) !important;
            box-shadow: 0 15px 35px rgba(0,0,0,0.2) !important;
        }

        .card:hover {
            transform: translateY(-5px) !important;
            box-shadow: 0 15px 35px rgba(0,0,0,0.2) !important;
        }

        /* Animazioni per gli input */
        .form-control:focus, .form-select:focus {
            border-color: #80bdff;
            box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
            transform: scale(1.02);
            transition: all 0.3s ease;
        }

        /* Effetti per le label */
        .form-label {
            font-weight: bold;
            color: #495057;
            transition: color 0.3s ease;
        }

        /* Glassmorphism effect */
        .card-header {
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
        }

        /* Testo in grassetto per tutti gli elementi */
        .card-title, .form-label, .btn {
            font-weight: bold !important;
        }

        /* Input text in grassetto */
        .form-control, .form-select {
            font-weight: bold;
        }

        /* Stili per page header simili alla pagina navi */
        .page-header {
            margin-bottom: 2rem;
            padding: 2rem 0;
        }

        .page-header h2 {
            color: #2c3e50;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }

        .subtitle {
            color: #6c757d;
            font-size: 1.1rem;
            margin-bottom: 1rem;
        }

        /* Avatar circles simili alla pagina navi */
        .avatar-circle {
            width: 45px;
            height: 45px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
        }

        /* Card header migliorato */
        .card-header {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-bottom: 2px solid #dee2e6;
        }

        .card-header h5 {
            color: #495057;
            font-weight: 600;
        }

        /* Animazioni per i messaggi moderni */
        @keyframes slideInRight {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        @keyframes fadeOut {
            from {
                transform: translateX(0);
                opacity: 1;
            }
            to {
                transform: translateX(100%);
                opacity: 0;
            }
        }

        .fade-out {
            animation: fadeOut 0.3s ease-out forwards;
        }

        /* Stili per messaggi moderni */
        .alert.position-fixed {
            transform: translateX(0);
            transition: all 0.3s ease;
        }

        .alert.position-fixed:hover {
            transform: translateX(-5px) scale(1.02);
            box-shadow: 0 12px 35px rgba(0,0,0,0.2) !important;
        }

        /* Font grassetto per tutta l'applicazione */
        body, .card, .card-body, .card-header, .btn, .form-control, .form-label,
        .table, .nav-link, .modal-body, .modal-header, .modal-footer,
        .alert, .badge, .dropdown-menu, .navbar, .breadcrumb,
        input, select, textarea, label, p, div, span, h1, h2, h3, h4, h5, h6 {
            font-weight: bold !important;
        }

        /* ===== FIX ANTEPRIMA SOF TEMA MARITTIMO - OVERRIDE DIRETTO ===== */

        /* Stile base anteprima SOF */
        .sof-preview-container {
            font-family: 'Courier New', monospace !important;
            font-size: 0.85em !important;
            line-height: 1.4 !important;
            white-space: pre-wrap !important;
            word-wrap: break-word !important;
            max-height: 400px !important;
            overflow-y: auto !important;
            padding: 1rem !important;
            border-radius: 0.375rem !important;
        }

        /* Tema marittimo - OVERRIDE FORZATO */
        body.theme-maritime .sof-preview-container,
        body.theme-maritime #sof_preview_container .sof-preview-container,
        body.theme-maritime div.sof-preview-container {
            background-color: #ffffff !important;
            background: #ffffff !important;
            color: #212529 !important;
            border: 3px solid #ffd700 !important;
            box-shadow: 0 6px 12px rgba(0, 0, 0, 0.3) !important;
        }

        /* Tutti i testi dentro l'anteprima SOF in tema marittimo */
        body.theme-maritime .sof-preview-container *,
        body.theme-maritime .sof-preview-container div,
        body.theme-maritime .sof-preview-container span,
        body.theme-maritime .sof-preview-container p {
            color: #212529 !important;
            background: transparent !important;
        }

        /* Placeholder anteprima SOF */
        .sof-preview-placeholder {
            text-align: center !important;
            padding: 2rem !important;
        }

        body.theme-maritime .sof-preview-placeholder {
            color: #212529 !important;
            background: #ffffff !important;
            border: 3px solid #ffd700 !important;
            border-radius: 0.375rem !important;
        }

        /* OVERRIDE FINALE ULTRA-AGGRESSIVO PER ANTEPRIMA SOF */
        body.theme-maritime #sof_preview_container,
        body.theme-maritime #sof_preview_container *,
        body.theme-maritime #sof_preview_container div,
        body.theme-maritime #sof_preview_container .sof-preview-container,
        body.theme-maritime #sof_preview_container .sof-preview-content {
            background-color: #ffffff !important;
            background: #ffffff !important;
            color: #212529 !important;
        }

        /* Forza bordo oro per anteprima SOF */
        body.theme-maritime #sof_preview_container .sof-preview-container,
        body.theme-maritime #sof_preview_container .sof-preview-content {
            border: 3px solid #ffd700 !important;
            box-shadow: 0 6px 12px rgba(0, 0, 0, 0.3) !important;
        }

        /* Override ultra-specifico per il contenuto SOF generato dinamicamente */
        body.theme-maritime .sof-preview-content,
        body.theme-maritime div.sof-preview-content,
        body.theme-maritime #sof_preview_container .sof-preview-content {
            background-color: #ffffff !important;
            background: #ffffff !important;
            color: #212529 !important;
            border: 3px solid #ffd700 !important;
            box-shadow: 0 6px 12px rgba(0, 0, 0, 0.3) !important;
        }

        /* Forza il colore del testo per tutti gli elementi figli */
        body.theme-maritime .sof-preview-content > div,
        body.theme-maritime .sof-preview-content > span,
        body.theme-maritime .sof-preview-content > p,
        body.theme-maritime .sof-preview-content * {
            color: #212529 !important;
            background: transparent !important;
        }

        /* Font extra grassetto per elementi importanti */
        .card-title, .modal-title, .btn, .nav-link.active,
        .table th, .form-label, .alert, .badge {
            font-weight: 900 !important;
        }

        /* Font grassetto per testo specifico */
        .text-muted, .small, .form-text {
            font-weight: 600 !important;
        }
</style>
{% endblock %}

{% block content %}

        <!-- Informazioni Viaggio -->
        <div class="card mb-4">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <div class="d-flex align-items-center">
                            <h5 class="mb-0 me-3"><i class="fas fa-info-circle me-2"></i>Informazioni Viaggio</h5>
                            {% if viaggio.visibile == 'no' %}
                                <span class="badge bg-success fs-6">
                                    <i class="fas fa-check-circle me-1"></i>SOF COMPLETATO
                                </span>
                            {% else %}
                                <span class="badge bg-warning fs-6">
                                    <i class="fas fa-clock me-1"></i>SOF DA COMPLETARE
                                </span>
                            {% endif %}
                        </div>
                        <small class="text-muted">Dettagli principali del viaggio {{ viaggio.viaggio }}</small>
                    </div>
                    <div>
                        {% if viaggio.visibile == 'no' %}
                            <a href="/operativo/sof/realizzati" class="btn shadow-lg border-0"
                               style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; border-radius: 25px; padding: 10px 20px; transition: all 0.3s ease; transform: scale(1);"
                               onmouseover="this.style.transform='scale(1.05)'; this.style.boxShadow='0 8px 25px rgba(40,167,69,0.4)';"
                               onmouseout="this.style.transform='scale(1)'; this.style.boxShadow='0 4px 15px rgba(0,0,0,0.2)';">
                                <span style="font-size: 1.1em; margin-right: 8px;">🔙</span>
                                <span style="font-weight: bold;">Torna ai SOF Realizzati</span>
                            </a>
                        {% else %}
                            <a href="/operativo/sof/da-realizzare?updated={{ viaggio.id }}" class="btn shadow-lg border-0"
                               style="background: linear-gradient(135deg, #6c5ce7 0%, #a29bfe 100%); color: white; border-radius: 25px; padding: 10px 20px; transition: all 0.3s ease; transform: scale(1);"
                               onmouseover="this.style.transform='scale(1.05)'; this.style.boxShadow='0 8px 25px rgba(108,92,231,0.4)';"
                               onmouseout="this.style.transform='scale(1)'; this.style.boxShadow='0 4px 15px rgba(0,0,0,0.2)';">
                                <span style="font-size: 1.1em; margin-right: 8px;">🔙</span>
                                <span style="font-weight: bold;">Torna alla Lista SOF</span>
                            </a>
                        {% endif %}
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <div class="d-flex align-items-center mb-3">
                            <div class="avatar-circle me-3 bg-primary">
                                <i class="fas fa-ship text-white"></i>
                            </div>
                            <div>
                                <h6 class="text-muted mb-1">Nome Nave</h6>
                                <p class="h5 mb-0 fw-bold">{{ viaggio.nome_nave }}</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="d-flex align-items-center mb-3">
                            <div class="avatar-circle me-3 bg-info">
                                <i class="fas fa-barcode text-white"></i>
                            </div>
                            <div>
                                <h6 class="text-muted mb-1">Codice Viaggio</h6>
                                <p class="h5 mb-0 fw-bold text-primary">{{ viaggio.viaggio }}</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="d-flex align-items-center mb-3 position-relative">
                            <div class="avatar-circle me-3 bg-success">
                                <i class="fas fa-calendar-alt text-white"></i>
                            </div>
                            <div class="flex-grow-1">
                                <h6 class="text-muted mb-1">Data Arrivo</h6>

                                <!-- Visualizzazione normale (SOLO LETTURA) -->
                                <p class="h5 mb-0 fw-bold">
                                    {% if viaggio.data_arrivo %}
                                        {% set date_parts = viaggio.data_arrivo.split('-') %}
                                        {{ date_parts[2] }}-{{ date_parts[1] }}-{{ date_parts[0] }}
                                    {% else %}
                                        <span class="text-muted">N/A</span>
                                    {% endif %}
                                </p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="d-flex align-items-center mb-3 position-relative">
                            <div class="avatar-circle me-3 bg-warning">
                                <i class="fas fa-calendar-check text-white"></i>
                            </div>
                            <div class="flex-grow-1">
                                <h6 class="text-muted mb-1">Data Partenza</h6>

                                <!-- Visualizzazione normale (SOLO LETTURA) -->
                                <p class="h5 mb-0 fw-bold">
                                    {% if viaggio.data_partenza %}
                                        {% set date_parts = viaggio.data_partenza.split('-') %}
                                        {{ date_parts[2] }}-{{ date_parts[1] }}-{{ date_parts[0] }}
                                    {% else %}
                                        <span class="text-muted">N/A</span>
                                    {% endif %}
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Tab Navigation -->
        <div class="row mb-4">
            <div class="col">
                <ul class="nav nav-tabs" id="viaggioTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="orari-tab" data-bs-toggle="tab" data-bs-target="#orari" type="button" role="tab">
                            <span style="font-size: 1.1em; margin-right: 8px;">🕒</span>
                            ORARI
                            <span class="status-indicator" id="orari-status"></span>
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="import-tab" data-bs-toggle="tab" data-bs-target="#import" type="button" role="tab"
                                style="font-weight: bold; transition: all 0.3s ease;">
                            <span style="font-size: 1.1em; margin-right: 8px;">📥</span>
                            IMPORT
                            <span class="status-indicator" id="import-status"></span>
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="export-tab" data-bs-toggle="tab" data-bs-target="#export" type="button" role="tab"
                                style="font-weight: bold; transition: all 0.3s ease;">
                            <span style="font-size: 1.1em; margin-right: 8px;">📤</span>
                            EXPORT
                            <span class="status-indicator" id="export-status"></span>
                        </button>
                    </li>
                    <li class="nav-item" role="presentation" id="sof-tab-container" style="display: none;">
                        <button class="nav-link" id="sof-tab" data-bs-toggle="tab" data-bs-target="#sof" type="button" role="tab"
                                style="font-weight: bold; transition: all 0.3s ease; background: linear-gradient(135deg, #ffd700 0%, #ffb347 100%); color: #333;">
                            <span style="font-size: 1.1em; margin-right: 8px;">📋</span>
                            SOF
                            <span class="status-indicator status-green" id="sof-status"></span>
                        </button>
                    </li>
                </ul>
            </div>
        </div>

        <!-- Tab Content -->
        <div class="tab-content" id="viaggioTabsContent">
            <div class="tab-pane fade show active" id="orari" role="tabpanel">
                <form id="orariForm">
                    <div class="row">
                        <!-- Card Orari di Arrivo -->
                        <div class="col-md-3 me-4">
                            <div class="card card-orari shadow-lg border-0" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%); transition: all 0.3s ease;">
                                <div class="card-header text-white py-2 border-0" style="background: rgba(255,255,255,0.1); backdrop-filter: blur(10px);">
                                    <div class="d-flex align-items-center">
                                        <div class="me-2" style="font-size: 1.2em;">⚓</div>
                                        <h6 class="card-title mb-0 small fw-bold">
                                            ARRIVO
                                        </h6>
                                    </div>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <label for="porto_arrivo" class="form-label">Porto Arrivo</label>
                                        <select class="form-select" id="porto_arrivo" name="porto_arrivo">
                                            <option value="">Seleziona porto...</option>
                                        </select>
                                    </div>
                                    <div class="mb-3">
                                        <label for="sbe" class="form-label">SBE</label>
                                        <input type="datetime-local" class="form-control" id="sbe" name="sbe"
                                               value="{{ orari.sbe if orari and orari.sbe else '' }}">
                                    </div>
                                    <div class="mb-3">
                                        <label for="pilota_arrivo" class="form-label">Pilota Arrivo</label>
                                        <input type="datetime-local" class="form-control" id="pilota_arrivo" name="pilota_arrivo"
                                               value="{{ orari.pilota_arrivo if orari and orari.pilota_arrivo else '' }}">
                                    </div>
                                    <div class="mb-3">
                                        <label for="all_fast" class="form-label">All Fast</label>
                                        <input type="datetime-local" class="form-control" id="all_fast" name="all_fast"
                                               value="{{ orari.all_fast if orari and orari.all_fast else '' }}">
                                    </div>
                                    <div class="mb-3">
                                        <label for="tug_arrivo" class="form-label">Tug Arrivo</label>
                                        <input type="number" class="form-control" id="tug_arrivo" name="tug_arrivo" min="0"
                                               value="{{ orari.tug_arrivo if orari and orari.tug_arrivo is not none else '0' }}">
                                    </div>
                                    <div class="mb-3">
                                        <label for="draft" class="form-label">Draft</label>
                                        <input type="number" step="0.01" class="form-control" id="draft" name="draft"
                                               value="{{ orari.draft if orari and orari.draft is not none else '' }}">
                                    </div>
                                    <div class="mb-3">
                                        <label for="soc" class="form-label">SOC</label>
                                        <input type="datetime-local" class="form-control" id="soc" name="soc"
                                               value="{{ orari.soc if orari and orari.soc else '' }}">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Card Orari di Partenza -->
                        <div class="col-md-3 me-4">
                            <div class="card card-orari shadow-lg border-0" style="background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%); transition: all 0.3s ease;">
                                <div class="card-header text-dark py-2 border-0" style="background: rgba(255,255,255,0.2); backdrop-filter: blur(10px);">
                                    <div class="d-flex align-items-center">
                                        <div class="me-2" style="font-size: 1.2em;">🚢</div>
                                        <h6 class="card-title mb-0 small fw-bold">
                                            PARTENZA
                                        </h6>
                                    </div>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <label for="porto_di_destinazione" class="form-label">Porto di Destinazione</label>
                                        <select class="form-select" id="porto_di_destinazione" name="porto_di_destinazione">
                                            <option value="">Seleziona porto...</option>
                                        </select>
                                    </div>
                                    <div class="mb-3">
                                        <label for="pilota_partenza" class="form-label">Pilota Partenza</label>
                                        <input type="datetime-local" class="form-control" id="pilota_partenza" name="pilota_partenza"
                                               value="{{ orari.pilota_partenza if orari and orari.pilota_partenza else '' }}">
                                    </div>
                                    <div class="mb-3">
                                        <label for="tug_partenza" class="form-label">Tug Partenza</label>
                                        <input type="number" class="form-control" id="tug_partenza" name="tug_partenza" min="0"
                                               value="{{ orari.tug_partenza if orari and orari.tug_partenza is not none else '0' }}">
                                    </div>
                                    <div class="mb-3">
                                        <label for="foc" class="form-label">FOC</label>
                                        <input type="datetime-local" class="form-control" id="foc" name="foc"
                                               value="{{ orari.foc if orari and orari.foc else '' }}">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Card Carburanti a destra degli Orari di Partenza -->
                        <div class="col-md-2 me-4">
                            <div class="card shadow-lg border-0" style="background: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%); transition: all 0.3s ease;">
                                <div class="card-header text-white py-2 border-0" style="background: rgba(255,255,255,0.1); backdrop-filter: blur(10px);">
                                    <div class="d-flex align-items-center">
                                        <div class="me-2" style="font-size: 1.2em;">⛽</div>
                                        <h6 class="card-title mb-0 small fw-bold">
                                            CARBURANTI
                                        </h6>
                                    </div>
                                </div>
                                <div class="card-body py-2">
                                    <div class="mb-2">
                                        <label for="fo" class="form-label small">FO</label>
                                        <input type="number" step="0.01" class="form-control form-control-sm" id="fo" name="fo"
                                               value="{{ orari.fo if orari and orari.fo is not none else '0.00' }}"
                                               placeholder="0.00">
                                    </div>
                                    <div class="mb-2">
                                        <label for="do" class="form-label small">DO</label>
                                        <input type="number" step="0.01" class="form-control form-control-sm" id="do" name="do"
                                               value="{{ orari.do if orari and orari.do is not none else '0.00' }}"
                                               placeholder="0.00">
                                    </div>
                                    <div class="mb-2">
                                        <label for="lo" class="form-label small">LO</label>
                                        <input type="number" step="0.01" class="form-control form-control-sm" id="lo" name="lo"
                                               value="{{ orari.lo if orari and orari.lo is not none else '0.00' }}"
                                               placeholder="0.00">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Pulsante Salva a destra della Card Carburanti -->
                        <div class="col-md-1">
                            <div class="save-button-container">
                                <button type="submit" class="btn btn-lg shadow-lg border-0" style="background: linear-gradient(135deg, #007bff 0%, #0056b3 100%); color: white; border-radius: 15px; transition: all 0.3s ease; transform: scale(1);"
                                        onmouseover="this.style.transform='scale(1.05)'; this.style.boxShadow='0 8px 25px rgba(0,123,255,0.4)';"
                                        onmouseout="this.style.transform='scale(1)'; this.style.boxShadow='0 4px 15px rgba(0,0,0,0.2)';">
                                    <div style="font-size: 1.5em; margin-bottom: 5px;">💾</div>
                                    <div style="font-size: 0.8em; font-weight: bold;">SALVA</div>
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>

            <!-- Tab IMPORT -->
            <div class="tab-pane fade" id="import" role="tabpanel">
                <div class="row">
                    <!-- Card Upload File -->
                    <div class="col-md-4 me-4">
                        <div class="card shadow-lg border-0" style="background: linear-gradient(135deg, #6c5ce7 0%, #a29bfe 100%); transition: all 0.3s ease;">
                            <div class="card-header text-white py-2 border-0" style="background: rgba(255,255,255,0.1); backdrop-filter: blur(10px);">
                                <div class="d-flex align-items-center">
                                    <div class="me-2" style="font-size: 1.2em;">📁</div>
                                    <h6 class="card-title mb-0 small fw-bold">UPLOAD FILE</h6>
                                </div>
                            </div>
                            <div class="card-body">
                                <form id="importForm" enctype="multipart/form-data">
                                    <div class="mb-3">
                                        <label for="file_upload" class="form-label">Seleziona File</label>
                                        <input type="file" class="form-control" id="file_upload" name="file_upload"
                                               accept=".xlsx,.xls,.csv,application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,text/csv" required>
                                        <div class="form-text">Formati supportati: Excel (.xlsx, .xls), CSV</div>
                                    </div>
                                    <button type="submit" class="btn btn-light fw-bold w-100"
                                            style="border-radius: 10px; transition: all 0.3s ease;"
                                            onmouseover="this.style.transform='scale(1.05)'; this.style.boxShadow='0 5px 15px rgba(255,255,255,0.3)';"
                                            onmouseout="this.style.transform='scale(1)'; this.style.boxShadow='none';">
                                        <span style="font-size: 1.1em; margin-right: 8px;">⬆️</span>
                                        CARICA FILE
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>

                    <!-- Card Anteprima Dati -->
                    <div class="col-md-4 me-4">
                        <div class="card shadow-lg border-0" style="background: linear-gradient(135deg, #00b894 0%, #00cec9 100%); transition: all 0.3s ease;">
                            <div class="card-header text-white py-2 border-0" style="background: rgba(255,255,255,0.1); backdrop-filter: blur(10px);">
                                <div class="d-flex align-items-center">
                                    <div class="me-2" style="font-size: 1.2em;">👁️</div>
                                    <h6 class="card-title mb-0 small fw-bold">ANTEPRIMA DATI</h6>
                                </div>
                            </div>
                            <div class="card-body">
                                <div id="preview_container" style="max-height: 300px; overflow-y: auto;">
                                    <div class="text-center text-muted py-4">
                                        <div style="font-size: 2em; margin-bottom: 10px;">📋</div>
                                        <p class="mb-0">Carica un file per vedere l'anteprima</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Card Azioni Import -->
                    <div class="col-md-3">
                        <div class="card shadow-lg border-0" style="background: linear-gradient(135deg, #e17055 0%, #fdcb6e 100%); transition: all 0.3s ease;">
                            <div class="card-header text-white py-2 border-0" style="background: rgba(255,255,255,0.1); backdrop-filter: blur(10px);">
                                <div class="d-flex align-items-center">
                                    <div class="me-2" style="font-size: 1.2em;">⚡</div>
                                    <h6 class="card-title mb-0 small fw-bold">AZIONI</h6>
                                </div>
                            </div>
                            <div class="card-body">
                                <button type="button" class="btn btn-light fw-bold w-100 mb-3" id="validate_btn" disabled
                                        style="border-radius: 10px; transition: all 0.3s ease;"
                                        onmouseover="if(!this.disabled) { this.style.transform='scale(1.05)'; this.style.boxShadow='0 5px 15px rgba(255,255,255,0.3)'; }"
                                        onmouseout="this.style.transform='scale(1)'; this.style.boxShadow='none';">
                                    <span style="font-size: 1.1em; margin-right: 8px;">✅</span>
                                    VALIDA DATI
                                </button>

                                <button type="button" class="btn btn-light fw-bold w-100 mb-3" id="import_btn" disabled
                                        style="border-radius: 10px; transition: all 0.3s ease;"
                                        onmouseover="if(!this.disabled) { this.style.transform='scale(1.05)'; this.style.boxShadow='0 5px 15px rgba(255,255,255,0.3)'; }"
                                        onmouseout="this.style.transform='scale(1)'; this.style.boxShadow='none';">
                                    <span style="font-size: 1.1em; margin-right: 8px;">💾</span>
                                    IMPORTA DATI
                                </button>

                                <button type="button" class="btn btn-danger fw-bold w-100 mb-3" id="delete_import_btn"
                                        style="border-radius: 10px; transition: all 0.3s ease; background: linear-gradient(135deg, #dc3545 0%, #c82333 100%); border: none;"
                                        onmouseover="this.style.transform='scale(1.05)'; this.style.boxShadow='0 5px 15px rgba(220,53,69,0.4)';"
                                        onmouseout="this.style.transform='scale(1)'; this.style.boxShadow='none';">
                                    <span style="font-size: 1.1em; margin-right: 8px;">🗑️</span>
                                    ELIMINA DATI
                                </button>

                                <div class="mt-3">
                                    <small class="text-white">
                                        <div class="mb-1"><strong>Stato:</strong> <span id="import_status_text">In attesa</span></div>
                                        <div><strong>Righe:</strong> <span id="rows_count">0</span></div>
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Sezione Dati IMPORT Importati -->
                <div class="row mt-4">
                    <div class="col-md-8">
                        <div class="card shadow-lg border-0" style="background: linear-gradient(135deg, #00b894 0%, #00cec9 100%);">
                            <div class="card-header text-white py-2 border-0" style="background: rgba(255,255,255,0.1); backdrop-filter: blur(10px);">
                                <div class="d-flex align-items-center justify-content-between">
                                    <div class="d-flex align-items-center">
                                        <div class="me-2" style="font-size: 1.2em;">📦</div>
                                        <h6 class="card-title mb-0 small fw-bold">DATI IMPORT</h6>
                                    </div>
                                    <button type="button" class="btn btn-light btn-sm" id="refresh_import_btn"
                                            style="border-radius: 8px; font-size: 0.8em;"
                                            onclick="loadImportData()">
                                        🔄 AGGIORNA
                                    </button>
                                </div>
                            </div>
                            <div class="card-body">
                                <div id="import_data_container" style="max-height: 300px; overflow-y: auto;">
                                    <div class="text-center text-muted py-4">
                                        <div style="font-size: 2em; margin-bottom: 10px;">📋</div>
                                        <p class="mb-0">Nessun dato IMPORT presente</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-4">
                        <div class="card shadow-lg border-0" style="background: linear-gradient(135deg, #636e72 0%, #b2bec3 100%);">
                            <div class="card-header text-white py-2 border-0" style="background: rgba(255,255,255,0.1); backdrop-filter: blur(10px);">
                                <div class="d-flex align-items-center">
                                    <div class="me-2" style="font-size: 1.2em;">📝</div>
                                    <h6 class="card-title mb-0 small fw-bold">LOG IMPORT</h6>
                                </div>
                            </div>
                            <div class="card-body">
                                <div id="import_log" style="max-height: 300px; overflow-y: auto; background: rgba(255,255,255,0.1); border-radius: 8px; padding: 15px;">
                                    <div class="text-muted">
                                        <small>I log delle operazioni di import appariranno qui...</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Tab EXPORT -->
            <div class="tab-pane fade" id="export" role="tabpanel">
                <div class="row">
                    <!-- Card Upload File -->
                    <div class="col-md-4 me-4">
                        <div class="card shadow-lg border-0" style="background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%); transition: all 0.3s ease;">
                            <div class="card-header text-white py-2 border-0" style="background: rgba(255,255,255,0.1); backdrop-filter: blur(10px);">
                                <div class="d-flex align-items-center">
                                    <div class="me-2" style="font-size: 1.2em;">📁</div>
                                    <h6 class="card-title mb-0 small fw-bold">UPLOAD FILE</h6>
                                </div>
                            </div>
                            <div class="card-body">
                                <form id="exportForm" enctype="multipart/form-data">
                                    <div class="mb-3">
                                        <label for="file_upload_export" class="form-label">Seleziona File</label>
                                        <input type="file" class="form-control" id="file_upload_export" name="file_upload_export"
                                               accept=".xlsx,.xls,.csv,application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,text/csv" required>
                                        <div class="form-text">Formati supportati: Excel (.xlsx, .xls), CSV</div>
                                    </div>
                                    <button type="submit" class="btn btn-light fw-bold w-100"
                                            style="border-radius: 10px; transition: all 0.3s ease;"
                                            onmouseover="this.style.transform='scale(1.05)'; this.style.boxShadow='0 5px 15px rgba(255,255,255,0.3)';"
                                            onmouseout="this.style.transform='scale(1)'; this.style.boxShadow='none';">
                                        <span style="font-size: 1.1em; margin-right: 8px;">⬆️</span>
                                        CARICA FILE
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>

                    <!-- Card Anteprima Dati -->
                    <div class="col-md-4 me-4">
                        <div class="card shadow-lg border-0" style="background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%); transition: all 0.3s ease;">
                            <div class="card-header text-white py-2 border-0" style="background: rgba(255,255,255,0.1); backdrop-filter: blur(10px);">
                                <div class="d-flex align-items-center">
                                    <div class="me-2" style="font-size: 1.2em;">👁️</div>
                                    <h6 class="card-title mb-0 small fw-bold">ANTEPRIMA DATI</h6>
                                </div>
                            </div>
                            <div class="card-body">
                                <div id="preview_container_export" style="max-height: 300px; overflow-y: auto;">
                                    <div class="text-center text-muted py-4">
                                        <div style="font-size: 2em; margin-bottom: 10px;">📋</div>
                                        <p class="mb-0">Carica un file per vedere l'anteprima</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Card Azioni Export -->
                    <div class="col-md-3">
                        <div class="card shadow-lg border-0" style="background: linear-gradient(135deg, #9b59b6 0%, #8e44ad 100%); transition: all 0.3s ease;">
                            <div class="card-header text-white py-2 border-0" style="background: rgba(255,255,255,0.1); backdrop-filter: blur(10px);">
                                <div class="d-flex align-items-center">
                                    <div class="me-2" style="font-size: 1.2em;">⚡</div>
                                    <h6 class="card-title mb-0 small fw-bold">AZIONI</h6>
                                </div>
                            </div>
                            <div class="card-body">
                                <button type="button" class="btn btn-light fw-bold w-100 mb-3" id="validate_btn_export" disabled
                                        style="border-radius: 10px; transition: all 0.3s ease;"
                                        onmouseover="if(!this.disabled) { this.style.transform='scale(1.05)'; this.style.boxShadow='0 5px 15px rgba(255,255,255,0.3)'; }"
                                        onmouseout="this.style.transform='scale(1)'; this.style.boxShadow='none';">
                                    <span style="font-size: 1.1em; margin-right: 8px;">✅</span>
                                    VALIDA DATI
                                </button>

                                <button type="button" class="btn btn-light fw-bold w-100 mb-3" id="export_btn" disabled
                                        style="border-radius: 10px; transition: all 0.3s ease;"
                                        onmouseover="if(!this.disabled) { this.style.transform='scale(1.05)'; this.style.boxShadow='0 5px 15px rgba(255,255,255,0.3)'; }"
                                        onmouseout="this.style.transform='scale(1)'; this.style.boxShadow='none';">
                                    <span style="font-size: 1.1em; margin-right: 8px;">💾</span>
                                    ESPORTA DATI
                                </button>

                                <button type="button" class="btn btn-danger fw-bold w-100 mb-3" id="delete_export_btn"
                                        style="border-radius: 10px; transition: all 0.3s ease; background: linear-gradient(135deg, #dc3545 0%, #c82333 100%); border: none;"
                                        onmouseover="this.style.transform='scale(1.05)'; this.style.boxShadow='0 5px 15px rgba(220,53,69,0.4)';"
                                        onmouseout="this.style.transform='scale(1)'; this.style.boxShadow='none';">
                                    <span style="font-size: 1.1em; margin-right: 8px;">🗑️</span>
                                    ELIMINA DATI
                                </button>

                                <div class="mt-3">
                                    <small class="text-white">
                                        <div class="mb-1"><strong>Stato:</strong> <span id="export_status_text">In attesa</span></div>
                                        <div><strong>Righe:</strong> <span id="rows_count_export">0</span></div>
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Sezione Dati EXPORT Esportati -->
                <div class="row mt-4">
                    <div class="col-md-8">
                        <div class="card shadow-lg border-0" style="background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);">
                            <div class="card-header text-white py-2 border-0" style="background: rgba(255,255,255,0.1); backdrop-filter: blur(10px);">
                                <div class="d-flex align-items-center justify-content-between">
                                    <div class="d-flex align-items-center">
                                        <div class="me-2" style="font-size: 1.2em;">📦</div>
                                        <h6 class="card-title mb-0 small fw-bold">DATI EXPORT</h6>
                                    </div>
                                    <button type="button" class="btn btn-light btn-sm" id="refresh_export_btn"
                                            style="border-radius: 8px; font-size: 0.8em;"
                                            onclick="loadExportData()">
                                        🔄 AGGIORNA
                                    </button>
                                </div>
                            </div>
                            <div class="card-body">
                                <div id="export_data_container" style="max-height: 300px; overflow-y: auto;">
                                    <div class="text-center text-muted py-4">
                                        <div style="font-size: 2em; margin-bottom: 10px;">📋</div>
                                        <p class="mb-0">Nessun dato EXPORT presente</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-4">
                        <div class="card shadow-lg border-0" style="background: linear-gradient(135deg, #636e72 0%, #b2bec3 100%);">
                            <div class="card-header text-white py-2 border-0" style="background: rgba(255,255,255,0.1); backdrop-filter: blur(10px);">
                                <div class="d-flex align-items-center">
                                    <div class="me-2" style="font-size: 1.2em;">📝</div>
                                    <h6 class="card-title mb-0 small fw-bold">LOG EXPORT</h6>
                                </div>
                            </div>
                            <div class="card-body">
                                <div id="export_log" style="max-height: 300px; overflow-y: auto; background: rgba(255,255,255,0.1); border-radius: 8px; padding: 15px;">
                                    <div class="text-muted">
                                        <small>I log delle operazioni di export appariranno qui...</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Tab SOF -->
            <div class="tab-pane fade" id="sof" role="tabpanel">
                <div class="row">
                    <!-- Card Generazione SOF -->
                    <div class="col-md-6 me-4">
                        <div class="card shadow-lg border-0" style="background: linear-gradient(135deg, #ffd700 0%, #ffb347 100%); transition: all 0.3s ease;">
                            <div class="card-header text-dark py-2 border-0" style="background: rgba(255,255,255,0.2); backdrop-filter: blur(10px);">
                                <div class="d-flex align-items-center">
                                    <div class="me-2" style="font-size: 1.2em;">📋</div>
                                    <h6 class="card-title mb-0 small fw-bold">GENERAZIONE SOF</h6>
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <p class="text-dark mb-3">
                                        <strong>Statement of Facts (SOF)</strong><br>
                                        Documento che riassume tutti i dati del viaggio:
                                    </p>
                                    <ul class="text-dark small">
                                        <li>✅ Orari di arrivo e partenza</li>
                                        <li>✅ Dati IMPORT caricati</li>
                                        <li>✅ Dati EXPORT caricati</li>
                                        <li>📊 Statistiche complete</li>
                                        <li>📄 Report dettagliato</li>
                                    </ul>
                                </div>

                                {% if viaggio.visibile == 'si' %}
                                    <!-- Viaggio visibile: mostra pulsante genera -->
                                    <button type="button" class="btn btn-dark fw-bold w-100 mb-3" id="generate_sof_btn"
                                            style="border-radius: 10px; transition: all 0.3s ease;"
                                            onmouseover="this.style.transform='scale(1.05)'; this.style.boxShadow='0 5px 15px rgba(0,0,0,0.3)';"
                                            onmouseout="this.style.transform='scale(1)'; this.style.boxShadow='none';">
                                        <span style="font-size: 1.1em; margin-right: 8px;">🔄</span>
                                        GENERA SOF
                                    </button>

                                    <button type="button" class="btn btn-success fw-bold w-100 mb-3" id="download_sof_btn" disabled
                                            style="border-radius: 10px; transition: all 0.3s ease;"
                                            onmouseover="if(!this.disabled) { this.style.transform='scale(1.05)'; this.style.boxShadow='0 5px 15px rgba(40,167,69,0.4)'; }"
                                            onmouseout="this.style.transform='scale(1)'; this.style.boxShadow='none';">
                                        <span style="font-size: 1.1em; margin-right: 8px;">📥</span>
                                        SCARICA SOF
                                    </button>
                                {% else %}
                                    <!-- Viaggio non visibile (SOF completato): mostra solo pulsante download -->
                                    <div class="alert alert-success mb-3" style="border-radius: 10px;">
                                        <div class="d-flex align-items-center">
                                            <span style="font-size: 1.5em; margin-right: 10px;">✅</span>
                                            <div>
                                                <strong>SOF Completato</strong><br>
                                                <small>Il SOF per questo viaggio è già stato generato e può essere scaricato.</small>
                                            </div>
                                        </div>
                                    </div>

                                    <button type="button" class="btn btn-success fw-bold w-100 mb-3" id="download_completed_sof_btn"
                                            style="border-radius: 10px; transition: all 0.3s ease;"
                                            onmouseover="this.style.transform='scale(1.05)'; this.style.boxShadow='0 5px 15px rgba(40,167,69,0.4)';"
                                            onmouseout="this.style.transform='scale(1)'; this.style.boxShadow='none';"
                                            onclick="console.log('🖱️ Pulsante cliccato direttamente!'); testDirectDownloadSOF();">
                                        <span style="font-size: 1.1em; margin-right: 8px;">📥</span>
                                        SCARICA SOF COMPLETATO
                                    </button>
                                {% endif %}

                                <div class="mt-3">
                                    <small class="text-dark">
                                        <div class="mb-1"><strong>Stato:</strong> <span id="sof_status_text">Pronto per generazione</span></div>
                                        <div><strong>Ultimo aggiornamento:</strong> <span id="sof_last_update">-</span></div>
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Card Anteprima SOF -->
                    <div class="col-md-5">
                        <div class="card shadow-lg border-0" style="background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);">
                            <div class="card-header text-white py-2 border-0" style="background: rgba(255,255,255,0.1); backdrop-filter: blur(10px);">
                                <div class="d-flex align-items-center">
                                    <div class="me-2" style="font-size: 1.2em;">👁️</div>
                                    <h6 class="card-title mb-0 small fw-bold">ANTEPRIMA SOF</h6>
                                </div>
                            </div>
                            <div class="card-body">
                                <div id="sof_preview_container">
                                    <div class="sof-preview-placeholder">
                                        <div style="font-size: 2em; margin-bottom: 10px;">📋</div>
                                        <p class="mb-0">Genera il SOF per vedere l'anteprima</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Sezione Statistiche SOF -->
                <div class="row mt-4">
                    <div class="col-md-12">
                        <div class="card shadow-lg border-0" style="background: linear-gradient(135deg, #a29bfe 0%, #6c5ce7 100%);">
                            <div class="card-header text-white py-2 border-0" style="background: rgba(255,255,255,0.1); backdrop-filter: blur(10px);">
                                <div class="d-flex align-items-center">
                                    <div class="me-2" style="font-size: 1.2em;">📊</div>
                                    <h6 class="card-title mb-0 small fw-bold">STATISTICHE VIAGGIO</h6>
                                </div>
                            </div>
                            <div class="card-body">
                                <div id="sof_statistics_container">
                                    <div class="row g-3">
                                        <div class="col-md-3">
                                            <div class="text-center p-4 rounded-3 shadow-sm"
                                                 style="background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
                                                        border: 2px solid rgba(255,255,255,0.2);
                                                        transition: all 0.3s ease;"
                                                 onmouseover="this.style.transform='translateY(-5px)'; this.style.boxShadow='0 10px 25px rgba(116,185,255,0.4)';"
                                                 onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 4px 15px rgba(0,0,0,0.1)';">
                                                <div style="font-size: 2em; margin-bottom: 8px;">📥</div>
                                                <h3 class="text-white mb-2 fw-bold" id="stat_import_records" style="text-shadow: 1px 1px 2px rgba(0,0,0,0.3);">-</h3>
                                                <small class="text-white fw-semibold" style="text-shadow: 1px 1px 2px rgba(0,0,0,0.3);">Record IMPORT</small>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="text-center p-4 rounded-3 shadow-sm"
                                                 style="background: linear-gradient(135deg, #00b894 0%, #00a085 100%);
                                                        border: 2px solid rgba(255,255,255,0.2);
                                                        transition: all 0.3s ease;"
                                                 onmouseover="this.style.transform='translateY(-5px)'; this.style.boxShadow='0 10px 25px rgba(0,184,148,0.4)';"
                                                 onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 4px 15px rgba(0,0,0,0.1)';">
                                                <div style="font-size: 2em; margin-bottom: 8px;">📤</div>
                                                <h3 class="text-white mb-2 fw-bold" id="stat_export_records" style="text-shadow: 1px 1px 2px rgba(0,0,0,0.3);">-</h3>
                                                <small class="text-white fw-semibold" style="text-shadow: 1px 1px 2px rgba(0,0,0,0.3);">Record EXPORT</small>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="text-center p-4 rounded-3 shadow-sm"
                                                 style="background: linear-gradient(135deg, #fdcb6e 0%, #e17055 100%);
                                                        border: 2px solid rgba(255,255,255,0.2);
                                                        transition: all 0.3s ease;"
                                                 onmouseover="this.style.transform='translateY(-5px)'; this.style.boxShadow='0 10px 25px rgba(253,203,110,0.4)';"
                                                 onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 4px 15px rgba(0,0,0,0.1)';">
                                                <div style="font-size: 2em; margin-bottom: 8px;">📊</div>
                                                <h3 class="text-white mb-2 fw-bold" id="stat_total_qt" style="text-shadow: 1px 1px 2px rgba(0,0,0,0.3);">-</h3>
                                                <small class="text-white fw-semibold" style="text-shadow: 1px 1px 2px rgba(0,0,0,0.3);">Quantità Totale</small>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="text-center p-4 rounded-3 shadow-sm"
                                                 style="background: linear-gradient(135deg, #fd79a8 0%, #e84393 100%);
                                                        border: 2px solid rgba(255,255,255,0.2);
                                                        transition: all 0.3s ease;"
                                                 onmouseover="this.style.transform='translateY(-5px)'; this.style.boxShadow='0 10px 25px rgba(253,121,168,0.4)';"
                                                 onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 4px 15px rgba(0,0,0,0.1)';">
                                                <div style="font-size: 2em; margin-bottom: 8px;">🌍</div>
                                                <h3 class="text-white mb-2 fw-bold" id="stat_unique_ports" style="text-shadow: 1px 1px 2px rgba(0,0,0,0.3);">-</h3>
                                                <small class="text-white fw-semibold" style="text-shadow: 1px 1px 2px rgba(0,0,0,0.3);">Porti Unici</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

{% endblock %}

{% block extra_js %}
<script>
    const VIAGGIO_ID = {{ viaggio.id }};
    const ORARI_ESISTENTI = {{ orari|tojson if orari else 'null' }};

    // Funzione di test per download diretto
    function testDirectDownloadSOF() {
        console.log('🚀 Test download diretto SOF completato per viaggio:', VIAGGIO_ID);

        const btn = document.getElementById('download_completed_sof_btn');
        if (btn) {
            btn.disabled = true;
            btn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>Download...';
        }

        fetch(`/operativo/sof/viaggio/${VIAGGIO_ID}/sof/download`, {
            method: 'POST'
        })
        .then(response => {
            console.log('📡 Risposta ricevuta:', response.status, response.statusText);
            if (response.ok) {
                return response.blob().then(blob => {
                    const contentDisposition = response.headers.get('content-disposition');
                    let filename = `SOF_Viaggio_${VIAGGIO_ID}_Completato.docx`;

                    if (contentDisposition) {
                        const filenameMatch = contentDisposition.match(/filename=(.+)/);
                        if (filenameMatch) {
                            filename = filenameMatch[1];
                        }
                    }

                    return { blob, filename };
                });
            } else {
                throw new Error(`Errore HTTP: ${response.status}`);
            }
        })
        .then(({ blob, filename }) => {
            console.log('✅ Download completato:', filename, `(${blob.size} bytes)`);

            // Avvia il download del DOCX
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.style.display = 'none';
            a.href = url;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);

            console.log('💾 File scaricato con successo:', filename);

            // Mostra messaggio di successo
            if (typeof mostraSuccesso === 'function') {
                mostraSuccesso(`SOF completato scaricato: ${filename}`);
            } else {
                alert(`SOF completato scaricato: ${filename}`);
            }
        })
        .catch(error => {
            console.error('❌ Errore download:', error);

            // Mostra messaggio di errore
            if (typeof mostraErrore === 'function') {
                mostraErrore('Errore durante il download del SOF completato');
            } else {
                alert('Errore durante il download del SOF completato: ' + error.message);
            }
        })
        .finally(() => {
            if (btn) {
                btn.disabled = false;
                btn.innerHTML = '<span style="font-size: 1.1em; margin-right: 8px;">📥</span>SCARICA SOF COMPLETATO';
            }
        });
    }
</script>
<script src="/static/js/viaggio-dettaglio.js"></script>
<script src="/static/js/import-viaggio.js"></script>
<script src="/static/js/export-viaggio.js"></script>
<script src="/static/js/sof-viaggio.js"></script>

<!-- TEST DIRETTO API VIAGGIO E DEBUG COMPLETO -->
<script>
// Test immediato per verificare i dati del viaggio
setTimeout(async () => {
    console.log('🧪 TEST DIRETTO API VIAGGIO');
    console.log('🔍 VIAGGIO_ID per test:', {{ viaggio.id }});

    try {
        const response = await fetch(`/api/viaggi/{{ viaggio.id }}`);
        console.log('📡 Status API viaggio:', response.status);

        if (response.status === 200) {
            const data = await response.json();
            console.log('📋 DATI COMPLETI API VIAGGIO:', JSON.stringify(data, null, 2));

            if (data.success && data.data) {
                const viaggioData = data.data;
                console.log('🎯 ANALISI DATI VIAGGIO:');
                console.log('   ID:', viaggioData.id);
                console.log('   Codice:', viaggioData.viaggio);
                console.log('   Porto Arrivo:', viaggioData.porto_arrivo);
                console.log('   Porto Destinazione:', viaggioData.porto_destinazione);
                console.log('   Nome Nave:', viaggioData.nome_nave);

                if (!viaggioData.porto_arrivo && !viaggioData.porto_destinazione) {
                    console.log('❌ PROBLEMA: Il viaggio non ha porti salvati!');
                    console.log('   Questo significa che il salvataggio non ha funzionato.');
                } else {
                    console.log('✅ Il viaggio ha porti salvati, il problema è nel pre-popolamento.');

                    // TEST MANUALE PRE-POPOLAMENTO
                    console.log('🔧 TEST MANUALE PRE-POPOLAMENTO...');

                    setTimeout(() => {
                        const portoArrivoSelect = document.getElementById('porto_arrivo');
                        const portoDestinazioneSelect = document.getElementById('porto_di_destinazione');

                        console.log('🔍 Elementi DOM:');
                        console.log('   porto_arrivo element:', !!portoArrivoSelect);
                        console.log('   porto_di_destinazione element:', !!portoDestinazioneSelect);

                        if (portoArrivoSelect) {
                            console.log('   porto_arrivo options count:', portoArrivoSelect.options.length);
                            console.log('   porto_arrivo current value:', portoArrivoSelect.value);
                            console.log('   porto_arrivo options:', Array.from(portoArrivoSelect.options).map(o => ({value: o.value, text: o.text})));

                            // FORZA PRE-POPOLAMENTO PORTO ARRIVO
                            if (viaggioData.porto_arrivo) {
                                console.log('🚀 FORZA pre-popolamento porto_arrivo con:', viaggioData.porto_arrivo);
                                portoArrivoSelect.value = viaggioData.porto_arrivo;
                                console.log('   Valore dopo impostazione:', portoArrivoSelect.value);

                                if (portoArrivoSelect.value === viaggioData.porto_arrivo) {
                                    console.log('✅ Porto arrivo pre-popolato con successo!');
                                    portoArrivoSelect.style.backgroundColor = '#90EE90';
                                    portoArrivoSelect.style.border = '2px solid #32CD32';
                                } else {
                                    console.log('❌ Pre-popolamento porto arrivo fallito');

                                    // Prova con ricerca manuale
                                    const options = Array.from(portoArrivoSelect.options);
                                    const matchingOption = options.find(opt => opt.value === viaggioData.porto_arrivo);

                                    if (matchingOption) {
                                        console.log('🔧 Trovata opzione matching, forzo selezione...');
                                        matchingOption.selected = true;
                                        portoArrivoSelect.style.backgroundColor = '#FFD700';
                                        portoArrivoSelect.style.border = '2px solid #FFA500';
                                        console.log('✅ Porto arrivo forzato con successo!');
                                    } else {
                                        console.log('❌ Opzione non trovata per:', viaggioData.porto_arrivo);
                                        portoArrivoSelect.style.backgroundColor = '#FFB6C1';
                                        portoArrivoSelect.style.border = '2px solid #FF69B4';
                                    }
                                }
                            }
                        }

                        if (portoDestinazioneSelect) {
                            console.log('   porto_di_destinazione options count:', portoDestinazioneSelect.options.length);
                            console.log('   porto_di_destinazione current value:', portoDestinazioneSelect.value);
                            console.log('   porto_di_destinazione options:', Array.from(portoDestinazioneSelect.options).map(o => ({value: o.value, text: o.text})));

                            // FORZA PRE-POPOLAMENTO PORTO DESTINAZIONE
                            if (viaggioData.porto_destinazione) {
                                console.log('🚀 FORZA pre-popolamento porto_destinazione con:', viaggioData.porto_destinazione);
                                portoDestinazioneSelect.value = viaggioData.porto_destinazione;
                                console.log('   Valore dopo impostazione:', portoDestinazioneSelect.value);

                                if (portoDestinazioneSelect.value === viaggioData.porto_destinazione) {
                                    console.log('✅ Porto destinazione pre-popolato con successo!');
                                    portoDestinazioneSelect.style.backgroundColor = '#90EE90';
                                    portoDestinazioneSelect.style.border = '2px solid #32CD32';
                                } else {
                                    console.log('❌ Pre-popolamento porto destinazione fallito');

                                    // Prova con ricerca manuale
                                    const options = Array.from(portoDestinazioneSelect.options);
                                    const matchingOption = options.find(opt => opt.value === viaggioData.porto_destinazione);

                                    if (matchingOption) {
                                        console.log('🔧 Trovata opzione matching, forzo selezione...');
                                        matchingOption.selected = true;
                                        portoDestinazioneSelect.style.backgroundColor = '#FFD700';
                                        portoDestinazioneSelect.style.border = '2px solid #FFA500';
                                        console.log('✅ Porto destinazione forzato con successo!');
                                    } else {
                                        console.log('❌ Opzione non trovata per:', viaggioData.porto_destinazione);
                                        portoDestinazioneSelect.style.backgroundColor = '#FFB6C1';
                                        portoDestinazioneSelect.style.border = '2px solid #FF69B4';
                                    }
                                }
                            }
                        }
                    }, 2000); // Aspetta 2 secondi per essere sicuri che tutto sia caricato
                }
            } else {
                console.log('❌ API non ha restituito dati validi:', data);
            }
        } else {
            console.log('❌ Errore API:', response.status);
        }
    } catch (error) {
        console.error('❌ Errore test API:', error);
    }
}, 1000);

// Funzione globale per test manuale
window.testPrePopolamento = function() {
    console.log('🧪 TEST MANUALE PRE-POPOLAMENTO');

    const portoArrivoSelect = document.getElementById('porto_arrivo');
    const portoDestinazioneSelect = document.getElementById('porto_di_destinazione');

    if (portoArrivoSelect && portoArrivoSelect.options.length > 1) {
        portoArrivoSelect.value = portoArrivoSelect.options[1].value;
        portoArrivoSelect.style.backgroundColor = '#87CEEB';
        console.log('✅ Test porto arrivo:', portoArrivoSelect.value);
    }

    if (portoDestinazioneSelect && portoDestinazioneSelect.options.length > 2) {
        portoDestinazioneSelect.value = portoDestinazioneSelect.options[2].value;
        portoDestinazioneSelect.style.backgroundColor = '#87CEEB';
        console.log('✅ Test porto destinazione:', portoDestinazioneSelect.value);
    }
};

console.log('🔧 Usa testPrePopolamento() per test manuale');

// ===== ROLLBACK NUCLEARE COMPLETATO =====
// La funzionalità di modifica delle date è stata completamente rimossa
// per ripristinare la stabilità del sistema
console.log('🔄 ROLLBACK NUCLEARE: Sistema ripristinato allo stato precedente');
</script>
{% endblock %}
