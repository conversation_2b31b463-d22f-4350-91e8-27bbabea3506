<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard Amministrazione - SNIP</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

    <!-- CSS Temi Globali -->
    <link rel="stylesheet" href="{{ url_for('static', path='css/global-themes.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', path='css/theme-light-professional.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', path='css/theme-dark-professional.css') }}">
    <style>
        /* Base styles - will be overridden by theme classes */
        body {
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            transition: all 0.3s ease;
        }
        
        .admin-container {
            backdrop-filter: blur(10px);
            border-radius: 20px;
            margin: 20px;
            padding: 30px;
            transition: all 0.3s ease;
        }

        /* Tema Marittimo (default) */
        body.theme-maritime .admin-container,
        body:not([class*="theme-"]) .admin-container {
            background: rgba(255, 255, 255, 0.95);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        /* Tema Scuro */
        body.theme-dark .admin-container {
            background: rgba(52, 73, 94, 0.95);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.1);
            color: #ecf0f1;
        }

        /* Tema Chiaro */
        body.theme-light .admin-container {
            background: rgba(255, 255, 255, 0.98);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
            border: 1px solid rgba(0, 0, 0, 0.1);
            color: #212529;
        }
        
        .stat-card {
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            transition: all 0.3s ease;
        }

        /* Tema Marittimo (default) */
        body.theme-maritime .stat-card,
        body:not([class*="theme-"]) .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        /* Tema Scuro */
        body.theme-dark .stat-card {
            background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%);
            color: #ecf0f1;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        /* Tema Chiaro */
        body.theme-light .stat-card {
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            color: #212529;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(0, 0, 0, 0.1);
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
        }
        
        .stat-card .stat-icon {
            font-size: 2.5rem;
            opacity: 0.8;
        }
        
        .stat-card .stat-number {
            font-size: 2rem;
            font-weight: bold;
            margin: 10px 0;
        }
        
        .admin-nav {
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 30px;
            transition: all 0.3s ease;
        }

        /* Tema Marittimo (default) */
        body.theme-maritime .admin-nav,
        body:not([class*="theme-"]) .admin-nav {
            background: rgba(255, 255, 255, 0.1);
        }

        /* Tema Scuro */
        body.theme-dark .admin-nav {
            background: rgba(44, 62, 80, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        /* Tema Chiaro */
        body.theme-light .admin-nav {
            background: rgba(255, 255, 255, 0.8);
            border: 1px solid rgba(0, 0, 0, 0.1);
        }

        .admin-nav .nav-link {
            font-weight: 500;
            padding: 10px 20px;
            border-radius: 10px;
            margin: 0 5px;
            transition: all 0.3s ease;
        }

        /* Tema Marittimo (default) */
        body.theme-maritime .admin-nav .nav-link,
        body:not([class*="theme-"]) .admin-nav .nav-link {
            color: #2c3e50;
        }

        body.theme-maritime .admin-nav .nav-link:hover,
        body.theme-maritime .admin-nav .nav-link.active,
        body:not([class*="theme-"]) .admin-nav .nav-link:hover,
        body:not([class*="theme-"]) .admin-nav .nav-link.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        /* Tema Scuro */
        body.theme-dark .admin-nav .nav-link {
            color: #ecf0f1;
        }

        body.theme-dark .admin-nav .nav-link:hover,
        body.theme-dark .admin-nav .nav-link.active {
            background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%);
            color: white;
        }

        /* Tema Chiaro */
        body.theme-light .admin-nav .nav-link {
            color: #495057;
        }

        body.theme-light .admin-nav .nav-link:hover,
        body.theme-light .admin-nav .nav-link.active {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
        }
        
        .chart-container {
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            transition: all 0.3s ease;
        }

        /* Tema Marittimo (default) */
        body.theme-maritime .chart-container,
        body:not([class*="theme-"]) .chart-container {
            background: white;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        /* Tema Scuro */
        body.theme-dark .chart-container {
            background: rgba(52, 73, 94, 0.9);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.1);
            color: #ecf0f1;
        }

        /* Tema Chiaro */
        body.theme-light .chart-container {
            background: rgba(255, 255, 255, 0.98);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
            border: 1px solid rgba(0, 0, 0, 0.1);
            color: #212529;
        }
        
        .activity-feed {
            max-height: 400px;
            overflow-y: auto;
        }
        
        .activity-item {
            padding: 15px;
            margin-bottom: 10px;
            border-radius: 0 10px 10px 0;
            transition: all 0.3s ease;
        }

        /* Tema Marittimo (default) */
        body.theme-maritime .activity-item,
        body:not([class*="theme-"]) .activity-item {
            border-left: 3px solid #667eea;
            background: rgba(102, 126, 234, 0.1);
        }

        /* Tema Scuro */
        body.theme-dark .activity-item {
            border-left: 3px solid #3498db;
            background: rgba(52, 152, 219, 0.1);
            color: #ecf0f1;
        }

        /* Tema Chiaro */
        body.theme-light .activity-item {
            border-left: 3px solid #007bff;
            background: rgba(0, 123, 255, 0.1);
            color: #212529;
        }
        
        .btn-admin {
            border: none;
            color: white;
            padding: 10px 25px;
            border-radius: 10px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        /* Tema Marittimo (default) */
        body.theme-maritime .btn-admin,
        body:not([class*="theme-"]) .btn-admin {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        body.theme-maritime .btn-admin:hover,
        body:not([class*="theme-"]) .btn-admin:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
            color: white;
        }

        /* Tema Scuro */
        body.theme-dark .btn-admin {
            background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%);
        }

        body.theme-dark .btn-admin:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(52, 73, 94, 0.4);
            color: white;
        }

        /* Tema Chiaro */
        body.theme-light .btn-admin {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
        }

        body.theme-light .btn-admin:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0, 123, 255, 0.4);
            color: white;
        }
        
        .maritime-header {
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 30px;
            text-align: center;
            transition: all 0.3s ease;
        }

        /* Tema Marittimo (default) */
        body.theme-maritime .maritime-header,
        body:not([class*="theme-"]) .maritime-header {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: white;
        }

        /* Tema Scuro */
        body.theme-dark .maritime-header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: #ecf0f1;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        /* Tema Chiaro */
        body.theme-light .maritime-header {
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            color: #212529;
            border: 1px solid rgba(0, 0, 0, 0.1);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }
        
        .maritime-header h1 {
            margin: 0;
            font-size: 2.5rem;
            font-weight: 300;
        }
        
        .maritime-header .subtitle {
            opacity: 0.9;
            font-size: 1.1rem;
            margin-top: 10px;
        }
    </style>
</head>
<body class="theme-{{ user_theme }}">
    <!-- Navbar principale -->
    {% include 'components/navbar.html' %}

    <div class="container-fluid">
        <!-- Header -->
        <div class="maritime-header">
            <h1><i class="fas fa-anchor me-3"></i>Dashboard Amministrazione SNIP</h1>
            <div class="subtitle">Sistema Navale Integrato Portuale - Pannello di Controllo</div>
        </div>
        
        <!-- Navigation -->
        <div class="admin-nav">
            <ul class="nav nav-pills justify-content-center">
                <li class="nav-item">
                    <a class="nav-link active" href="#dashboard" data-section="dashboard">
                        <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#users" data-section="users">
                        <i class="fas fa-users me-2"></i>Gestione Utenti
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#config" data-section="config">
                        <i class="fas fa-cogs me-2"></i>Configurazioni
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#audit" data-section="audit">
                        <i class="fas fa-clipboard-list me-2"></i>Audit Log
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#sessions" data-section="sessions">
                        <i class="fas fa-user-clock me-2"></i>Sessioni
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#notifications" data-section="notifications">
                        <i class="fas fa-bell me-2"></i>Notifiche
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#system" data-section="system">
                        <i class="fas fa-server me-2"></i>Sistema
                    </a>
                </li>
            </ul>
        </div>
        
        <!-- Dashboard Section -->
        <div id="dashboard-section" class="admin-section">
            <div class="admin-container">
                <h2 class="mb-4"><i class="fas fa-chart-line me-2"></i>Panoramica Sistema</h2>
                
                <!-- Statistics Cards -->
                <div class="row">
                    <div class="col-md-3">
                        <div class="stat-card">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <div class="stat-number" id="total-users">-</div>
                                    <div>Utenti Totali</div>
                                </div>
                                <div class="stat-icon">
                                    <i class="fas fa-users"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <div class="stat-number" id="active-sessions">-</div>
                                    <div>Sessioni Attive</div>
                                </div>
                                <div class="stat-icon">
                                    <i class="fas fa-user-clock"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <div class="stat-number" id="total-viaggi">-</div>
                                    <div>Viaggi Totali</div>
                                </div>
                                <div class="stat-icon">
                                    <i class="fas fa-ship"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <div class="stat-number" id="recent-activity">-</div>
                                    <div>Attività Recenti</div>
                                </div>
                                <div class="stat-icon">
                                    <i class="fas fa-activity"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Charts and Activity -->
                <div class="row">
                    <div class="col-md-6">
                        <div class="chart-container">
                            <h4><i class="fas fa-chart-pie me-2"></i>Utenti per Reparto</h4>
                            <canvas id="repartoChart" width="400" height="200"></canvas>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="chart-container">
                            <h4><i class="fas fa-chart-bar me-2"></i>Utenti per Ruolo</h4>
                            <canvas id="ruoloChart" width="400" height="200"></canvas>
                        </div>
                    </div>
                </div>
                
                <!-- System Health -->
                <div class="row">
                    <div class="col-md-12">
                        <div class="chart-container">
                            <h4><i class="fas fa-heartbeat me-2"></i>Stato Sistema</h4>
                            <div id="system-health" class="p-3">
                                <div class="d-flex justify-content-between align-items-center">
                                    <span>Caricamento stato sistema...</span>
                                    <div class="spinner-border spinner-border-sm" role="status"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Users Section -->
        <div id="users-section" class="admin-section" style="display: none;">
            <div class="admin-container">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2><i class="fas fa-users me-2"></i>Gestione Utenti</h2>
                    <div class="btn-group">
                        <button class="btn btn-admin" onclick="showCreateUserModal()">
                            <i class="fas fa-plus me-2"></i>Nuovo Utente
                        </button>
                        <button class="btn btn-outline-primary" onclick="exportUsers()">
                            <i class="fas fa-download me-2"></i>Esporta
                        </button>
                        <button class="btn btn-outline-success" onclick="refreshUsers()">
                            <i class="fas fa-sync me-2"></i>Aggiorna
                        </button>
                    </div>
                </div>

                <!-- Search and Filters -->
                <div class="row mb-3">
                    <div class="col-md-4">
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-search"></i></span>
                            <input type="text" class="form-control" id="user-search" placeholder="Cerca utenti..." onkeyup="searchUsersLive()">
                            <button class="btn btn-outline-secondary" onclick="clearSearch()">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <select class="form-select" id="filter-reparto" onchange="filterUsers()">
                            <option value="">Tutti i reparti</option>
                            <option value="OPERATIVO">Operativo</option>
                            <option value="AMMINISTRAZIONE">Amministrazione</option>
                            <option value="CONTABILITA">Contabilità</option>
                            <option value="SHORTSEA">Shortsea</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <select class="form-select" id="filter-ruolo" onchange="filterUsers()">
                            <option value="">Tutti i ruoli</option>
                            <option value="USER">User</option>
                            <option value="ADMIN">Admin</option>
                            <option value="SUPER_ADMIN">Super Admin</option>
                            <option value="VISITOR">Visitor</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <select class="form-select" id="filter-stato" onchange="filterUsers()">
                            <option value="">Tutti gli stati</option>
                            <option value="si">Attivi</option>
                            <option value="no">Inattivi</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <div class="text-muted small mt-2">
                            <span id="users-count">0</span> utenti trovati
                        </div>
                    </div>
                </div>
                
                <!-- Users Table -->
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th>
                                    <input type="checkbox" id="select-all-users" onchange="toggleSelectAll()">
                                </th>
                                <th onclick="sortUsers('id_user')" style="cursor: pointer;">
                                    ID <i class="fas fa-sort"></i>
                                </th>
                                <th onclick="sortUsers('nome')" style="cursor: pointer;">
                                    Nome <i class="fas fa-sort"></i>
                                </th>
                                <th onclick="sortUsers('email')" style="cursor: pointer;">
                                    Email <i class="fas fa-sort"></i>
                                </th>
                                <th onclick="sortUsers('reparto')" style="cursor: pointer;">
                                    Reparto <i class="fas fa-sort"></i>
                                </th>
                                <th onclick="sortUsers('ruolo')" style="cursor: pointer;">
                                    Ruolo <i class="fas fa-sort"></i>
                                </th>
                                <th>Stato</th>
                                <th>Ultimo Accesso</th>
                                <th>Azioni</th>
                            </tr>
                        </thead>
                        <tbody id="users-table-body">
                            <tr>
                                <td colspan="9" class="text-center">
                                    <div class="spinner-border" role="status">
                                        <span class="visually-hidden">Caricamento...</span>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <nav aria-label="Paginazione utenti">
                    <ul class="pagination justify-content-center" id="users-pagination">
                        <!-- Pagination will be generated by JavaScript -->
                    </ul>
                </nav>

                <!-- Bulk Actions -->
                <div class="card mt-3" id="bulk-actions" style="display: none;">
                    <div class="card-body">
                        <h6><i class="fas fa-tasks me-2"></i>Azioni Multiple</h6>
                        <div class="btn-group">
                            <button class="btn btn-sm btn-success" onclick="bulkActivateUsers()">
                                <i class="fas fa-check me-1"></i>Attiva Selezionati
                            </button>
                            <button class="btn btn-sm btn-warning" onclick="bulkDeactivateUsers()">
                                <i class="fas fa-pause me-1"></i>Disattiva Selezionati
                            </button>
                            <button class="btn btn-sm btn-info" onclick="bulkChangeReparto()">
                                <i class="fas fa-building me-1"></i>Cambia Reparto
                            </button>
                            <button class="btn btn-sm btn-danger" onclick="bulkDeleteUsers()">
                                <i class="fas fa-trash me-1"></i>Elimina Selezionati
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Configuration Section -->
        <div id="config-section" class="admin-section" style="display: none;">
            <div class="admin-container">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2><i class="fas fa-cogs me-2"></i>Configurazioni Sistema</h2>
                    <div class="btn-group">
                        <button class="btn btn-success" onclick="saveAllConfigurations()">
                            <i class="fas fa-save me-2"></i>Salva Tutto
                        </button>
                        <button class="btn btn-outline-secondary" onclick="resetConfigurations()">
                            <i class="fas fa-undo me-2"></i>Reset
                        </button>
                    </div>
                </div>

                <!-- Configuration Tabs -->
                <ul class="nav nav-pills mb-4" id="config-tabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="ports-tab" data-bs-toggle="pill" data-bs-target="#ports-config" type="button" role="tab">
                            <i class="fas fa-anchor me-1"></i>Porti
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="email-tab" data-bs-toggle="pill" data-bs-target="#email-config" type="button" role="tab">
                            <i class="fas fa-envelope me-1"></i>Email
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="security-tab" data-bs-toggle="pill" data-bs-target="#security-config" type="button" role="tab">
                            <i class="fas fa-shield-alt me-1"></i>Sicurezza
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="sof-tab" data-bs-toggle="pill" data-bs-target="#sof-config" type="button" role="tab">
                            <i class="fas fa-file-alt me-1"></i>SOF
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="interface-tab" data-bs-toggle="pill" data-bs-target="#interface-config" type="button" role="tab">
                            <i class="fas fa-palette me-1"></i>Interface
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="reporting-tab" data-bs-toggle="pill" data-bs-target="#reporting-config" type="button" role="tab">
                            <i class="fas fa-chart-bar me-1"></i>Report
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="database-tab" data-bs-toggle="pill" data-bs-target="#database-config" type="button" role="tab">
                            <i class="fas fa-database me-1"></i>Database
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="system-tab" data-bs-toggle="pill" data-bs-target="#system-config" type="button" role="tab">
                            <i class="fas fa-server me-1"></i>Sistema
                        </button>
                    </li>
                </ul>

                <!-- Configuration Content -->
                <div class="tab-content" id="config-content">

                    <!-- Ports Configuration -->
                    <div class="tab-pane fade show active" id="ports-config" role="tabpanel">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-anchor me-2"></i>Configurazione Porti di Gestione</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <h6>Porto di Salerno</h6>
                                        <div class="mb-3">
                                            <label class="form-label">Nome Completo</label>
                                            <input type="text" class="form-control" id="salerno-name" value="Porto di Salerno">
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">Codice Porto</label>
                                            <input type="text" class="form-control" id="salerno-code" value="ITSRN">
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">Capitaneria</label>
                                            <input type="text" class="form-control" id="salerno-harbor" value="+39 089 123456">
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">Dogana</label>
                                            <input type="text" class="form-control" id="salerno-customs" value="+39 089 654321">
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">Email Porto</label>
                                            <input type="email" class="form-control" id="salerno-email" value="<EMAIL>">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <h6>Porto di Gioia Tauro</h6>
                                        <div class="mb-3">
                                            <label class="form-label">Nome Completo</label>
                                            <input type="text" class="form-control" id="gioia-name" value="Porto di Gioia Tauro">
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">Codice Porto</label>
                                            <input type="text" class="form-control" id="gioia-code" value="ITGIT">
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">Capitaneria</label>
                                            <input type="text" class="form-control" id="gioia-harbor" value="+39 0966 123456">
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">Dogana</label>
                                            <input type="text" class="form-control" id="gioia-customs" value="+39 0966 654321">
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">Email Porto</label>
                                            <input type="email" class="form-control" id="gioia-email" value="<EMAIL>">
                                        </div>
                                    </div>
                                </div>
                                <div class="row mt-3">
                                    <div class="col-12">
                                        <h6>Servizi Offerti</h6>
                                        <div class="row">
                                            <div class="col-md-4">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="service-shipping" checked>
                                                    <label class="form-check-label" for="service-shipping">Shipping</label>
                                                </div>
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="service-forwarding" checked>
                                                    <label class="form-check-label" for="service-forwarding">Forwarding</label>
                                                </div>
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="service-customs" checked>
                                                    <label class="form-check-label" for="service-customs">Customs Clearance</label>
                                                </div>
                                            </div>
                                            <div class="col-md-4">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="service-storage" checked>
                                                    <label class="form-check-label" for="service-storage">Storage</label>
                                                </div>
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="service-transport" checked>
                                                    <label class="form-check-label" for="service-transport">Transport</label>
                                                </div>
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="service-documentation" checked>
                                                    <label class="form-check-label" for="service-documentation">Documentation</label>
                                                </div>
                                            </div>
                                            <div class="col-md-4">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="service-insurance" checked>
                                                    <label class="form-check-label" for="service-insurance">Insurance</label>
                                                </div>
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="service-tracking" checked>
                                                    <label class="form-check-label" for="service-tracking">Cargo Tracking</label>
                                                </div>
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="service-consulting" checked>
                                                    <label class="form-check-label" for="service-consulting">Consulting</label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Email Configuration -->
                    <div class="tab-pane fade" id="email-config" role="tabpanel">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-envelope me-2"></i>Configurazione Email</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <h6>Server SMTP</h6>
                                        <div class="mb-3">
                                            <label class="form-label">Host SMTP</label>
                                            <input type="text" class="form-control" id="smtp-host" value="smtp.gmail.com">
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">Porta</label>
                                            <input type="number" class="form-control" id="smtp-port" value="587">
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">Username</label>
                                            <input type="text" class="form-control" id="smtp-username" value="<EMAIL>">
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">Password</label>
                                            <input type="password" class="form-control" id="smtp-password" value="••••••••">
                                        </div>
                                        <div class="form-check mb-3">
                                            <input class="form-check-input" type="checkbox" id="smtp-ssl" checked>
                                            <label class="form-check-label" for="smtp-ssl">Usa SSL/TLS</label>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <h6>Configurazioni Email</h6>
                                        <div class="mb-3">
                                            <label class="form-label">Email Mittente</label>
                                            <input type="email" class="form-control" id="sender-email" value="<EMAIL>">
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">Nome Mittente</label>
                                            <input type="text" class="form-control" id="sender-name" value="Michele Autuori Srl">
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">Email Admin</label>
                                            <input type="email" class="form-control" id="admin-email" value="<EMAIL>">
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">Email Supporto</label>
                                            <input type="email" class="form-control" id="support-email" value="<EMAIL>">
                                        </div>
                                    </div>
                                </div>
                                <div class="row mt-3">
                                    <div class="col-12">
                                        <h6>Notifiche Automatiche</h6>
                                        <div class="row">
                                            <div class="col-md-4">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="notify-sof-completed" checked>
                                                    <label class="form-check-label" for="notify-sof-completed">SOF Completato</label>
                                                </div>
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="notify-new-voyage" checked>
                                                    <label class="form-check-label" for="notify-new-voyage">Nuovo Viaggio</label>
                                                </div>
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="notify-user-created" checked>
                                                    <label class="form-check-label" for="notify-user-created">Nuovo Utente</label>
                                                </div>
                                            </div>
                                            <div class="col-md-4">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="notify-login-failed" checked>
                                                    <label class="form-check-label" for="notify-login-failed">Login Fallito</label>
                                                </div>
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="notify-system-error" checked>
                                                    <label class="form-check-label" for="notify-system-error">Errori Sistema</label>
                                                </div>
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="notify-backup" checked>
                                                    <label class="form-check-label" for="notify-backup">Backup Completato</label>
                                                </div>
                                            </div>
                                            <div class="col-md-4">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="notify-daily-report" checked>
                                                    <label class="form-check-label" for="notify-daily-report">Report Giornaliero</label>
                                                </div>
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="notify-weekly-report">
                                                    <label class="form-check-label" for="notify-weekly-report">Report Settimanale</label>
                                                </div>
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="notify-monthly-report">
                                                    <label class="form-check-label" for="notify-monthly-report">Report Mensile</label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="mt-3">
                                    <button class="btn btn-outline-primary" onclick="testEmailConfiguration()">
                                        <i class="fas fa-paper-plane me-1"></i>Test Email
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Security Configuration -->
                    <div class="tab-pane fade" id="security-config" role="tabpanel">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-shield-alt me-2"></i>Configurazione Sicurezza</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <h6>Politiche Password</h6>
                                        <div class="mb-3">
                                            <label class="form-label">Lunghezza Minima</label>
                                            <input type="number" class="form-control" id="password-min-length" value="6" min="4" max="20">
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">Scadenza Password (giorni)</label>
                                            <input type="number" class="form-control" id="password-expiry" value="90" min="30" max="365">
                                        </div>
                                        <div class="form-check mb-2">
                                            <input class="form-check-input" type="checkbox" id="password-uppercase" checked>
                                            <label class="form-check-label" for="password-uppercase">Richiedi Maiuscole</label>
                                        </div>
                                        <div class="form-check mb-2">
                                            <input class="form-check-input" type="checkbox" id="password-numbers" checked>
                                            <label class="form-check-label" for="password-numbers">Richiedi Numeri</label>
                                        </div>
                                        <div class="form-check mb-3">
                                            <input class="form-check-input" type="checkbox" id="password-special">
                                            <label class="form-check-label" for="password-special">Richiedi Caratteri Speciali</label>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <h6>Controllo Accessi</h6>
                                        <div class="mb-3">
                                            <label class="form-label">Timeout Sessione (minuti)</label>
                                            <input type="number" class="form-control" id="session-timeout" value="60" min="15" max="480">
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">Max Tentativi Login</label>
                                            <input type="number" class="form-control" id="max-login-attempts" value="5" min="3" max="10">
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">Blocco Account (minuti)</label>
                                            <input type="number" class="form-control" id="account-lockout" value="15" min="5" max="60">
                                        </div>
                                        <div class="form-check mb-2">
                                            <input class="form-check-input" type="checkbox" id="two-factor-auth">
                                            <label class="form-check-label" for="two-factor-auth">Autenticazione a Due Fattori</label>
                                        </div>
                                        <div class="form-check mb-3">
                                            <input class="form-check-input" type="checkbox" id="ip-whitelist">
                                            <label class="form-check-label" for="ip-whitelist">Whitelist IP Admin</label>
                                        </div>
                                    </div>
                                </div>
                                <div class="row mt-3">
                                    <div class="col-md-6">
                                        <h6>Logging e Audit</h6>
                                        <div class="mb-3">
                                            <label class="form-label">Retention Log (giorni)</label>
                                            <input type="number" class="form-control" id="log-retention" value="90" min="30" max="365">
                                        </div>
                                        <div class="form-check mb-2">
                                            <input class="form-check-input" type="checkbox" id="log-login" checked>
                                            <label class="form-check-label" for="log-login">Log Accessi</label>
                                        </div>
                                        <div class="form-check mb-2">
                                            <input class="form-check-input" type="checkbox" id="log-actions" checked>
                                            <label class="form-check-label" for="log-actions">Log Azioni Utente</label>
                                        </div>
                                        <div class="form-check mb-3">
                                            <input class="form-check-input" type="checkbox" id="log-errors" checked>
                                            <label class="form-check-label" for="log-errors">Log Errori Sistema</label>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <h6>Backup e Sicurezza</h6>
                                        <div class="mb-3">
                                            <label class="form-label">Backup Automatico</label>
                                            <select class="form-select" id="backup-frequency">
                                                <option value="daily" selected>Giornaliero</option>
                                                <option value="weekly">Settimanale</option>
                                                <option value="monthly">Mensile</option>
                                                <option value="disabled">Disabilitato</option>
                                            </select>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">Retention Backup (giorni)</label>
                                            <input type="number" class="form-control" id="backup-retention" value="30" min="7" max="365">
                                        </div>
                                        <div class="form-check mb-2">
                                            <input class="form-check-input" type="checkbox" id="encrypt-backups" checked>
                                            <label class="form-check-label" for="encrypt-backups">Crittografia Backup</label>
                                        </div>
                                        <div class="form-check mb-3">
                                            <input class="form-check-input" type="checkbox" id="ssl-only" checked>
                                            <label class="form-check-label" for="ssl-only">Solo Connessioni SSL</label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- SOF Configuration -->
                    <div class="tab-pane fade" id="sof-config" role="tabpanel">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-file-alt me-2"></i>Configurazione SOF</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <h6>Template SOF</h6>
                                        <div class="mb-3">
                                            <label class="form-label">Titolo Documento</label>
                                            <input type="text" class="form-control" id="sof-title" value="STATEMENT OF FACTS">
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">Sottotitolo</label>
                                            <input type="text" class="form-control" id="sof-subtitle" value="shipping and forwarding agency">
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">Logo Dimensione</label>
                                            <select class="form-select" id="sof-logo-size">
                                                <option value="small">Piccolo</option>
                                                <option value="medium" selected>Medio</option>
                                                <option value="large">Grande</option>
                                            </select>
                                        </div>
                                        <div class="form-check mb-2">
                                            <input class="form-check-input" type="checkbox" id="sof-logo-top" checked>
                                            <label class="form-check-label" for="sof-logo-top">Logo in Alto</label>
                                        </div>
                                        <div class="form-check mb-3">
                                            <input class="form-check-input" type="checkbox" id="sof-logo-bottom" checked>
                                            <label class="form-check-label" for="sof-logo-bottom">Logo in Basso</label>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <h6>Campi SOF</h6>
                                        <div class="form-check mb-2">
                                            <input class="form-check-input" type="checkbox" id="sof-field-sbe" checked>
                                            <label class="form-check-label" for="sof-field-sbe">SBE</label>
                                        </div>
                                        <div class="form-check mb-2">
                                            <input class="form-check-input" type="checkbox" id="sof-field-pilot" checked>
                                            <label class="form-check-label" for="sof-field-pilot">Arrival/Departure Pilot</label>
                                        </div>
                                        <div class="form-check mb-2">
                                            <input class="form-check-input" type="checkbox" id="sof-field-tug" checked>
                                            <label class="form-check-label" for="sof-field-tug">Arrival/Departure Tug</label>
                                        </div>
                                        <div class="form-check mb-2">
                                            <input class="form-check-input" type="checkbox" id="sof-field-draft" checked>
                                            <label class="form-check-label" for="sof-field-draft">Draft</label>
                                        </div>
                                        <div class="form-check mb-2">
                                            <input class="form-check-input" type="checkbox" id="sof-field-soc" checked>
                                            <label class="form-check-label" for="sof-field-soc">SOC</label>
                                        </div>
                                        <div class="form-check mb-2">
                                            <input class="form-check-input" type="checkbox" id="sof-field-foc" checked>
                                            <label class="form-check-label" for="sof-field-foc">FOC</label>
                                        </div>
                                        <div class="form-check mb-3">
                                            <input class="form-check-input" type="checkbox" id="sof-field-summary">
                                            <label class="form-check-label" for="sof-field-summary">Tabella Riassunto</label>
                                        </div>
                                    </div>
                                </div>
                                <div class="row mt-3">
                                    <div class="col-md-6">
                                        <h6>Numerazione e Formati</h6>
                                        <div class="mb-3">
                                            <label class="form-label">Formato Numerazione</label>
                                            <select class="form-select" id="sof-numbering">
                                                <option value="auto" selected>Automatica (SOF-2024-001)</option>
                                                <option value="manual">Manuale</option>
                                                <option value="date">Per Data (20240101-001)</option>
                                            </select>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">Formato Export</label>
                                            <select class="form-select" id="sof-export-format">
                                                <option value="docx" selected>DOCX</option>
                                                <option value="pdf">PDF</option>
                                                <option value="both">Entrambi</option>
                                            </select>
                                        </div>
                                        <div class="form-check mb-3">
                                            <input class="form-check-input" type="checkbox" id="sof-watermark">
                                            <label class="form-check-label" for="sof-watermark">Watermark Documenti</label>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <h6>Workflow e Approvazione</h6>
                                        <div class="mb-3">
                                            <label class="form-label">Approvazione Richiesta</label>
                                            <select class="form-select" id="sof-approval">
                                                <option value="none" selected>Nessuna</option>
                                                <option value="admin">Solo Admin</option>
                                                <option value="super_admin">Solo Super Admin</option>
                                            </select>
                                        </div>
                                        <div class="form-check mb-2">
                                            <input class="form-check-input" type="checkbox" id="sof-auto-email" checked>
                                            <label class="form-check-label" for="sof-auto-email">Email Automatica</label>
                                        </div>
                                        <div class="form-check mb-2">
                                            <input class="form-check-input" type="checkbox" id="sof-version-control" checked>
                                            <label class="form-check-label" for="sof-version-control">Controllo Versioni</label>
                                        </div>
                                        <div class="form-check mb-3">
                                            <input class="form-check-input" type="checkbox" id="sof-archive" checked>
                                            <label class="form-check-label" for="sof-archive">Archiviazione Automatica</label>
                                        </div>
                                    </div>
                                </div>
                                <div class="mt-3">
                                    <button class="btn btn-outline-primary" onclick="previewSOFTemplate()">
                                        <i class="fas fa-eye me-1"></i>Anteprima Template
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Interface Configuration -->
                    <div class="tab-pane fade" id="interface-config" role="tabpanel">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-palette me-2"></i>Configurazione Interface</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <h6>Tema e Colori</h6>
                                        <div class="mb-3">
                                            <label class="form-label">Tema Principale</label>
                                            <select class="form-select" id="theme-main">
                                                <option value="maritime" selected>Marittimo (Blu)</option>
                                                <option value="professional">Professionale (Grigio)</option>
                                                <option value="modern">Moderno (Scuro)</option>
                                                <option value="classic">Classico (Bianco)</option>
                                            </select>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">Colore Primario</label>
                                            <input type="color" class="form-control form-control-color" id="primary-color" value="#0066cc">
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">Colore Secondario</label>
                                            <input type="color" class="form-control form-control-color" id="secondary-color" value="#004499">
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">Colore Accento</label>
                                            <input type="color" class="form-control form-control-color" id="accent-color" value="#ff6b35">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <h6>Logo e Branding</h6>
                                        <div class="mb-3">
                                            <label class="form-label">Logo Menu</label>
                                            <input type="file" class="form-control" id="logo-menu" accept="image/*">
                                            <small class="text-muted">Dimensioni consigliate: 200x60px</small>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">Logo Login</label>
                                            <input type="file" class="form-control" id="logo-login" accept="image/*">
                                            <small class="text-muted">Dimensioni consigliate: 300x100px</small>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">Favicon</label>
                                            <input type="file" class="form-control" id="favicon" accept="image/*">
                                            <small class="text-muted">Formato: ICO, PNG 32x32px</small>
                                        </div>
                                        <div class="form-check mb-3">
                                            <input class="form-check-input" type="checkbox" id="glassmorphism" checked>
                                            <label class="form-check-label" for="glassmorphism">Effetto Glassmorphism</label>
                                        </div>
                                    </div>
                                </div>
                                <div class="row mt-3">
                                    <div class="col-md-6">
                                        <h6>Localizzazione</h6>
                                        <div class="mb-3">
                                            <label class="form-label">Lingua Predefinita</label>
                                            <select class="form-select" id="default-language">
                                                <option value="it" selected>Italiano</option>
                                                <option value="en">English</option>
                                                <option value="es">Español</option>
                                                <option value="fr">Français</option>
                                            </select>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">Timezone</label>
                                            <select class="form-select" id="timezone">
                                                <option value="Europe/Rome" selected>Europa/Roma (CET)</option>
                                                <option value="UTC">UTC</option>
                                                <option value="Europe/London">Europa/Londra (GMT)</option>
                                            </select>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">Formato Data</label>
                                            <select class="form-select" id="date-format">
                                                <option value="DD/MM/YYYY" selected>DD/MM/YYYY</option>
                                                <option value="MM/DD/YYYY">MM/DD/YYYY</option>
                                                <option value="YYYY-MM-DD">YYYY-MM-DD</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <h6>Layout e Navigazione</h6>
                                        <div class="mb-3">
                                            <label class="form-label">Sidebar Predefinita</label>
                                            <select class="form-select" id="sidebar-default">
                                                <option value="expanded" selected>Espansa</option>
                                                <option value="collapsed">Compressa</option>
                                                <option value="auto">Automatica</option>
                                            </select>
                                        </div>
                                        <div class="form-check mb-2">
                                            <input class="form-check-input" type="checkbox" id="responsive-design" checked>
                                            <label class="form-check-label" for="responsive-design">Design Responsive</label>
                                        </div>
                                        <div class="form-check mb-2">
                                            <input class="form-check-input" type="checkbox" id="dark-mode">
                                            <label class="form-check-label" for="dark-mode">Modalità Scura</label>
                                        </div>
                                        <div class="form-check mb-3">
                                            <input class="form-check-input" type="checkbox" id="animations" checked>
                                            <label class="form-check-label" for="animations">Animazioni</label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Reporting Configuration -->
                    <div class="tab-pane fade" id="reporting-config" role="tabpanel">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-chart-bar me-2"></i>Configurazione Reporting</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <h6>Report Automatici</h6>
                                        <div class="mb-3">
                                            <label class="form-label">Report Giornaliero</label>
                                            <select class="form-select" id="daily-report">
                                                <option value="disabled">Disabilitato</option>
                                                <option value="enabled" selected>Abilitato</option>
                                            </select>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">Orario Invio</label>
                                            <input type="time" class="form-control" id="daily-report-time" value="08:00">
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">Report Settimanale</label>
                                            <select class="form-select" id="weekly-report">
                                                <option value="disabled">Disabilitato</option>
                                                <option value="monday" selected>Lunedì</option>
                                                <option value="friday">Venerdì</option>
                                            </select>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">Report Mensile</label>
                                            <select class="form-select" id="monthly-report">
                                                <option value="disabled">Disabilitato</option>
                                                <option value="first" selected>Primo del mese</option>
                                                <option value="last">Ultimo del mese</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <h6>KPI Dashboard</h6>
                                        <div class="form-check mb-2">
                                            <input class="form-check-input" type="checkbox" id="kpi-voyages" checked>
                                            <label class="form-check-label" for="kpi-voyages">Viaggi Totali</label>
                                        </div>
                                        <div class="form-check mb-2">
                                            <input class="form-check-input" type="checkbox" id="kpi-sof" checked>
                                            <label class="form-check-label" for="kpi-sof">SOF Completati</label>
                                        </div>
                                        <div class="form-check mb-2">
                                            <input class="form-check-input" type="checkbox" id="kpi-users" checked>
                                            <label class="form-check-label" for="kpi-users">Utenti Attivi</label>
                                        </div>
                                        <div class="form-check mb-2">
                                            <input class="form-check-input" type="checkbox" id="kpi-ports" checked>
                                            <label class="form-check-label" for="kpi-ports">Statistiche Porti</label>
                                        </div>
                                        <div class="form-check mb-2">
                                            <input class="form-check-input" type="checkbox" id="kpi-performance" checked>
                                            <label class="form-check-label" for="kpi-performance">Performance Sistema</label>
                                        </div>
                                        <div class="form-check mb-3">
                                            <input class="form-check-input" type="checkbox" id="kpi-revenue">
                                            <label class="form-check-label" for="kpi-revenue">Ricavi (se disponibili)</label>
                                        </div>
                                    </div>
                                </div>
                                <div class="row mt-3">
                                    <div class="col-md-6">
                                        <h6>Export e Formati</h6>
                                        <div class="mb-3">
                                            <label class="form-label">Formato Export Predefinito</label>
                                            <select class="form-select" id="export-format">
                                                <option value="excel" selected>Excel (.xlsx)</option>
                                                <option value="csv">CSV</option>
                                                <option value="pdf">PDF</option>
                                                <option value="json">JSON</option>
                                            </select>
                                        </div>
                                        <div class="form-check mb-2">
                                            <input class="form-check-input" type="checkbox" id="auto-export" checked>
                                            <label class="form-check-label" for="auto-export">Export Automatico</label>
                                        </div>
                                        <div class="form-check mb-3">
                                            <input class="form-check-input" type="checkbox" id="compress-exports">
                                            <label class="form-check-label" for="compress-exports">Comprimi Export</label>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <h6>Destinatari Report</h6>
                                        <div class="mb-3">
                                            <label class="form-label">Email Destinatari</label>
                                            <textarea class="form-control" id="report-recipients" rows="3" placeholder="<EMAIL>&#10;<EMAIL>"><EMAIL>
<EMAIL></textarea>
                                        </div>
                                        <div class="form-check mb-2">
                                            <input class="form-check-input" type="checkbox" id="include-charts" checked>
                                            <label class="form-check-label" for="include-charts">Includi Grafici</label>
                                        </div>
                                        <div class="form-check mb-3">
                                            <input class="form-check-input" type="checkbox" id="detailed-reports">
                                            <label class="form-check-label" for="detailed-reports">Report Dettagliati</label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Database Configuration -->
                    <div class="tab-pane fade" id="database-config" role="tabpanel">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-database me-2"></i>Configurazione Database</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <h6>Backup e Manutenzione</h6>
                                        <div class="mb-3">
                                            <label class="form-label">Backup Automatico</label>
                                            <select class="form-select" id="db-backup-schedule">
                                                <option value="hourly">Ogni Ora</option>
                                                <option value="daily" selected>Giornaliero</option>
                                                <option value="weekly">Settimanale</option>
                                                <option value="monthly">Mensile</option>
                                            </select>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">Orario Backup</label>
                                            <input type="time" class="form-control" id="db-backup-time" value="02:00">
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">Retention Backup (giorni)</label>
                                            <input type="number" class="form-control" id="db-backup-retention" value="30" min="7" max="365">
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">Percorso Backup</label>
                                            <input type="text" class="form-control" id="db-backup-path" value="/backups/snip/" readonly>
                                        </div>
                                        <div class="form-check mb-3">
                                            <input class="form-check-input" type="checkbox" id="db-compress-backup" checked>
                                            <label class="form-check-label" for="db-compress-backup">Comprimi Backup</label>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <h6>Ottimizzazione</h6>
                                        <div class="mb-3">
                                            <label class="form-label">Pulizia Automatica Log</label>
                                            <select class="form-select" id="db-log-cleanup">
                                                <option value="daily" selected>Giornaliera</option>
                                                <option value="weekly">Settimanale</option>
                                                <option value="monthly">Mensile</option>
                                                <option value="disabled">Disabilitata</option>
                                            </select>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">Archiviazione Viaggi (mesi)</label>
                                            <input type="number" class="form-control" id="db-archive-months" value="24" min="12" max="120">
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">Ottimizzazione Tabelle</label>
                                            <select class="form-select" id="db-optimize">
                                                <option value="weekly" selected>Settimanale</option>
                                                <option value="monthly">Mensile</option>
                                                <option value="disabled">Disabilitata</option>
                                            </select>
                                        </div>
                                        <div class="form-check mb-2">
                                            <input class="form-check-input" type="checkbox" id="db-auto-vacuum" checked>
                                            <label class="form-check-label" for="db-auto-vacuum">Auto Vacuum</label>
                                        </div>
                                        <div class="form-check mb-3">
                                            <input class="form-check-input" type="checkbox" id="db-analyze" checked>
                                            <label class="form-check-label" for="db-analyze">Auto Analyze</label>
                                        </div>
                                    </div>
                                </div>
                                <div class="row mt-3">
                                    <div class="col-md-6">
                                        <h6>Monitoraggio</h6>
                                        <div class="mb-3">
                                            <label class="form-label">Soglia Spazio Disco (%)</label>
                                            <input type="number" class="form-control" id="db-disk-threshold" value="85" min="50" max="95">
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">Soglia Connessioni</label>
                                            <input type="number" class="form-control" id="db-connection-threshold" value="80" min="50" max="100">
                                        </div>
                                        <div class="form-check mb-2">
                                            <input class="form-check-input" type="checkbox" id="db-monitor-performance" checked>
                                            <label class="form-check-label" for="db-monitor-performance">Monitor Performance</label>
                                        </div>
                                        <div class="form-check mb-3">
                                            <input class="form-check-input" type="checkbox" id="db-alert-email" checked>
                                            <label class="form-check-label" for="db-alert-email">Alert via Email</label>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <h6>Azioni Manuali</h6>
                                        <div class="d-grid gap-2">
                                            <button class="btn btn-outline-primary" onclick="createManualBackup()">
                                                <i class="fas fa-download me-1"></i>Backup Manuale
                                            </button>
                                            <button class="btn btn-outline-warning" onclick="optimizeDatabase()">
                                                <i class="fas fa-tools me-1"></i>Ottimizza Database
                                            </button>
                                            <button class="btn btn-outline-info" onclick="checkDatabaseHealth()">
                                                <i class="fas fa-heartbeat me-1"></i>Verifica Salute DB
                                            </button>
                                            <button class="btn btn-outline-secondary" onclick="viewDatabaseStats()">
                                                <i class="fas fa-chart-line me-1"></i>Statistiche DB
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- System Configuration -->
                    <div class="tab-pane fade" id="system-config" role="tabpanel">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-server me-2"></i>Configurazione Sistema</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <h6>Informazioni Sistema</h6>
                                        <div class="mb-3">
                                            <label class="form-label">Versione Applicazione</label>
                                            <div class="input-group">
                                                <input type="text" class="form-control" id="app-version" value="v2.0.0" placeholder="es. v2.0.0">
                                                <button class="btn btn-outline-primary" type="button" onclick="updateAppVersion()">
                                                    <i class="fas fa-save me-1"></i>Aggiorna
                                                </button>
                                            </div>
                                            <small class="form-text text-muted">La versione apparirà nel logo dell'applicazione</small>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">Ambiente</label>
                                            <select class="form-select" id="environment">
                                                <option value="production" selected>Produzione</option>
                                                <option value="staging">Staging</option>
                                                <option value="development">Sviluppo</option>
                                            </select>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">Debug Mode</label>
                                            <select class="form-select" id="debug-mode">
                                                <option value="disabled" selected>Disabilitato</option>
                                                <option value="enabled">Abilitato</option>
                                                <option value="verbose">Verbose</option>
                                            </select>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">Log Level</label>
                                            <select class="form-select" id="log-level">
                                                <option value="ERROR">ERROR</option>
                                                <option value="WARNING">WARNING</option>
                                                <option value="INFO" selected>INFO</option>
                                                <option value="DEBUG">DEBUG</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <h6>Performance e Limiti</h6>
                                        <div class="mb-3">
                                            <label class="form-label">Max Upload Size (MB)</label>
                                            <input type="number" class="form-control" id="max-upload-size" value="50" min="1" max="500">
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">Timeout Request (secondi)</label>
                                            <input type="number" class="form-control" id="request-timeout" value="30" min="10" max="300">
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">Max Connessioni Simultanee</label>
                                            <input type="number" class="form-control" id="max-connections" value="100" min="10" max="1000">
                                        </div>
                                        <div class="form-check mb-2">
                                            <input class="form-check-input" type="checkbox" id="cache-enabled" checked>
                                            <label class="form-check-label" for="cache-enabled">Cache Abilitata</label>
                                        </div>
                                        <div class="form-check mb-3">
                                            <input class="form-check-input" type="checkbox" id="compression-enabled" checked>
                                            <label class="form-check-label" for="compression-enabled">Compressione Gzip</label>
                                        </div>
                                    </div>
                                </div>
                                <div class="row mt-3">
                                    <div class="col-md-6">
                                        <h6>Manutenzione</h6>
                                        <div class="mb-3">
                                            <label class="form-label">Modalità Manutenzione</label>
                                            <select class="form-select" id="maintenance-mode">
                                                <option value="disabled" selected>Disabilitata</option>
                                                <option value="enabled">Abilitata</option>
                                                <option value="scheduled">Programmata</option>
                                            </select>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">Messaggio Manutenzione</label>
                                            <textarea class="form-control" id="maintenance-message" rows="2" placeholder="Sistema in manutenzione...">Sistema in manutenzione. Torneremo presto online.</textarea>
                                        </div>
                                        <div class="form-check mb-3">
                                            <input class="form-check-input" type="checkbox" id="auto-updates">
                                            <label class="form-check-label" for="auto-updates">Aggiornamenti Automatici</label>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <h6>Monitoraggio Sistema</h6>
                                        <div class="mb-3">
                                            <div class="d-flex justify-content-between">
                                                <span>CPU Usage:</span>
                                                <span id="cpu-usage" class="badge bg-success">15%</span>
                                            </div>
                                            <div class="progress mb-2">
                                                <div class="progress-bar bg-success" style="width: 15%"></div>
                                            </div>
                                        </div>
                                        <div class="mb-3">
                                            <div class="d-flex justify-content-between">
                                                <span>Memory Usage:</span>
                                                <span id="memory-usage" class="badge bg-warning">65%</span>
                                            </div>
                                            <div class="progress mb-2">
                                                <div class="progress-bar bg-warning" style="width: 65%"></div>
                                            </div>
                                        </div>
                                        <div class="mb-3">
                                            <div class="d-flex justify-content-between">
                                                <span>Disk Usage:</span>
                                                <span id="disk-usage" class="badge bg-info">45%</span>
                                            </div>
                                            <div class="progress mb-2">
                                                <div class="progress-bar bg-info" style="width: 45%"></div>
                                            </div>
                                        </div>
                                        <div class="d-grid">
                                            <button class="btn btn-outline-primary" onclick="refreshSystemStats()">
                                                <i class="fas fa-sync me-1"></i>Aggiorna Statistiche
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div> <!-- End tab-content -->
            </div> <!-- End admin-container -->
        </div> <!-- End config-section -->
        
        <div id="audit-section" class="admin-section" style="display: none;">
            <div class="admin-container">
                <h2><i class="fas fa-clipboard-list me-2"></i>Log di Audit</h2>
                <p>Sezione audit log in caricamento...</p>
            </div>
        </div>
        
        <div id="sessions-section" class="admin-section" style="display: none;">
            <div class="admin-container">
                <h2><i class="fas fa-user-clock me-2"></i>Gestione Sessioni</h2>
                <p>Sezione sessioni in caricamento...</p>
            </div>
        </div>

        <!-- Notifications Section -->
        <div id="notifications-section" class="admin-section" style="display: none;">
            <div class="admin-container">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2><i class="fas fa-bell me-2"></i>Sistema Notifiche</h2>
                    <div class="btn-group">
                        <button class="btn btn-admin" onclick="openNotificationsPage()">
                            <i class="fas fa-external-link-alt me-2"></i>Apri Gestione Notifiche
                        </button>
                        <button class="btn btn-outline-info" onclick="refreshNotificationsStats()">
                            <i class="fas fa-sync me-2"></i>Aggiorna
                        </button>
                    </div>
                </div>

                <!-- Statistiche Notifiche -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="stat-card">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <div class="stat-number" id="total-notifications">-</div>
                                    <div>Totale Notifiche</div>
                                </div>
                                <div class="stat-icon">
                                    <i class="fas fa-bell"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <div class="stat-number" id="active-notifications">-</div>
                                    <div>Attive</div>
                                </div>
                                <div class="stat-icon">
                                    <i class="fas fa-bell-slash"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <div class="stat-number" id="unread-notifications">-</div>
                                    <div>Non Lette</div>
                                </div>
                                <div class="stat-icon">
                                    <i class="fas fa-envelope"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <div class="stat-number" id="old-notifications">-</div>
                                    <div>Vecchie (>30gg)</div>
                                </div>
                                <div class="stat-icon">
                                    <i class="fas fa-archive"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Azioni Rapide -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header bg-primary text-white">
                                <h5><i class="fas fa-plus me-2"></i>Azioni Rapide</h5>
                            </div>
                            <div class="card-body">
                                <div class="d-grid gap-2">
                                    <button class="btn btn-outline-primary" onclick="createQuickNotification()">
                                        <i class="fas fa-plus me-2"></i>Crea Notifica Rapida
                                    </button>
                                    <button class="btn btn-outline-warning" onclick="cleanupOldNotifications()">
                                        <i class="fas fa-broom me-2"></i>Pulizia Notifiche Vecchie
                                    </button>
                                    <button class="btn btn-outline-danger" onclick="deleteAllNotifications()">
                                        <i class="fas fa-trash-alt me-2"></i>Elimina Tutte le Notifiche
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header bg-info text-white">
                                <h5><i class="fas fa-chart-pie me-2"></i>Distribuzione per Reparto</h5>
                            </div>
                            <div class="card-body">
                                <canvas id="notificationsChart" width="400" height="200"></canvas>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Notifiche Recenti -->
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-clock me-2"></i>Notifiche Recenti</h5>
                    </div>
                    <div class="card-body">
                        <div id="recent-notifications-list">
                            <div class="text-center">
                                <div class="spinner-border" role="status">
                                    <span class="visually-hidden">Caricamento...</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div id="system-section" class="admin-section" style="display: none;">
            <div class="admin-container">
                <h2><i class="fas fa-server me-2"></i>Informazioni Sistema</h2>
                <p>Sezione sistema in caricamento...</p>
            </div>
        </div>
    </div>

    <!-- User Management Modals -->

    <!-- Create/Edit User Modal -->
    <div class="modal fade" id="userModal" tabindex="-1" aria-labelledby="userModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="userModalLabel">
                        <i class="fas fa-user-plus me-2"></i>Nuovo Utente
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="userForm">
                        <input type="hidden" id="user-id" name="user_id">

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="user-nome" class="form-label">
                                        <i class="fas fa-user me-1"></i>Nome *
                                    </label>
                                    <input type="text" class="form-control" id="user-nome" name="nome" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="user-cognome" class="form-label">
                                        <i class="fas fa-user me-1"></i>Cognome *
                                    </label>
                                    <input type="text" class="form-control" id="user-cognome" name="cognome" required>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-12">
                                <div class="mb-3">
                                    <label for="user-email" class="form-label">
                                        <i class="fas fa-envelope me-1"></i>Email *
                                    </label>
                                    <input type="email" class="form-control" id="user-email" name="email" required>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="user-reparto" class="form-label">
                                        <i class="fas fa-building me-1"></i>Reparto *
                                    </label>
                                    <select class="form-select" id="user-reparto" name="reparto" required>
                                        <option value="">Seleziona reparto</option>
                                        <option value="OPERATIVO">Operativo</option>
                                        <option value="AMMINISTRAZIONE">Amministrazione</option>
                                        <option value="CONTABILITA">Contabilità</option>
                                        <option value="SHORTSEA">Shortsea</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="user-ruolo" class="form-label">
                                        <i class="fas fa-shield-alt me-1"></i>Ruolo *
                                    </label>
                                    <select class="form-select" id="user-ruolo" name="ruolo" required>
                                        <option value="">Seleziona ruolo</option>
                                        <option value="USER">User</option>
                                        <option value="ADMIN">Admin</option>
                                        <option value="SUPER_ADMIN">Super Admin</option>
                                        <option value="VISITOR">Visitor</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="user-visibile" class="form-label">
                                        <i class="fas fa-eye me-1"></i>Stato
                                    </label>
                                    <select class="form-select" id="user-visibile" name="visibile">
                                        <option value="si">Attivo</option>
                                        <option value="no">Inattivo</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="row" id="password-section">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="user-password" class="form-label">
                                        <i class="fas fa-lock me-1"></i>Password *
                                    </label>
                                    <div class="input-group">
                                        <input type="password" class="form-control" id="user-password" name="password">
                                        <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('user-password')">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </div>
                                    <div class="form-text">Minimo 6 caratteri</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="user-confirm-password" class="form-label">
                                        <i class="fas fa-lock me-1"></i>Conferma Password *
                                    </label>
                                    <div class="input-group">
                                        <input type="password" class="form-control" id="user-confirm-password" name="confirm_password">
                                        <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('user-confirm-password')">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Note field removed as not present in current database schema -->
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-1"></i>Annulla
                    </button>
                    <button type="button" class="btn btn-admin" onclick="saveUser()">
                        <i class="fas fa-save me-1"></i>Salva Utente
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="/static/js/messages.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script src="/static/js/admin.js"></script>
    <script src="/static/js/user-management.js"></script>
    <script src="/static/js/config-management.js"></script>

    <!-- Script per gestione notifiche -->
    <script>
        // Funzioni per la sezione notifiche
        function openNotificationsPage() {
            window.open('/admin/notifiche', '_blank');
        }

        async function refreshNotificationsStats() {
            try {
                const response = await fetch('/admin/api/notifications');
                const data = await response.json();

                if (data.success && data.stats) {
                    document.getElementById('total-notifications').textContent = data.stats.total || 0;
                    document.getElementById('active-notifications').textContent = data.stats.active || 0;
                    document.getElementById('unread-notifications').textContent = data.stats.unread || 0;
                    document.getElementById('old-notifications').textContent = data.stats.old || 0;

                    // Aggiorna anche le notifiche recenti
                    updateRecentNotifications(data.notifications || []);

                    // Aggiorna grafico
                    updateNotificationsChart(data.notifications || []);
                }
            } catch (error) {
                console.error('Errore caricamento statistiche notifiche:', error);
            }
        }

        function updateRecentNotifications(notifications) {
            const container = document.getElementById('recent-notifications-list');

            if (!notifications || notifications.length === 0) {
                container.innerHTML = '<p class="text-muted text-center">Nessuna notifica recente</p>';
                return;
            }

            const recentNotifications = notifications.slice(0, 5);
            let html = '';

            recentNotifications.forEach(notif => {
                const date = new Date(notif.created_at).toLocaleDateString('it-IT');
                const typeIcon = getNotificationIcon(notif.type);
                const typeColor = getNotificationColor(notif.type);

                html += `
                    <div class="d-flex align-items-center mb-3 p-3 border rounded">
                        <div class="me-3">
                            <i class="fas ${typeIcon}" style="color: ${typeColor}; font-size: 1.2em;"></i>
                        </div>
                        <div class="flex-grow-1">
                            <h6 class="mb-1">${notif.title}</h6>
                            <p class="mb-1 text-muted small">${notif.message.substring(0, 100)}...</p>
                            <small class="text-muted">
                                📍 ${notif.reparto} • 📅 ${date} • 👤 ${notif.creator_name || 'Sistema'}
                            </small>
                        </div>
                        <div class="text-end">
                            <span class="badge bg-${typeColor.replace('#', '')}">${notif.type}</span>
                        </div>
                    </div>
                `;
            });

            container.innerHTML = html;
        }

        function getNotificationIcon(type) {
            const icons = {
                'INFO': 'fa-info-circle',
                'WARNING': 'fa-exclamation-triangle',
                'SUCCESS': 'fa-check-circle',
                'ERROR': 'fa-times-circle',
                'URGENT': 'fa-exclamation'
            };
            return icons[type] || 'fa-bell';
        }

        function getNotificationColor(type) {
            const colors = {
                'INFO': '#17a2b8',
                'WARNING': '#ffc107',
                'SUCCESS': '#28a745',
                'ERROR': '#dc3545',
                'URGENT': '#fd7e14'
            };
            return colors[type] || '#6c757d';
        }

        function updateNotificationsChart(notifications) {
            const ctx = document.getElementById('notificationsChart');
            if (!ctx) return;

            // Conta notifiche per reparto
            const repartoCount = {};
            notifications.forEach(notif => {
                repartoCount[notif.reparto] = (repartoCount[notif.reparto] || 0) + 1;
            });

            const labels = Object.keys(repartoCount);
            const data = Object.values(repartoCount);
            const colors = ['#667eea', '#764ba2', '#f093fb', '#f5576c', '#4facfe'];

            new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: labels,
                    datasets: [{
                        data: data,
                        backgroundColor: colors.slice(0, labels.length),
                        borderWidth: 2,
                        borderColor: '#fff'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });
        }

        function createQuickNotification() {
            Swal.fire({
                title: '🔔 Crea Notifica Rapida',
                html: `
                    <div class="text-start">
                        <div class="mb-3">
                            <label class="form-label">Titolo</label>
                            <input type="text" id="quick-title" class="form-control" placeholder="Titolo notifica">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Messaggio</label>
                            <textarea id="quick-message" class="form-control" rows="3" placeholder="Messaggio notifica"></textarea>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Reparto</label>
                            <select id="quick-reparto" class="form-control">
                                <option value="OPERATIVO">Operativo</option>
                                <option value="AMMINISTRAZIONE">Amministrazione</option>
                                <option value="CONTABILITA">Contabilità</option>
                                <option value="SHORTSEA">Shortsea</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Tipo</label>
                            <select id="quick-type" class="form-control">
                                <option value="INFO">Info</option>
                                <option value="WARNING">Avviso</option>
                                <option value="SUCCESS">Successo</option>
                                <option value="ERROR">Errore</option>
                                <option value="URGENT">Urgente</option>
                            </select>
                        </div>
                    </div>
                `,
                showCancelButton: true,
                confirmButtonText: 'Crea Notifica',
                cancelButtonText: 'Annulla',
                preConfirm: () => {
                    const title = document.getElementById('quick-title').value;
                    const message = document.getElementById('quick-message').value;
                    const reparto = document.getElementById('quick-reparto').value;
                    const type = document.getElementById('quick-type').value;

                    if (!title || !message) {
                        Swal.showValidationMessage('Titolo e messaggio sono obbligatori');
                        return false;
                    }

                    return { title, message, reparto, type };
                }
            }).then((result) => {
                if (result.isConfirmed) {
                    // Reindirizza alla pagina notifiche con parametri
                    const params = new URLSearchParams(result.value);
                    window.open(`/admin/notifiche?${params.toString()}`, '_blank');
                }
            });
        }

        function cleanupOldNotifications() {
            window.open('/admin/notifiche#cleanup', '_blank');
        }

        function deleteAllNotifications() {
            window.open('/admin/notifiche#delete-all', '_blank');
        }

        // Carica statistiche notifiche quando si apre la sezione
        document.addEventListener('DOMContentLoaded', function() {
            // Aggiungi listener per il tab notifiche
            const notificationsTab = document.querySelector('[data-section="notifications"]');
            if (notificationsTab) {
                notificationsTab.addEventListener('click', function() {
                    setTimeout(() => {
                        refreshNotificationsStats();
                    }, 100);
                });
            }
        });

        // ===== GESTIONE VERSIONE APPLICAZIONE =====

        // Carica la versione attuale all'avvio
        document.addEventListener('DOMContentLoaded', function() {
            loadCurrentVersion();
        });

        async function loadCurrentVersion() {
            try {
                const response = await fetch('/api/system/config');
                const data = await response.json();

                if (data.success && data.config) {
                    document.getElementById('app-version').value = data.config.app_version || 'v2.0.0';
                }
            } catch (error) {
                console.error('Errore caricamento versione:', error);
            }
        }

        async function updateAppVersion() {
            const versionInput = document.getElementById('app-version');
            const newVersion = versionInput.value.trim();

            if (!newVersion) {
                Swal.fire({
                    icon: 'error',
                    title: 'Errore',
                    text: 'Inserisci una versione valida'
                });
                return;
            }

            // Validazione formato versione (es. v2.0.0, 2.0.0, v2.0)
            const versionRegex = /^v?\d+\.\d+(\.\d+)?$/;
            if (!versionRegex.test(newVersion)) {
                Swal.fire({
                    icon: 'error',
                    title: 'Formato non valido',
                    text: 'Usa il formato: v2.0.0 o 2.0.0'
                });
                return;
            }

            // Assicurati che inizi con 'v'
            const formattedVersion = newVersion.startsWith('v') ? newVersion : 'v' + newVersion;

            try {
                const response = await fetch('/api/system/config', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        app_version: formattedVersion
                    })
                });

                const data = await response.json();

                if (data.success) {
                    // Aggiorna il campo con la versione formattata
                    versionInput.value = formattedVersion;

                    Swal.fire({
                        icon: 'success',
                        title: 'Versione aggiornata!',
                        text: `La versione è stata aggiornata a ${formattedVersion}. Ricarica la pagina per vedere il cambiamento nel logo.`,
                        showCancelButton: true,
                        confirmButtonText: 'Ricarica ora',
                        cancelButtonText: 'Ricarica dopo'
                    }).then((result) => {
                        if (result.isConfirmed) {
                            window.location.reload();
                        }
                    });
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'Errore',
                        text: data.error || 'Errore durante l\'aggiornamento'
                    });
                }
            } catch (error) {
                console.error('Errore aggiornamento versione:', error);
                Swal.fire({
                    icon: 'error',
                    title: 'Errore di rete',
                    text: 'Impossibile aggiornare la versione'
                });
            }
        }
    </script>

    <!-- Script per inizializzazione temi -->
    <script>
        // Inizializza il sistema dei temi quando la pagina è caricata
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🎨 Inizializzazione temi dashboard amministrazione...');

            // Il tema è già applicato dal server tramite la classe body
            const currentTheme = '{{ user_theme }}';
            console.log('🎯 Tema corrente dal server:', currentTheme);
            console.log('🎯 Tipo tema:', typeof currentTheme);

            // Debug: mostra il valore raw del tema
            console.log('🔍 Debug tema raw:', JSON.stringify('{{ user_theme }}'));

            // Verifica che la classe del tema sia applicata al body
            const body = document.body;
            const expectedClass = 'theme-' + currentTheme;

            console.log('🎯 Classe attesa:', expectedClass);
            console.log('📋 Classi body attuali:', body.className);
            console.log('✅ Ha classe attesa?', body.classList.contains(expectedClass));

            if (!body.classList.contains(expectedClass)) {
                console.log('🔧 Applicando classe tema mancante:', expectedClass);
                // Rimuovi tutte le classi tema esistenti
                body.classList.remove('theme-light', 'theme-dark', 'theme-maritime');
                // Applica la classe corretta
                body.classList.add(expectedClass);
                console.log('📋 Classi body dopo correzione:', body.className);
            }

            console.log('✅ Tema dashboard amministrazione inizializzato:', currentTheme);
        });
    </script>
</body>
</html>
