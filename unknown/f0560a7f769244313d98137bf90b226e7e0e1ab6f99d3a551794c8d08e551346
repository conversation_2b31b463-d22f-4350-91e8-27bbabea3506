{% extends "base_operativo.html" %}

{% block title %}Gestione Navi - M.A.P.{% endblock %}

{% block extra_css %}
<link href="/static/css/armatori.css" rel="stylesheet">
<style>
    /* ===== STILI PAGINAZIONE MIGLIORATI ===== */
    .modern-pagination-footer {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-top: 2px solid #dee2e6;
        padding: 1.5rem;
        border-radius: 0 0 20px 20px;
        box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.05);
    }

    .pagination-container {
        display: flex;
        justify-content: space-between;
        align-items: center;
        gap: 1rem;
        flex-wrap: wrap;
    }

    .pagination-info {
        display: flex;
        align-items: center;
    }

    .info-badge {
        background: rgba(102, 126, 234, 0.1);
        border: 1px solid rgba(102, 126, 234, 0.2);
        border-radius: 12px;
        padding: 8px 16px;
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 0.9rem;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    }

    .info-badge i {
        color: #667eea;
    }

    .info-text {
        color: #495057;
    }

    .info-text strong {
        color: #667eea;
        font-weight: 600;
    }

    .pagination-nav {
        flex: 1;
        display: flex;
        justify-content: center;
    }

    .modern-pagination {
        display: flex;
        gap: 4px;
        align-items: center;
    }

    .modern-pagination .page-item {
        margin: 0;
    }

    .modern-pagination .page-link {
        border-radius: 10px;
        border: 2px solid transparent;
        color: #667eea;
        font-weight: 500;
        padding: 8px 12px;
        min-width: 40px;
        text-align: center;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        background: white;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        text-decoration: none;
    }

    .modern-pagination .page-link:hover {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-color: #667eea;
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
    }

    .modern-pagination .page-item.active .page-link {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-color: #667eea;
        color: white;
        box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
        transform: translateY(-1px);
    }

    .modern-pagination .page-item.disabled .page-link {
        color: #adb5bd;
        background-color: #f8f9fa;
        border-color: #dee2e6;
        cursor: not-allowed;
        box-shadow: none;
    }

    .modern-pagination .page-item.disabled .page-link:hover {
        transform: none;
        background-color: #f8f9fa;
        color: #adb5bd;
    }

    .page-selector-container {
        display: flex;
        align-items: center;
    }

    .page-selector {
        display: flex;
        align-items: center;
        gap: 8px;
        background: white;
        border: 2px solid #dee2e6;
        border-radius: 12px;
        padding: 8px 12px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        transition: all 0.3s ease;
    }

    .page-selector-label {
        font-size: 0.85rem;
        color: #6c757d;
        font-weight: 500;
        margin: 0;
        white-space: nowrap;
    }

    .page-selector-label i {
        color: #667eea;
    }

    .modern-page-select {
        border: none;
        background: transparent;
        color: #495057;
        font-weight: 500;
        font-size: 0.9rem;
        padding: 4px 8px;
        min-width: 100px;
        cursor: pointer;
    }

    .modern-page-select:focus {
        outline: none;
        box-shadow: none;
    }

    .page-selector:hover {
        border-color: #667eea;
        box-shadow: 0 4px 8px rgba(102, 126, 234, 0.15);
        transform: translateY(-1px);
    }

    /* ===== RESPONSIVE DESIGN ===== */
    @media (max-width: 768px) {
        .pagination-container {
            flex-direction: column;
            gap: 1rem;
            text-align: center;
        }

        .pagination-info {
            order: 3;
        }

        .pagination-nav {
            order: 1;
        }

        .page-selector-container {
            order: 2;
        }

        .modern-pagination {
            justify-content: center;
            flex-wrap: wrap;
            gap: 2px;
        }

        .modern-pagination .page-link {
            padding: 6px 10px;
            font-size: 0.85rem;
            min-width: 35px;
        }

        .info-badge {
            font-size: 0.8rem;
            padding: 6px 12px;
        }
    }

    @media (max-width: 576px) {
        .modern-pagination-footer {
            padding: 1rem;
        }

        .modern-pagination .page-link {
            padding: 5px 8px;
            font-size: 0.8rem;
            min-width: 30px;
        }

        .page-selector {
            padding: 6px 10px;
        }

        .page-selector-label {
            font-size: 0.8rem;
        }

        .modern-page-select {
            font-size: 0.8rem;
            min-width: 80px;
        }
    }

    /* ===== TEMA SCURO ===== */
    body.theme-dark .modern-pagination-footer {
        background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
        border-top: 2px solid #495057;
    }

    body.theme-dark .info-badge {
        background: rgba(167, 139, 250, 0.15);
        border-color: rgba(167, 139, 250, 0.3);
    }

    body.theme-dark .info-badge i {
        color: #a78bfa;
    }

    body.theme-dark .info-text {
        color: #e2e8f0;
    }

    body.theme-dark .info-text strong {
        color: #a78bfa;
    }

    body.theme-dark .modern-pagination .page-link {
        background: rgba(30, 30, 46, 0.8);
        color: #a78bfa;
        border-color: rgba(255, 255, 255, 0.1);
    }

    body.theme-dark .modern-pagination .page-link:hover {
        background: linear-gradient(135deg, #a78bfa 0%, #c084fc 100%);
        border-color: #a78bfa;
        color: white;
    }

    body.theme-dark .modern-pagination .page-item.active .page-link {
        background: linear-gradient(135deg, #a78bfa 0%, #c084fc 100%);
        border-color: #a78bfa;
        color: white;
    }

    body.theme-dark .modern-pagination .page-item.disabled .page-link {
        background: rgba(30, 30, 46, 0.5);
        color: #6c757d;
        border-color: rgba(255, 255, 255, 0.05);
    }

    body.theme-dark .page-selector {
        background: rgba(30, 30, 46, 0.8);
        border-color: rgba(255, 255, 255, 0.1);
    }

    body.theme-dark .page-selector-label {
        color: #94a3b8;
    }

    body.theme-dark .page-selector-label i {
        color: #a78bfa;
    }

    body.theme-dark .modern-page-select {
        color: #e2e8f0;
    }

    body.theme-dark .page-selector:hover {
        border-color: #a78bfa;
    }

    /* ===== ANIMAZIONI ===== */
    @keyframes pageHover {
        0% { transform: translateY(0); }
        50% { transform: translateY(-3px); }
        100% { transform: translateY(-2px); }
    }

    .modern-pagination .page-link:hover {
        animation: pageHover 0.3s ease;
    }

    /* Miglioramenti visibilità */
    .modern-pagination .page-link {
        font-size: 0.95rem;
        font-weight: 600;
        letter-spacing: 0.5px;
    }

    .modern-pagination .page-item.active .page-link {
        font-weight: 700;
        box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4), 0 0 0 2px rgba(102, 126, 234, 0.2);
    }

    body.theme-dark .modern-pagination .page-item.active .page-link {
        box-shadow: 0 4px 12px rgba(167, 139, 250, 0.4), 0 0 0 2px rgba(167, 139, 250, 0.2);
    }
</style>
{% endblock %}

{% block content %}
        <!-- Header della pagina migliorato -->
        <div class="page-header text-center">
            <h2><i class="fas fa-ship me-3"></i>Gestione Navi</h2>
            <p class="subtitle">Gestisci tutte le navi del sistema M.A.P.</p>
        </div>

        <!-- Messaggio di errore -->
        {% if error %}
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            {{ error }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
        {% endif %}

        <!-- Messaggio di successo -->
        {% if success %}
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            {{ success }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
        {% endif %}



        <!-- Tabella delle navi -->
        <div class="card">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-start">
                    <div class="flex-grow-1 me-4">
                        <h5 class="mb-0"><i class="fas fa-list me-2"></i>Lista Navi</h5>
                        <small class="text-muted">
                            <i class="fas fa-ship me-1"></i>
                            {% if search_term %}
                            <span class="text-success">
                                <i class="fas fa-filter me-1"></i>
                                Trovate {{ pagination.total }} navi per "{{ search_term }}"
                            </span>
                            <br>
                            <span class="small">Mostrando {{ pagination.start_item }}-{{ pagination.end_item }} di {{ pagination.total }} risultati</span>
                            {% else %}
                            Mostrando {{ pagination.start_item }}-{{ pagination.end_item }} di {{ pagination.total }} navi totali
                            {% endif %}
                            <span class="badge bg-info ms-2">
                                <i class="fas fa-file-alt me-1"></i>Pagina {{ pagination.page }} di {{ pagination.total_pages }}
                            </span>
                        </small>

                        {% if search_term %}
                        <div class="mt-2">
                            <div class="alert alert-info py-2 mb-0">
                                <i class="fas fa-search me-2"></i>
                                <strong>Ricerca attiva:</strong> Stai visualizzando i risultati per "{{ search_term }}".
                                <a href="/operativo/navi" class="alert-link ms-2">
                                    <i class="fas fa-times me-1"></i>Cancella filtro
                                </a>
                            </div>
                        </div>
                        {% endif %}
                    </div>

                    <div class="d-flex flex-column align-items-end gap-2">
                        <button class="btn btn-success" data-bs-toggle="modal" data-bs-target="#addNaveModal">
                            <i class="fas fa-plus me-2"></i>Aggiungi Nuova Nave
                        </button>

                        <form method="GET" action="/operativo/navi" class="d-flex align-items-center gap-2">
                            <!-- Mantieni parametri esistenti -->
                            {% if request.query_params.get('success') %}
                            <input type="hidden" name="success" value="{{ request.query_params.get('success') }}">
                            {% endif %}

                            <div class="input-group" style="width: 350px;">
                                <span class="input-group-text">
                                    <i class="fas fa-search"></i>
                                </span>
                                <input type="text" class="form-control" name="search"
                                       value="{{ search_term }}"
                                       placeholder="Cerca navi..."
                                       title="Ricerca automatica dopo 2+ caratteri. Cerca in nome, codice, prefisso e armatore">
                                {% if search_term %}
                                <button type="button" class="btn btn-outline-secondary"
                                        onclick="clearSearch()" title="Cancella ricerca">
                                    <i class="fas fa-times"></i>
                                </button>
                                {% endif %}
                                <button type="submit" class="btn btn-primary" title="Cerca">
                                    <i class="fas fa-search me-1"></i>Cerca
                                </button>
                            </div>

                            <!-- Indicatore di caricamento -->
                            <div id="searchLoading" class="ms-2" style="display: none;">
                                <span class="text-primary">
                                    <i class="fas fa-spinner fa-spin me-1"></i>
                                    <small>Ricerca...</small>
                                </span>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th><i class="fas fa-ship me-2"></i>Nome Nave</th>
                                <th><i class="fas fa-barcode me-2"></i>Codice Nave</th>
                                <th><i class="fas fa-route me-2"></i>Prefisso Viaggio</th>
                                <th><i class="fas fa-euro-sign me-2"></i>Agemar (€)</th>
                                <th><i class="fas fa-building me-2"></i>Armatore</th>
                                <th class="text-center"><i class="fas fa-cogs me-2"></i>Azioni</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for nave in navi %}
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="avatar-circle me-3">
                                            <i class="fas fa-ship"></i>
                                        </div>
                                        <strong>{{ nave['nave'] }}</strong>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-secondary">{{ nave['codice_nave'] }}</span>
                                </td>
                                <td>
                                    <span class="badge bg-info">{{ nave['prefisso_viaggio'] }}</span>
                                </td>
                                <td>
                                    {% if nave['agemar'] %}
                                        <span class="text-success fw-bold">
                                            <i class="fas fa-euro-sign me-1"></i>{{ "%.2f"|format(nave['agemar']) }}
                                        </span>
                                    {% else %}
                                        <span class="text-muted">
                                            <i class="fas fa-minus me-1"></i>Non impostato
                                        </span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if nave['armatore_nome'] %}
                                        <span class="badge bg-primary">
                                            <i class="fas fa-building me-1"></i>{{ nave['armatore_nome'] }}
                                        </span>
                                    {% else %}
                                        <span class="text-muted">
                                            <i class="fas fa-user-slash me-1"></i>Non assegnato
                                        </span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="btn btn-sm btn-outline-primary" title="Modifica Nave"
                                                data-bs-toggle="modal" data-bs-target="#editNaveModal"
                                                data-original-code="{{ nave['codice_nave'] }}"
                                                data-nome="{{ nave['nave'] }}"
                                                data-codice="{{ nave['codice_nave'] }}"
                                                data-prefisso="{{ nave['prefisso_viaggio'] }}"
                                                data-agemar="{{ nave['agemar'] or '' }}"
                                                data-armatore-id="{{ nave['armatore_id'] or '' }}"
                                                onclick="editNaveFromData(this)">
                                            <i class="fas fa-edit me-1"></i>Modifica
                                        </button>
                                        <button class="btn btn-sm btn-outline-danger" title="Elimina Nave"
                                                data-bs-toggle="modal" data-bs-target="#deleteNaveModal"
                                                data-codice="{{ nave['codice_nave'] }}"
                                                data-nome="{{ nave['nave'] }}"
                                                onclick="deleteNaveFromData(this)">
                                            <i class="fas fa-trash me-1"></i>Elimina
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                            {% if not navi %}
                            <tr>
                                <td colspan="6" class="text-center py-5">
                                    <div class="text-muted">
                                        <i class="fas fa-ship fa-3x mb-3"></i>
                                        <h5>Nessuna nave trovata</h5>
                                        <p>Utilizza il pulsante verde in alto a destra per aggiungere la prima nave</p>
                                    </div>
                                </td>
                            </tr>
                            {% endif %}
                        </tbody>
                    </table>
                </div>

                <!-- Controlli di paginazione -->
                {% if pagination.total_pages > 1 %}
                <div class="card-footer modern-pagination-footer">
                    <div class="pagination-container">
                        <div class="pagination-info">
                            <div class="info-badge">
                                <i class="fas fa-info-circle me-1"></i>
                                <span class="info-text">
                                    Mostrando <strong>{{ pagination.start_item }}-{{ pagination.end_item }}</strong>
                                    di <strong>{{ pagination.total }}</strong> navi
                                </span>
                            </div>
                        </div>

                        <nav aria-label="Paginazione navi" class="pagination-nav">
                            <ul class="pagination modern-pagination mb-0">
                                <!-- Pulsante Prima Pagina -->
                                {% if pagination.has_prev %}
                                <li class="page-item">
                                    <a class="page-link" href="?page=1{% if search_term %}&search={{ search_term }}{% endif %}" title="Prima pagina">
                                        <i class="fas fa-angle-double-left"></i>
                                    </a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ pagination.prev_page }}{% if search_term %}&search={{ search_term }}{% endif %}" title="Pagina precedente">
                                        <i class="fas fa-angle-left"></i>
                                    </a>
                                </li>
                                {% else %}
                                <li class="page-item disabled">
                                    <span class="page-link">
                                        <i class="fas fa-angle-double-left"></i>
                                    </span>
                                </li>
                                <li class="page-item disabled">
                                    <span class="page-link">
                                        <i class="fas fa-angle-left"></i>
                                    </span>
                                </li>
                                {% endif %}

                                <!-- Numeri di pagina -->
                                {% set start_page = pagination.page - 2 if pagination.page - 2 > 1 else 1 %}
                                {% set end_page = pagination.page + 2 if pagination.page + 2 < pagination.total_pages else pagination.total_pages %}

                                {% if start_page > 1 %}
                                <li class="page-item">
                                    <a class="page-link" href="?page=1{% if search_term %}&search={{ search_term }}{% endif %}">1</a>
                                </li>
                                {% if start_page > 2 %}
                                <li class="page-item disabled">
                                    <span class="page-link">...</span>
                                </li>
                                {% endif %}
                                {% endif %}

                                {% for page_num in range(start_page, end_page + 1) %}
                                {% if page_num >= 1 and page_num <= pagination.total_pages %}
                                <li class="page-item {% if page_num == pagination.page %}active{% endif %}">
                                    <a class="page-link" href="?page={{ page_num }}{% if search_term %}&search={{ search_term }}{% endif %}">{{ page_num }}</a>
                                </li>
                                {% endif %}
                                {% endfor %}

                                {% if end_page < pagination.total_pages %}
                                {% if end_page < pagination.total_pages - 1 %}
                                <li class="page-item disabled">
                                    <span class="page-link">...</span>
                                </li>
                                {% endif %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ pagination.total_pages }}{% if search_term %}&search={{ search_term }}{% endif %}">{{ pagination.total_pages }}</a>
                                </li>
                                {% endif %}

                                <!-- Pulsante Ultima Pagina -->
                                {% if pagination.has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ pagination.next_page }}{% if search_term %}&search={{ search_term }}{% endif %}" title="Pagina successiva">
                                        <i class="fas fa-angle-right"></i>
                                    </a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ pagination.total_pages }}{% if search_term %}&search={{ search_term }}{% endif %}" title="Ultima pagina">
                                        <i class="fas fa-angle-double-right"></i>
                                    </a>
                                </li>
                                {% else %}
                                <li class="page-item disabled">
                                    <span class="page-link">
                                        <i class="fas fa-angle-right"></i>
                                    </span>
                                </li>
                                <li class="page-item disabled">
                                    <span class="page-link">
                                        <i class="fas fa-angle-double-right"></i>
                                    </span>
                                </li>
                                {% endif %}
                            </ul>
                        </nav>

                        <!-- Selettore rapido pagina -->
                        <div class="page-selector-container">
                            <div class="page-selector">
                                <label class="page-selector-label">
                                    <i class="fas fa-list-ol me-1"></i>
                                    Vai a:
                                </label>
                                <select class="form-select modern-page-select" onchange="navigateToPage(this.value)">
                                    {% if pagination.total_pages > 0 %}
                                    {% for page_num in range(1, pagination.total_pages + 1) %}
                                    <option value="{{ page_num }}" {% if page_num == pagination.page %}selected{% endif %}>
                                        Pagina {{ page_num }}
                                    </option>
                                    {% endfor %}
                                    {% else %}
                                    <option value="1" selected>Pagina 1</option>
                                    {% endif %}
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Modal Aggiungi Nave -->
    <div class="modal fade" id="addNaveModal" tabindex="-1">
        <div class="modal-dialog modal-lg modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-ship me-2"></i>Aggiungi Nave
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <form action="/operativo/navi/add" method="post">
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="nome_nave" class="form-label">
                                        <i class="fas fa-ship me-2"></i>Nome Nave
                                    </label>
                                    <input type="text" class="form-control" id="nome_nave" name="nome_nave"
                                           placeholder="Inserisci il nome della nave..." required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="codice_nave" class="form-label">
                                        <i class="fas fa-barcode me-2"></i>Codice Nave
                                    </label>
                                    <input type="text" class="form-control" id="codice_nave" name="codice_nave"
                                           placeholder="Es: MSC001" required>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="prefisso_viaggio" class="form-label">
                                        <i class="fas fa-route me-2"></i>Prefisso Viaggio
                                    </label>
                                    <input type="text" class="form-control" id="prefisso_viaggio" name="prefisso_viaggio"
                                           placeholder="Es: MSC" required>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="agemar" class="form-label">
                                        <i class="fas fa-euro-sign me-2"></i>Agemar (€)
                                    </label>
                                    <input type="number" step="0.01" class="form-control" id="agemar" name="agemar"
                                           placeholder="0.00" min="0">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="armatore_id" class="form-label">
                                        <i class="fas fa-building me-2"></i>Armatore
                                    </label>
                                    <select class="form-control" id="armatore_id" name="armatore_id">
                                        <option value="">Seleziona armatore...</option>
                                        {% for armatore in armatori %}
                                        <option value="{{ armatore.id }}">{{ armatore.Nome_Armatore }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>Nota:</strong> I campi Agemar e Armatore sono opzionali e possono essere compilati successivamente.
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                            <i class="fas fa-times me-2"></i>Annulla
                        </button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>Aggiungi Nave
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Modal Modifica Nave -->
    <div class="modal fade" id="editNaveModal" tabindex="-1">
        <div class="modal-dialog modal-lg modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-edit me-2"></i>Modifica Nave
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <form action="/operativo/navi/edit" method="post">
                    <div class="modal-body">
                        <input type="hidden" id="editNaveOriginalCode" name="original_codice_nave">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="editNomeNave" class="form-label">
                                        <i class="fas fa-ship me-2"></i>Nome Nave
                                    </label>
                                    <input type="text" class="form-control" id="editNomeNave" name="nome_nave" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="editCodiceNave" class="form-label">
                                        <i class="fas fa-barcode me-2"></i>Codice Nave
                                    </label>
                                    <input type="text" class="form-control" id="editCodiceNave" name="codice_nave" required>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="editPrefissoViaggio" class="form-label">
                                        <i class="fas fa-route me-2"></i>Prefisso Viaggio
                                    </label>
                                    <input type="text" class="form-control" id="editPrefissoViaggio" name="prefisso_viaggio" required>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="editAgemar" class="form-label">
                                        <i class="fas fa-euro-sign me-2"></i>Agemar (€)
                                    </label>
                                    <input type="number" step="0.01" class="form-control" id="editAgemar" name="agemar" min="0">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="editArmatoreId" class="form-label">
                                        <i class="fas fa-building me-2"></i>Armatore
                                    </label>
                                    <select class="form-control" id="editArmatoreId" name="armatore_id">
                                        <option value="">Seleziona armatore...</option>
                                        {% for armatore in armatori %}
                                        <option value="{{ armatore.id }}">{{ armatore.Nome_Armatore }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            Modifica i dati della nave e clicca "Salva Modifiche" per confermare.
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                            <i class="fas fa-times me-2"></i>Annulla
                        </button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>Salva Modifiche
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Modal Elimina Nave -->
    <div class="modal fade" id="deleteNaveModal" tabindex="-1">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header bg-danger text-white">
                    <h5 class="modal-title">
                        <i class="fas fa-trash me-2"></i>Elimina Nave
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <form action="/operativo/navi/delete" method="post">
                    <div class="modal-body">
                        <input type="hidden" id="deleteNaveCode" name="codice_nave">
                        <div class="text-center mb-4">
                            <i class="fas fa-exclamation-triangle text-warning fa-4x mb-3"></i>
                            <h5>Conferma Eliminazione</h5>
                            <p class="mb-0">Sei sicuro di voler eliminare la nave:</p>
                            <h6 class="text-primary mt-2" id="deleteNaveNome"></h6>
                            <small class="text-muted">Codice: <span id="deleteNaveCodeDisplay"></span></small>
                        </div>
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <strong>Attenzione!</strong> Questa azione non può essere annullata.
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                            <i class="fas fa-times me-2"></i>Annulla
                        </button>
                        <button type="submit" class="btn btn-danger">
                            <i class="fas fa-trash me-2"></i>Elimina Definitivamente
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/static/js/alerts.js"></script>
    <script>
        // Funzione per popolare il modal di modifica usando data attributes
        function editNaveFromData(button) {
            const originalCode = button.getAttribute('data-original-code');
            const nome = button.getAttribute('data-nome');
            const codice = button.getAttribute('data-codice');
            const prefisso = button.getAttribute('data-prefisso');
            const agemar = button.getAttribute('data-agemar');
            const armatoreId = button.getAttribute('data-armatore-id');

            document.getElementById('editNaveOriginalCode').value = originalCode;
            document.getElementById('editNomeNave').value = nome;
            document.getElementById('editCodiceNave').value = codice;
            document.getElementById('editPrefissoViaggio').value = prefisso;
            document.getElementById('editAgemar').value = agemar || '';
            document.getElementById('editArmatoreId').value = armatoreId || '';
        }

        // Funzione per popolare il modal di eliminazione usando data attributes
        function deleteNaveFromData(button) {
            const codice = button.getAttribute('data-codice');
            const nome = button.getAttribute('data-nome');

            document.getElementById('deleteNaveCode').value = codice;
            document.getElementById('deleteNaveNome').textContent = nome;
            document.getElementById('deleteNaveCodeDisplay').textContent = codice;
        }

        // Funzioni legacy per compatibilità con AJAX
        function editNave(originalCode, nome, codice, prefisso, agemar, armatoreId) {
            document.getElementById('editNaveOriginalCode').value = originalCode;
            document.getElementById('editNomeNave').value = nome;
            document.getElementById('editCodiceNave').value = codice;
            document.getElementById('editPrefissoViaggio').value = prefisso;
            document.getElementById('editAgemar').value = agemar || '';
            document.getElementById('editArmatoreId').value = armatoreId || '';
        }

        function deleteNave(codice, nome) {
            document.getElementById('deleteNaveCode').value = codice;
            document.getElementById('deleteNaveNome').textContent = nome;
            document.getElementById('deleteNaveCodeDisplay').textContent = codice;
        }

        // Variabili globali per la ricerca
        let searchTimeout;
        let currentSearchRequest;

        // Funzione per navigare a una pagina mantenendo la ricerca
        function navigateToPage(page) {
            const searchTerm = getCurrentSearchTerm();
            let url = '?page=' + page;
            if (searchTerm) {
                url += '&search=' + encodeURIComponent(searchTerm);
            }
            window.location.href = url;
        }

        // Funzione per cancellare la ricerca
        function clearSearch() {
            const searchInput = document.querySelector('input[name="search"]');
            if (searchInput) {
                searchInput.value = '';
            }
            window.location.href = '/operativo/navi';
        }

        // Funzione per ottenere il termine di ricerca corrente
        function getCurrentSearchTerm() {
            const searchInput = document.querySelector('input[name="search"]');
            return searchInput ? searchInput.value.trim() : '';
        }

        // Funzione per mostrare/nascondere l'indicatore di caricamento
        function showLoadingIndicator(show) {
            const loadingIndicator = document.getElementById('searchLoading');
            const searchButton = document.querySelector('button[type="submit"]');

            if (loadingIndicator) {
                loadingIndicator.style.display = show ? 'inline-block' : 'none';
            }

            if (searchButton) {
                if (show) {
                    searchButton.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Cercando...';
                    searchButton.disabled = true;
                } else {
                    searchButton.innerHTML = '<i class="fas fa-search me-1"></i>Cerca';
                    searchButton.disabled = false;
                }
            }
        }

        // Funzione per eseguire la ricerca in tempo reale
        function performRealTimeSearch(searchTerm, page = 1) {
            // Cancella richiesta precedente se in corso
            if (currentSearchRequest) {
                currentSearchRequest.abort();
            }

            // Mostra indicatore di caricamento
            showLoadingIndicator(true);

            // Prepara URL per la ricerca
            let url = '/operativo/navi?';
            const params = new URLSearchParams();

            if (searchTerm) {
                params.append('search', searchTerm);
            }
            params.append('page', page);
            params.append('ajax', '1'); // Flag per indicare richiesta AJAX

            url += params.toString();

            // Crea nuova richiesta
            currentSearchRequest = new XMLHttpRequest();
            currentSearchRequest.open('GET', url, true);

            currentSearchRequest.onreadystatechange = function() {
                if (this.readyState === 4) {
                    showLoadingIndicator(false);

                    if (this.status === 200) {
                        try {
                            const response = JSON.parse(this.responseText);
                            updateSearchResults(response);
                        } catch (e) {
                            // Se non è JSON, aggiorna la pagina intera
                            window.location.href = url.replace('&ajax=1', '');
                        }
                    } else {
                        console.error('Errore nella ricerca:', this.status);
                    }
                    currentSearchRequest = null;
                }
            };

            currentSearchRequest.send();
        }

        // Funzione per aggiornare i risultati di ricerca
        function updateSearchResults(data) {
            // Aggiorna la tabella
            const tbody = document.querySelector('tbody');
            if (tbody && data.html) {
                tbody.innerHTML = data.html;
            }

            // Aggiorna i contatori
            const naviCountElement = document.querySelector('.text-muted');
            if (naviCountElement && data.count_html) {
                naviCountElement.innerHTML = data.count_html;
            }

            // Aggiorna la paginazione
            const paginationContainer = document.querySelector('.card-footer');
            if (paginationContainer && data.pagination_html) {
                paginationContainer.innerHTML = data.pagination_html;
            }

            // Aggiorna l'URL senza ricaricare la pagina
            const newUrl = data.current_url || window.location.href;
            window.history.pushState({}, '', newUrl);
        }

        // Gestione ricerca in tempo reale
        const searchInput = document.querySelector('input[name="search"]');
        if (searchInput) {
            // Ricerca in tempo reale con debounce
            searchInput.addEventListener('input', function() {
                const searchTerm = this.value.trim();

                // Cancella timeout precedente
                clearTimeout(searchTimeout);

                // Se meno di 2 caratteri, mostra tutto
                if (searchTerm.length < 2) {
                    if (searchTerm.length === 0) {
                        // Campo vuoto, torna alla vista normale
                        searchTimeout = setTimeout(() => {
                            window.location.href = '/operativo/navi';
                        }, 300);
                    }
                    return;
                }

                // Debounce di 400ms
                searchTimeout = setTimeout(() => {
                    performRealTimeSearch(searchTerm);
                }, 400);
            });

            // Gestione ricerca con Enter (per compatibilità)
            searchInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    clearTimeout(searchTimeout);
                    const searchTerm = this.value.trim();
                    if (searchTerm.length >= 2) {
                        performRealTimeSearch(searchTerm);
                    } else if (searchTerm.length === 0) {
                        window.location.href = '/operativo/navi';
                    }
                }
            });

            // Focus automatico sul campo di ricerca se vuoto
            if (!searchInput.value.trim()) {
                searchInput.focus();
            }
        }

        // Gestione click pulsante cerca
        const searchForm = document.querySelector('form[action="/operativo/navi"]');
        if (searchForm) {
            searchForm.addEventListener('submit', function(e) {
                e.preventDefault();
                const searchTerm = getCurrentSearchTerm();
                if (searchTerm.length >= 2) {
                    performRealTimeSearch(searchTerm);
                } else if (searchTerm.length === 0) {
                    window.location.href = '/operativo/navi';
                } else {
                    alert('Inserisci almeno 2 caratteri per la ricerca');
                }
            });
        }

        // Aggiungi indicatore di caricamento per i link di paginazione
        document.querySelectorAll('.pagination .page-link').forEach(link => {
            link.addEventListener('click', function(e) {
                if (!this.closest('.page-item').classList.contains('disabled') &&
                    !this.closest('.page-item').classList.contains('active')) {

                    // Mostra indicatore di caricamento
                    const originalText = this.innerHTML;
                    this.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
                    this.style.pointerEvents = 'none';

                    // Ripristina dopo un breve delay (nel caso il caricamento sia veloce)
                    setTimeout(() => {
                        this.innerHTML = originalText;
                        this.style.pointerEvents = 'auto';
                    }, 3000);
                }
            });
        });
    </script>

{% endblock %}

{% block extra_js %}
<script src="/static/js/alerts.js"></script>
{% endblock %}