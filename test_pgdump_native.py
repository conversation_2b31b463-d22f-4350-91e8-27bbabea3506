#!/usr/bin/env python3
"""
Test backup con pg_dump nativo
"""

from backup_manager import BackupManager
import logging

# Configura logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

def test_pgdump_native():
    print("TEST BACKUP CON PG_DUMP NATIVO")
    print("=" * 50)
    
    try:
        # Inizializza backup manager
        db_url = "postgresql://re77:271077@localhost:5432/AGENTE"
        backup_manager = BackupManager(db_url)
        
        print("Test 1: Backup formato SQL (pg_dump nativo)...")
        
        # Crea backup in formato SQL usando pg_dump nativo
        backup_path = backup_manager.create_backup(format_type="sql")
        
        if backup_path:
            print(f"SUCCESS: Backup SQL creato: {backup_path}")
            
            # Verifica file
            from pathlib import Path
            backup_file = Path(backup_path)
            
            if backup_file.exists():
                file_size = backup_file.stat().st_size
                print(f"File size: {file_size / 1024:.1f} KB")
                
                # Verifica se è pg_dump nativo o SQLAlchemy
                if backup_file.suffix == '.gz':
                    import gzip
                    try:
                        with gzip.open(backup_file, 'rt', encoding='utf-8') as f:
                            first_lines = [f.readline().strip() for _ in range(10)]
                        
                        # Cerca indicatori pg_dump vs SQLAlchemy
                        is_pgdump = any("pg_dump" in line for line in first_lines)
                        is_sqlalchemy = any("SNIP Backup System" in line for line in first_lines)
                        
                        if is_pgdump:
                            print("METODO: pg_dump nativo utilizzato!")
                            print("Prime righe pg_dump:")
                            for i, line in enumerate(first_lines[:5], 1):
                                if line:
                                    print(f"  {i}: {line}")
                        elif is_sqlalchemy:
                            print("METODO: SQLAlchemy fallback utilizzato")
                            print("Prime righe SQLAlchemy:")
                            for i, line in enumerate(first_lines[:5], 1):
                                if line:
                                    print(f"  {i}: {line}")
                        else:
                            print("METODO: Non determinato")
                        
                    except Exception as e:
                        print(f"Errore lettura file: {e}")
                
                return True
            else:
                print("ERROR: File non esistente")
                return False
        else:
            print("ERROR: Backup fallito")
            return False
            
    except Exception as e:
        print(f"ERROR: {e}")
        return False

def test_pgdump_custom():
    print(f"\nTEST BACKUP FORMATO CUSTOM")
    print("=" * 40)
    
    try:
        # Inizializza backup manager
        db_url = "postgresql://re77:271077@localhost:5432/AGENTE"
        backup_manager = BackupManager(db_url)
        
        print("Test 2: Backup formato custom (pg_dump --format=custom)...")
        
        # Crea backup in formato custom
        backup_path = backup_manager.create_backup(format_type="custom")
        
        if backup_path:
            print(f"SUCCESS: Backup custom creato: {backup_path}")
            
            # Verifica file
            from pathlib import Path
            backup_file = Path(backup_path)
            
            if backup_file.exists():
                file_size = backup_file.stat().st_size
                print(f"File size: {file_size / 1024:.1f} KB")
                
                # Verifica header formato custom
                if backup_file.suffix == '.gz':
                    import gzip
                    try:
                        with gzip.open(backup_file, 'rb') as f:
                            header = f.read(20)
                        print(f"Header binario: {header[:10]}")
                        
                        # Verifica se è formato pg_dump custom
                        if b'PGDMP' in header:
                            print("FORMATO: pg_dump custom format (binario)")
                            print("COMPATIBILE: pg_restore")
                        else:
                            print("FORMATO: Non riconosciuto come pg_dump custom")
                        
                    except Exception as e:
                        print(f"Errore lettura header: {e}")
                
                print(f"\nCOMANDO IMPORTAZIONE:")
                if backup_file.suffix == '.gz':
                    print(f"1. gunzip {backup_file.name}")
                    print(f"2. pg_restore -d AGENTE {backup_file.stem}")
                else:
                    print(f"pg_restore -d AGENTE {backup_file.name}")
                
                return True
            else:
                print("ERROR: File non esistente")
                return False
        else:
            print("ERROR: Backup custom fallito")
            return False
            
    except Exception as e:
        print(f"ERROR: {e}")
        return False

def main():
    print("TEST PG_DUMP NATIVO - DATABASE AGENTE")
    print("=" * 60)
    
    # Test formato SQL
    sql_success = test_pgdump_native()
    
    # Test formato custom
    custom_success = test_pgdump_custom()
    
    print(f"\n" + "=" * 60)
    print("RISULTATI FINALI")
    print("=" * 60)
    
    print(f"Backup SQL (pg_dump): {'SUCCESS' if sql_success else 'FAILED'}")
    print(f"Backup Custom (pg_dump): {'SUCCESS' if custom_success else 'FAILED'}")
    
    if sql_success and custom_success:
        print(f"\nSUCCESS: ENTRAMBI I FORMATI FUNZIONANTI!")
        print("pg_dump nativo disponibile e funzionante")
        print()
        print("FORMATI DISPONIBILI:")
        print("1. SQL format (psql compatible)")
        print("   - Testo leggibile")
        print("   - Modificabile manualmente")
        print("   - Comando: psql -d AGENTE -f file.sql")
        print()
        print("2. Custom format (pg_restore compatible)")
        print("   - Formato binario compatto")
        print("   - Ripristino selettivo")
        print("   - Comando: pg_restore -d AGENTE file.dump")
        print()
        print("RACCOMANDAZIONE: Usa formato custom per backup automatici")
    elif sql_success:
        print(f"\nSUCCESS: Solo formato SQL disponibile")
        print("pg_dump funziona per formato SQL")
        print("Formato custom non disponibile")
    elif custom_success:
        print(f"\nSUCCESS: Solo formato custom disponibile")
        print("pg_dump funziona per formato custom")
        print("Formato SQL non disponibile")
    else:
        print(f"\nFAILED: pg_dump non funzionante")
        print("Fallback a SQLAlchemy necessario")
        print("Verificare installazione PostgreSQL")

if __name__ == "__main__":
    main()
