#!/usr/bin/env python3
"""
Sistema di Backup Automatico per SNIP
Gestisce backup automatici del database con invio email all'amministratore
"""

import os
import sys
import logging
import subprocess
import gzip
import shutil
from datetime import datetime, timedelta
from pathlib import Path
from typing import Optional, Dict, Any
import schedule
import time
import threading
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker

# Configurazione logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('backup_manager.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class BackupManager:
    """Gestisce i backup automatici del database"""
    
    def __init__(self, db_url: str, backup_dir: str = "backups"):
        self.db_url = db_url
        self.backup_dir = Path(backup_dir)
        self.backup_dir.mkdir(exist_ok=True)
        
        # Crea engine per database
        self.engine = create_engine(db_url)
        self.SessionLocal = sessionmaker(bind=self.engine)
        
        logger.info(f"[BACKUP] BackupManager inizializzato - Directory: {self.backup_dir}")
    
    def get_backup_config(self) -> Dict[str, Any]:
        """Ottiene configurazioni backup dal database"""
        try:
            with self.SessionLocal() as db:
                result = db.execute(text("""
                    SELECT config_key, config_value
                    FROM "SYSTEM_CONFIG"
                    WHERE config_key IN (
                        'backup_frequency', 'backup_retention', 'backup_time',
                        'backup_compress', 'email_admin_email', 'email_sender_email'
                    )
                """))
                
                config = {}
                for row in result:
                    key = row[0]
                    value = row[1]

                    # Gestisci mapping email
                    if key == 'email_admin_email':
                        key = 'admin_email'
                    elif key == 'email_sender_email':
                        key = 'sender_email'

                    # Converti valori
                    if key in ['backup_retention']:
                        config[key] = int(value) if value.isdigit() else 30
                    elif key == 'backup_compress':
                        config[key] = value.lower() in ['true', '1', 'yes']
                    else:
                        config[key] = value
                
                # Valori predefiniti
                config.setdefault('backup_frequency', 'daily')
                config.setdefault('backup_retention', 30)
                config.setdefault('backup_time', '02:00')
                config.setdefault('backup_compress', True)
                config.setdefault('admin_email', '')
                config.setdefault('sender_email', '')
                
                return config
                
        except Exception as e:
            logger.error(f"Errore lettura configurazioni backup: {e}")
            return {
                'backup_frequency': 'daily',
                'backup_retention': 30,
                'backup_time': '02:00',
                'backup_compress': True,
                'admin_email': '',
                'sender_email': ''
            }
    
    def create_backup(self) -> Optional[str]:
        """Crea backup del database"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_filename = f"snip_backup_{timestamp}.sql"
            backup_path = self.backup_dir / backup_filename
            
            logger.info(f"[BACKUP] Inizio backup database: {backup_filename}")
            
            # Estrai parametri connessione
            db_params = self._parse_db_url()
            
            # Comando pg_dump
            cmd = [
                'pg_dump',
                '-h', db_params['host'],
                '-p', str(db_params['port']),
                '-U', db_params['username'],
                '-d', db_params['database'],
                '-f', str(backup_path),
                '--verbose',
                '--no-password'
            ]
            
            # Imposta password come variabile ambiente
            env = os.environ.copy()
            env['PGPASSWORD'] = db_params['password']
            
            # Esegui backup
            result = subprocess.run(
                cmd,
                env=env,
                capture_output=True,
                text=True,
                timeout=3600  # 1 ora timeout
            )
            
            if result.returncode != 0:
                logger.error(f"Errore pg_dump: {result.stderr}")
                return None
            
            # Comprimi se richiesto
            config = self.get_backup_config()
            if config.get('backup_compress', True):
                compressed_path = backup_path.with_suffix('.sql.gz')
                with open(backup_path, 'rb') as f_in:
                    with gzip.open(compressed_path, 'wb') as f_out:
                        shutil.copyfileobj(f_in, f_out)
                
                # Rimuovi file non compresso
                backup_path.unlink()
                backup_path = compressed_path
                logger.info(f"[BACKUP] Backup compresso: {backup_path.name}")
            
            # Verifica dimensione file
            file_size = backup_path.stat().st_size
            if file_size == 0:
                logger.error("Backup vuoto generato!")
                backup_path.unlink()
                return None
            
            logger.info(f"[BACKUP] Backup completato: {backup_path.name} ({file_size / 1024 / 1024:.2f} MB)")
            
            # Pulisci backup vecchi
            self._cleanup_old_backups()
            
            # Invia notifica email
            self._send_backup_notification(backup_path, file_size)
            
            return str(backup_path)
            
        except subprocess.TimeoutExpired:
            logger.error("Timeout durante backup database")
            return None
        except Exception as e:
            logger.error(f"Errore durante backup: {e}")
            return None
    
    def _parse_db_url(self) -> Dict[str, str]:
        """Estrae parametri dalla URL database"""
        # Formato: postgresql://username:password@host:port/database
        from urllib.parse import urlparse
        
        parsed = urlparse(self.db_url)
        return {
            'host': parsed.hostname or 'localhost',
            'port': parsed.port or 5432,
            'username': parsed.username or 'postgres',
            'password': parsed.password or '',
            'database': parsed.path.lstrip('/') or 'snip'
        }
    
    def _cleanup_old_backups(self):
        """Rimuove backup vecchi secondo retention policy"""
        try:
            config = self.get_backup_config()
            retention_days = config.get('backup_retention', 30)
            
            cutoff_date = datetime.now() - timedelta(days=retention_days)
            
            removed_count = 0
            for backup_file in self.backup_dir.glob("snip_backup_*.sql*"):
                if backup_file.stat().st_mtime < cutoff_date.timestamp():
                    backup_file.unlink()
                    removed_count += 1
                    logger.info(f"[CLEANUP] Rimosso backup vecchio: {backup_file.name}")

            if removed_count > 0:
                logger.info(f"[CLEANUP] Pulizia completata: {removed_count} backup rimossi")
                
        except Exception as e:
            logger.error(f"Errore pulizia backup vecchi: {e}")
    
    def _send_backup_notification(self, backup_path: Path, file_size: int):
        """Invia notifica email del backup con file allegato all'amministratore"""
        try:
            config = self.get_backup_config()
            admin_email = config.get('admin_email')
            sender_email = config.get('sender_email')

            if not admin_email:
                logger.warning("Email amministratore non configurata - notifica non inviata")
                return

            # Importa funzione email dal main
            sys.path.append('.')
            from main import send_email

            subject = f"📦 Backup Database SNIP - {datetime.now().strftime('%d/%m/%Y %H:%M')}"

            body = f"""
Gentile Amministratore,

Il backup automatico del database SNIP è stato completato con successo.

📊 DETTAGLI BACKUP:
• Data/Ora: {datetime.now().strftime('%d/%m/%Y alle %H:%M:%S')}
• File: {backup_path.name}
• Dimensione: {file_size / 1024 / 1024:.2f} MB
• Compressione: {'Attiva' if backup_path.suffix == '.gz' else 'Disattiva'}

📋 CONFIGURAZIONE BACKUP:
• Frequenza: {config.get('backup_frequency', 'daily')}
• Retention: {config.get('backup_retention', 30)} giorni
• Orario programmato: {config.get('backup_time', '02:00')}

📎 ALLEGATO:
Il file di backup SQL è allegato a questa email e può essere utilizzato per:
• Ripristino completo del database AGENTE
• Backup di sicurezza offline
• Migrazione dati su altro server

📧 DESTINATARI:
• Email Admin: {admin_email}
• Email Mittente (CC): {sender_email if sender_email else 'Non configurata'}

⚠️ IMPORTANTE:
Conservare questo file in luogo sicuro. Contiene tutti i dati del sistema SNIP.

---
Sistema di Backup Automatico SNIP
Michele Autuori Srl - shipping and forwarding agency
            """

            # Invia email con allegato
            with self.SessionLocal() as db:
                success = send_email(
                    to_email=admin_email,
                    subject=subject,
                    body=body,
                    db=db,
                    attachment_path=str(backup_path),
                    cc_email=sender_email if sender_email else None
                )
                if success:
                    recipients = f"{admin_email}"
                    if sender_email:
                        recipients += f" (CC: {sender_email})"
                    logger.info(f"[EMAIL] Backup inviato con allegato a: {recipients}")
                    logger.info(f"[EMAIL] File allegato: {backup_path.name} ({file_size / 1024 / 1024:.2f} MB)")
                else:
                    logger.warning("Errore invio email backup con allegato")

        except Exception as e:
            logger.error(f"Errore invio notifica backup: {e}")
    
    def schedule_backups(self):
        """Programma backup automatici"""
        try:
            config = self.get_backup_config()
            frequency = config.get('backup_frequency', 'daily')
            backup_time = config.get('backup_time', '02:00')
            
            # Cancella schedule precedenti
            schedule.clear()
            
            if frequency == 'hourly':
                schedule.every().hour.do(self.create_backup)
                logger.info("📅 Backup programmati: ogni ora")
            elif frequency == 'daily':
                schedule.every().day.at(backup_time).do(self.create_backup)
                logger.info(f"[SCHEDULE] Backup programmati: giornalieri alle {backup_time}")
            elif frequency == 'weekly':
                schedule.every().monday.at(backup_time).do(self.create_backup)
                logger.info(f"[SCHEDULE] Backup programmati: settimanali (lunedì alle {backup_time})")
            elif frequency == 'monthly':
                schedule.every().month.do(self.create_backup)
                logger.info("[SCHEDULE] Backup programmati: mensili")
            else:
                logger.info("[SCHEDULE] Backup automatici disabilitati")
                return

            logger.info("[SCHEDULE] Scheduler backup configurato")
            
        except Exception as e:
            logger.error(f"Errore configurazione scheduler: {e}")
    
    def run_scheduler(self):
        """Esegue lo scheduler in background"""
        logger.info("[SCHEDULER] Avvio scheduler backup...")

        while True:
            try:
                schedule.run_pending()
                time.sleep(60)  # Controlla ogni minuto
            except KeyboardInterrupt:
                logger.info("[SCHEDULER] Scheduler backup fermato")
                break
            except Exception as e:
                logger.error(f"Errore scheduler: {e}")
                time.sleep(300)  # Aspetta 5 minuti prima di riprovare

def start_backup_service(db_url: str):
    """Avvia il servizio di backup in background"""
    backup_manager = BackupManager(db_url)
    backup_manager.schedule_backups()
    
    # Esegui in thread separato
    scheduler_thread = threading.Thread(
        target=backup_manager.run_scheduler,
        daemon=True,
        name="BackupScheduler"
    )
    scheduler_thread.start()
    
    logger.info("[SERVICE] Servizio backup avviato in background")
    return backup_manager

if __name__ == "__main__":
    # Test standalone
    db_url = "postgresql://snip_user:snip_password@localhost:5432/snip"
    backup_manager = BackupManager(db_url)
    
    # Test backup manuale
    result = backup_manager.create_backup()
    if result:
        print(f"[SUCCESS] Backup creato: {result}")
    else:
        print("[ERROR] Errore creazione backup")
