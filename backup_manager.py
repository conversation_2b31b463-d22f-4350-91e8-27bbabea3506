#!/usr/bin/env python3
"""
Sistema di Backup Automatico per SNIP
Gestisce backup automatici del database con invio email all'amministratore
"""

import os
import sys
import logging
import subprocess
import gzip
import shutil
from datetime import datetime, timedelta
from pathlib import Path
from typing import Optional, Dict, Any
import schedule
import time
import threading
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker

# Configurazione logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('backup_manager.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class BackupManager:
    """Gestisce i backup automatici del database"""
    
    def __init__(self, db_url: str, backup_dir: str = "backups"):
        self.db_url = db_url
        self.backup_dir = Path(backup_dir)
        self.backup_dir.mkdir(exist_ok=True)
        
        # Crea engine per database
        self.engine = create_engine(db_url)
        self.SessionLocal = sessionmaker(bind=self.engine)
        
        logger.info(f"[BACKUP] BackupManager inizializzato - Directory: {self.backup_dir}")
    
    def get_backup_config(self) -> Dict[str, Any]:
        """Ottiene configurazioni backup dal database"""
        try:
            with self.SessionLocal() as db:
                result = db.execute(text("""
                    SELECT config_key, config_value
                    FROM "SYSTEM_CONFIG"
                    WHERE config_key IN (
                        'backup_frequency', 'backup_retention', 'backup_time',
                        'backup_compress', 'email_admin_email', 'email_sender_email'
                    )
                """))
                
                config = {}
                for row in result:
                    key = row[0]
                    value = row[1]

                    # Gestisci mapping email
                    if key == 'email_admin_email':
                        key = 'admin_email'
                    elif key == 'email_sender_email':
                        key = 'sender_email'

                    # Converti valori
                    if key in ['backup_retention']:
                        config[key] = int(value) if value.isdigit() else 30
                    elif key == 'backup_compress':
                        config[key] = value.lower() in ['true', '1', 'yes']
                    else:
                        config[key] = value
                
                # Valori predefiniti
                config.setdefault('backup_frequency', 'daily')
                config.setdefault('backup_retention', 30)
                config.setdefault('backup_time', '02:00')
                config.setdefault('backup_compress', True)
                config.setdefault('admin_email', '')
                config.setdefault('sender_email', '')
                
                return config
                
        except Exception as e:
            logger.error(f"Errore lettura configurazioni backup: {e}")
            return {
                'backup_frequency': 'daily',
                'backup_retention': 30,
                'backup_time': '02:00',
                'backup_compress': True,
                'admin_email': '',
                'sender_email': ''
            }
    
    def create_backup(self, format_type: str = "sql") -> Optional[str]:
        """Crea backup del database

        Args:
            format_type: "sql" per formato testo (psql), "custom" per formato binario (pg_restore)
        """
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

            if format_type == "custom":
                backup_filename = f"snip_backup_{timestamp}.dump"
            else:
                backup_filename = f"snip_backup_{timestamp}.sql"

            backup_path = self.backup_dir / backup_filename

            logger.info(f"[BACKUP] Inizio backup database: {backup_filename} (formato: {format_type})")

            # Scegli metodo backup in base al formato
            if format_type == "custom":
                backup_success = self._try_pg_dump_custom(backup_path)
            else:
                # Prova prima pg_dump, poi fallback a backup SQLAlchemy
                backup_success = self._try_pg_dump_backup(backup_path) or self._try_sqlalchemy_backup(backup_path)

            if not backup_success:
                logger.error("Tutti i metodi di backup sono falliti")
                return None
            
            # Comprimi se richiesto
            config = self.get_backup_config()
            if config.get('backup_compress', True):
                compressed_path = backup_path.with_suffix('.sql.gz')
                with open(backup_path, 'rb') as f_in:
                    with gzip.open(compressed_path, 'wb') as f_out:
                        shutil.copyfileobj(f_in, f_out)
                
                # Rimuovi file non compresso
                backup_path.unlink()
                backup_path = compressed_path
                logger.info(f"[BACKUP] Backup compresso: {backup_path.name}")
            
            # Verifica dimensione file
            file_size = backup_path.stat().st_size
            if file_size == 0:
                logger.error("Backup vuoto generato!")
                backup_path.unlink()
                return None
            
            logger.info(f"[BACKUP] Backup completato: {backup_path.name} ({file_size / 1024 / 1024:.2f} MB)")
            
            # Pulisci backup vecchi
            self._cleanup_old_backups()
            
            # Invia notifica email
            self._send_backup_notification(backup_path, file_size)
            
            return str(backup_path)

        except Exception as e:
            logger.error(f"Errore durante backup: {e}")
            return None

    def _try_pg_dump_backup(self, backup_path: Path) -> bool:
        """Prova backup con pg_dump"""
        try:
            # Estrai parametri connessione
            db_params = self._parse_db_url()

            # Trova pg_dump nel sistema
            pg_dump_paths = [
                'pg_dump',  # Se è nel PATH
                r'C:\Program Files\PostgreSQL\17\bin\pg_dump.exe',
                r'C:\Program Files\PostgreSQL\16\bin\pg_dump.exe',
                r'C:\Program Files\PostgreSQL\15\bin\pg_dump.exe',
                r'C:\Program Files (x86)\PostgreSQL\17\bin\pg_dump.exe'
            ]

            pg_dump_exe = None
            for path in pg_dump_paths:
                try:
                    if path == 'pg_dump':
                        # Test se è nel PATH
                        subprocess.run([path, '--version'], capture_output=True, timeout=5)
                        pg_dump_exe = path
                        break
                    else:
                        # Test percorso completo
                        from pathlib import Path as PathLib
                        if PathLib(path).exists():
                            pg_dump_exe = path
                            break
                except:
                    continue

            if not pg_dump_exe:
                logger.warning("pg_dump non trovato nel sistema")
                return False

            # Comando pg_dump
            cmd = [
                pg_dump_exe,
                '-h', db_params['host'],
                '-p', str(db_params['port']),
                '-U', db_params['username'],
                '-d', db_params['database'],
                '-f', str(backup_path),
                '--verbose',
                '--no-password'
            ]

            # Imposta password come variabile ambiente
            env = os.environ.copy()
            env['PGPASSWORD'] = db_params['password']

            # Esegui backup
            result = subprocess.run(
                cmd,
                env=env,
                capture_output=True,
                text=True,
                timeout=3600  # 1 ora timeout
            )

            if result.returncode == 0:
                logger.info("[BACKUP] pg_dump completato con successo")
                return True
            else:
                logger.warning(f"pg_dump fallito: {result.stderr}")
                return False

        except FileNotFoundError:
            logger.warning("pg_dump non trovato, uso backup alternativo")
            return False
        except subprocess.TimeoutExpired:
            logger.error("Timeout pg_dump")
            return False
        except Exception as e:
            logger.warning(f"Errore pg_dump: {e}")
            return False

    def _try_pg_dump_custom(self, backup_path: Path) -> bool:
        """Prova backup con pg_dump in formato custom (per pg_restore)"""
        try:
            # Estrai parametri connessione
            db_params = self._parse_db_url()

            # Trova pg_dump nel sistema
            pg_dump_paths = [
                'pg_dump',  # Se è nel PATH
                r'C:\Program Files\PostgreSQL\17\bin\pg_dump.exe',
                r'C:\Program Files\PostgreSQL\16\bin\pg_dump.exe',
                r'C:\Program Files\PostgreSQL\15\bin\pg_dump.exe',
                r'C:\Program Files (x86)\PostgreSQL\17\bin\pg_dump.exe'
            ]

            pg_dump_exe = None
            for path in pg_dump_paths:
                try:
                    if path == 'pg_dump':
                        # Test se è nel PATH
                        subprocess.run([path, '--version'], capture_output=True, timeout=5)
                        pg_dump_exe = path
                        break
                    else:
                        # Test percorso completo
                        from pathlib import Path as PathLib
                        if PathLib(path).exists():
                            pg_dump_exe = path
                            break
                except:
                    continue

            if not pg_dump_exe:
                logger.warning("pg_dump non trovato per formato custom")
                return False

            # Comando pg_dump con formato custom
            cmd = [
                pg_dump_exe,
                '-h', db_params['host'],
                '-p', str(db_params['port']),
                '-U', db_params['username'],
                '-d', db_params['database'],
                '-f', str(backup_path),
                '--format=custom',  # Formato custom per pg_restore
                '--verbose',
                '--no-password'
            ]

            # Imposta password come variabile ambiente
            env = os.environ.copy()
            env['PGPASSWORD'] = db_params['password']

            # Esegui backup
            result = subprocess.run(
                cmd,
                env=env,
                capture_output=True,
                text=True,
                timeout=3600  # 1 ora timeout
            )

            if result.returncode == 0:
                logger.info("[BACKUP] pg_dump custom completato con successo")
                return True
            else:
                logger.warning(f"pg_dump custom fallito: {result.stderr}")
                return False

        except FileNotFoundError:
            logger.warning("pg_dump non trovato per formato custom")
            return False
        except subprocess.TimeoutExpired:
            logger.error("Timeout pg_dump custom")
            return False
        except Exception as e:
            logger.warning(f"Errore pg_dump custom: {e}")
            return False

    def _try_sqlalchemy_backup(self, backup_path: Path) -> bool:
        """Backup alternativo usando SQLAlchemy - formato pg_dump"""
        try:
            logger.info("[BACKUP] Uso backup SQLAlchemy (formato pg_dump)")

            with open(backup_path, 'w', encoding='utf-8') as f:
                # Header PostgreSQL
                self._write_pgdump_header(f)

                # Schema database
                self._write_database_schema(f)

                # Dati tabelle (UNA SOLA VOLTA)
                self._write_all_table_data_once(f)

                # Footer PostgreSQL standard
                f.write("\n--\n-- PostgreSQL database dump complete\n--\n\n")

            logger.info("[BACKUP] Backup SQLAlchemy completato")
            return True

        except Exception as e:
            logger.error(f"Errore backup SQLAlchemy: {e}")
            return False

    def _write_pgdump_header(self, f):
        """Scrive header PostgreSQL valido per importazione"""
        # Header commentato per compatibilità psql
        f.write("--\n")
        f.write("-- PostgreSQL database dump\n")
        f.write("--\n\n")

        # Configurazioni PostgreSQL standard
        f.write("-- Dumped from database version 17.5\n")
        f.write("-- Dumped by SNIP Backup System\n\n")

        f.write("SET statement_timeout = 0;\n")
        f.write("SET lock_timeout = 0;\n")
        f.write("SET idle_in_transaction_session_timeout = 0;\n")
        f.write("SET client_encoding = 'UTF8';\n")
        f.write("SET standard_conforming_strings = on;\n")
        f.write("SELECT pg_catalog.set_config('search_path', '', false);\n")
        f.write("SET check_function_bodies = false;\n")
        f.write("SET xmloption = content;\n")
        f.write("SET client_min_messages = warning;\n")
        f.write("SET row_security = off;\n\n")

        # CREATE TYPE statements per enum (formato PostgreSQL valido)
        f.write("--\n-- Name: notificationtypeenum; Type: TYPE; Schema: public; Owner: re77\n--\n\n")
        f.write("CREATE TYPE public.notificationtypeenum AS ENUM (\n")
        f.write("    'INFO',\n")
        f.write("    'WARNING',\n")
        f.write("    'SUCCESS',\n")
        f.write("    'ERROR',\n")
        f.write("    'URGENT'\n")
        f.write(");\n\n")
        f.write("ALTER TYPE public.notificationtypeenum OWNER TO re77;\n\n")

        f.write("--\n-- Name: repartoenum; Type: TYPE; Schema: public; Owner: re77\n--\n\n")
        f.write("CREATE TYPE public.repartoenum AS ENUM (\n")
        f.write("    'OPERATIVO',\n")
        f.write("    'AMMINISTRAZIONE',\n")
        f.write("    'SHORTSEA',\n")
        f.write("    'CONTABILITA'\n")
        f.write(");\n\n")
        f.write("ALTER TYPE public.repartoenum OWNER TO re77;\n\n")

        f.write("--\n-- Name: ruolo_enum; Type: TYPE; Schema: public; Owner: re77\n--\n\n")
        f.write("CREATE TYPE public.ruolo_enum AS ENUM (\n")
        f.write("    'ADMIN',\n")
        f.write("    'SUPER_ADMIN',\n")
        f.write("    'USER',\n")
        f.write("    'VISITOR'\n")
        f.write(");\n\n")
        f.write("ALTER TYPE public.ruolo_enum OWNER TO re77;\n\n")

        f.write("--\n-- Name: ruoloenum; Type: TYPE; Schema: public; Owner: re77\n--\n\n")
        f.write("CREATE TYPE public.ruoloenum AS ENUM (\n")
        f.write("    'ADMIN',\n")
        f.write("    'SUPER_ADMIN',\n")
        f.write("    'USER',\n")
        f.write("    'VISITOR'\n")
        f.write(");\n\n")
        f.write("ALTER TYPE public.ruoloenum OWNER TO re77;\n\n")

        f.write("--\n-- Name: temaenum; Type: TYPE; Schema: public; Owner: re77\n--\n\n")
        f.write("CREATE TYPE public.temaenum AS ENUM (\n")
        f.write("    'light',\n")
        f.write("    'dark',\n")
        f.write("    'maritime'\n")
        f.write(");\n\n")
        f.write("ALTER TYPE public.temaenum OWNER TO re77;\n\n")

    def _write_database_schema(self, f):
        """Scrive schema database completo con CREATE TABLE"""
        f.write("--\n-- Database schema\n--\n\n")

        try:
            with self.SessionLocal() as db:
                # Ottieni tutte le tabelle
                tables_query = text("""
                    SELECT table_name
                    FROM information_schema.tables
                    WHERE table_schema = 'public'
                    AND table_type = 'BASE TABLE'
                    ORDER BY table_name
                """)

                result = db.execute(tables_query)
                tables = [row[0] for row in result.fetchall()]

                # Prima crea le sequenze
                self._write_sequences(f, db)

                # Poi genera CREATE TABLE per ogni tabella
                for table_name in tables:
                    self._write_create_table(f, table_name, db)

                # Infine imposta i default delle sequenze
                self._write_sequence_defaults(f, db)

        except Exception as e:
            logger.error(f"Errore scrittura schema: {e}")
            f.write("-- ERROR: Could not generate schema\n\n")

    def _write_create_table(self, f, table_name: str, db):
        """Genera CREATE TABLE statement per una tabella"""
        try:
            # Query per ottenere struttura tabella
            columns_query = text("""
                SELECT
                    column_name,
                    data_type,
                    character_maximum_length,
                    is_nullable,
                    column_default,
                    udt_name
                FROM information_schema.columns
                WHERE table_name = :table_name
                AND table_schema = 'public'
                ORDER BY ordinal_position
            """)

            result = db.execute(columns_query, {"table_name": table_name})
            columns = result.fetchall()

            if not columns:
                return

            f.write(f"--\n-- Name: {table_name}; Type: TABLE; Schema: public; Owner: re77\n--\n\n")
            f.write(f'CREATE TABLE public."{table_name}" (\n')

            column_definitions = []
            for col in columns:
                col_name, data_type, max_length, nullable, default, udt_name = col

                # Costruisci definizione colonna
                col_def = f'    "{col_name}"'

                # Tipo di dato
                if data_type == 'character varying':
                    if max_length:
                        col_def += f' character varying({max_length})'
                    else:
                        col_def += ' character varying'
                elif data_type == 'USER-DEFINED':
                    col_def += f' public.{udt_name}'
                elif data_type == 'integer':
                    col_def += ' integer'
                elif data_type == 'numeric':
                    col_def += ' numeric(10,2)'
                elif data_type == 'timestamp without time zone':
                    col_def += ' timestamp without time zone'
                elif data_type == 'date':
                    col_def += ' date'
                elif data_type == 'boolean':
                    col_def += ' boolean'
                elif data_type == 'text':
                    col_def += ' text'
                elif data_type == 'jsonb':
                    col_def += ' jsonb'
                else:
                    col_def += f' {data_type}'

                # NOT NULL
                if nullable == 'NO':
                    col_def += ' NOT NULL'

                # DEFAULT
                if default:
                    if 'nextval' in default:
                        # Sequence default
                        col_def += f' DEFAULT {default}'
                    elif default.startswith("'"):
                        # String default
                        col_def += f' DEFAULT {default}'
                    else:
                        # Other defaults
                        col_def += f' DEFAULT {default}'

                column_definitions.append(col_def)

            f.write(',\n'.join(column_definitions))
            f.write('\n);\n\n')
            f.write(f'ALTER TABLE public."{table_name}" OWNER TO re77;\n\n')

        except Exception as e:
            logger.error(f"Errore CREATE TABLE per {table_name}: {e}")
            f.write(f"-- ERROR creating table {table_name}: {str(e)}\n\n")

    def _write_sequences(self, f, db):
        """Crea le sequenze necessarie"""
        try:
            # Query per trovare tutte le sequenze esistenti nel database
            sequences_query = text("""
                SELECT sequence_name
                FROM information_schema.sequences
                WHERE sequence_schema = 'public'
                ORDER BY sequence_name
            """)

            result = db.execute(sequences_query)
            sequences = result.fetchall()

            if sequences:
                f.write("--\n-- Sequences\n--\n\n")

                for seq_row in sequences:
                    seq_name = seq_row[0]  # Solo il nome della sequenza
                    if seq_name:  # Verifica che il nome sequenza non sia None
                        # Ottieni il valore corrente della sequenza
                        try:
                            current_val_query = text(f"SELECT last_value FROM \"{seq_name}\"")
                            current_val_result = db.execute(current_val_query)
                            current_val = current_val_result.scalar() or 1
                        except:
                            current_val = 1

                        f.write(f"--\n-- Name: {seq_name}; Type: SEQUENCE; Schema: public; Owner: re77\n--\n\n")
                        f.write(f'CREATE SEQUENCE public."{seq_name}"\n')
                        f.write("    AS integer\n")
                        f.write(f"    START WITH {current_val}\n")
                        f.write("    INCREMENT BY 1\n")
                        f.write("    NO MINVALUE\n")
                        f.write("    NO MAXVALUE\n")
                        f.write("    CACHE 1;\n\n")
                        f.write(f'ALTER SEQUENCE public."{seq_name}" OWNER TO re77;\n\n')

                        # Imposta il valore corrente
                        f.write(f"SELECT pg_catalog.setval('public.\"{seq_name}\"', {current_val}, true);\n\n")

                        logger.info(f"[BACKUP] Sequenza {seq_name}: valore corrente {current_val}")
            else:
                f.write("-- No sequences found\n\n")

        except Exception as e:
            logger.error(f"Errore creazione sequenze: {e}")
            f.write("-- ERROR creating sequences\n\n")

    def _write_sequence_defaults(self, f, db):
        """Imposta i default delle sequenze per le colonne"""
        try:
            # Query per trovare colonne con sequenze
            defaults_query = text("""
                SELECT
                    table_name,
                    column_name,
                    column_default
                FROM information_schema.columns
                WHERE table_schema = 'public'
                AND column_default LIKE 'nextval%'
                ORDER BY table_name, column_name
            """)

            result = db.execute(defaults_query)
            defaults = result.fetchall()

            for default in defaults:
                table_name, column_name, column_default = default
                f.write(f'ALTER TABLE ONLY public."{table_name}" ALTER COLUMN {column_name} SET DEFAULT {column_default};\n')

            if defaults:
                f.write('\n')

        except Exception as e:
            logger.error(f"Errore impostazione default sequenze: {e}")

    def _write_all_table_data_once(self, f):
        """Scrive dati di tutte le tabelle UNA SOLA VOLTA"""
        f.write("--\n-- Data for Name: tables; Type: TABLE DATA; Schema: public; Owner: re77\n--\n\n")

        try:
            with self.SessionLocal() as db:
                # Ottieni lista di tutte le tabelle
                tables_query = text("""
                    SELECT table_name
                    FROM information_schema.tables
                    WHERE table_schema = 'public'
                    AND table_type = 'BASE TABLE'
                    ORDER BY table_name
                """)

                result = db.execute(tables_query)
                all_tables = [row[0] for row in result.fetchall()]

                logger.info(f"[BACKUP] Backup di {len(all_tables)} tabelle")

                # Backup ogni tabella UNA SOLA VOLTA (usa INSERT invece di COPY)
                for table_name in all_tables:
                    self._backup_single_table_insert(f, table_name)

        except Exception as e:
            logger.error(f"Errore backup tabelle: {e}")

    def _backup_single_table_copy(self, file_handle, table_name: str):
        """Backup di una singola tabella in formato COPY"""
        try:
            with self.SessionLocal() as db:
                # Query dati tabella
                result = db.execute(text(f'SELECT * FROM "{table_name}"'))
                rows = result.fetchall()
                columns = result.keys()

                # Header COPY
                file_handle.write(f"--\n-- Data for Name: {table_name}; Type: TABLE DATA; Schema: public; Owner: re77\n--\n\n")

                columns_str = ', '.join([f'"{col}"' for col in columns])
                file_handle.write(f'COPY public."{table_name}" ({columns_str}) FROM stdin;\n')

                # Dati
                if rows:
                    for row in rows:
                        values = []
                        for value in row:
                            if value is None:
                                values.append('\\N')
                            elif isinstance(value, str):
                                # Escape completo per formato COPY PostgreSQL
                                escaped_value = (value
                                    .replace('\\', '\\\\')  # Backslash
                                    .replace('\t', '\\t')   # Tab
                                    .replace('\n', '\\n')   # Newline
                                    .replace('\r', '\\r')   # Carriage return
                                    .replace('\b', '\\b')   # Backspace
                                    .replace('\f', '\\f')   # Form feed
                                    .replace('\v', '\\v'))  # Vertical tab
                                values.append(escaped_value)
                            elif isinstance(value, bool):
                                values.append('t' if value else 'f')
                            elif isinstance(value, (int, float)):
                                values.append(str(value))
                            elif hasattr(value, 'isoformat'):  # datetime objects
                                values.append(value.isoformat())
                            else:
                                # Per altri tipi, converti a stringa e applica escape
                                str_value = str(value)
                                escaped_value = (str_value
                                    .replace('\\', '\\\\')
                                    .replace('\t', '\\t')
                                    .replace('\n', '\\n')
                                    .replace('\r', '\\r'))
                                values.append(escaped_value)

                        file_handle.write('\t'.join(values) + '\n')

                # Terminatore COPY
                file_handle.write('\\.\n\n')

                logger.info(f"[BACKUP] Tabella {table_name}: {len(rows)} righe salvate")

        except Exception as e:
            logger.error(f"Errore backup tabella {table_name}: {e}")
            file_handle.write(f"-- ERROR backing up {table_name}: {str(e)}\n\n")

    def _backup_single_table_insert(self, file_handle, table_name: str):
        """Backup di una singola tabella in formato INSERT (più sicuro)"""
        try:
            with self.SessionLocal() as db:
                # Query dati tabella
                result = db.execute(text(f'SELECT * FROM "{table_name}"'))
                rows = result.fetchall()
                columns = result.keys()

                # Header
                file_handle.write(f"--\n-- Data for Name: {table_name}; Type: TABLE DATA; Schema: public; Owner: re77\n--\n\n")

                if not rows:
                    file_handle.write(f"-- No data for table {table_name}\n\n")
                    logger.info(f"[BACKUP] Tabella {table_name}: 0 righe salvate")
                    return

                # INSERT statements
                columns_str = ', '.join([f'"{col}"' for col in columns])

                for row in rows:
                    values = []
                    for value in row:
                        if value is None:
                            values.append('NULL')
                        elif isinstance(value, str):
                            # Escape per SQL string - doppio apice per escape
                            escaped_value = value.replace("'", "''")
                            values.append(f"'{escaped_value}'")
                        elif isinstance(value, bool):
                            values.append('TRUE' if value else 'FALSE')
                        elif isinstance(value, (int, float)):
                            values.append(str(value))
                        elif hasattr(value, 'isoformat'):  # datetime objects
                            values.append(f"'{value.isoformat()}'")
                        else:
                            # Per altri tipi, converti a stringa e applica escape
                            str_value = str(value).replace("'", "''")
                            values.append(f"'{str_value}'")

                    values_str = ', '.join(values)
                    file_handle.write(f'INSERT INTO public."{table_name}" ({columns_str}) VALUES ({values_str});\n')

                file_handle.write('\n')
                logger.info(f"[BACKUP] Tabella {table_name}: {len(rows)} righe salvate (INSERT)")

        except Exception as e:
            logger.error(f"Errore backup tabella {table_name}: {e}")
            file_handle.write(f"-- ERROR backing up {table_name}: {str(e)}\n\n")

    def _write_table_data(self, f):
        """Scrive dati tabelle in formato COPY"""
        f.write("--\n-- Data for Name: tables; Type: TABLE DATA; Schema: public; Owner: re77\n--\n\n")

        # Ottieni lista dinamica di tutte le tabelle dal database
        try:
            with self.SessionLocal() as db:
                tables_query = text("""
                    SELECT table_name
                    FROM information_schema.tables
                    WHERE table_schema = 'public'
                    AND table_type = 'BASE TABLE'
                    ORDER BY table_name
                """)

                result = db.execute(tables_query)
                all_tables = [row[0] for row in result.fetchall()]

                logger.info(f"[BACKUP] Trovate {len(all_tables)} tabelle nel database")

                # Backup di tutte le tabelle trovate (una sola volta ciascuna)
                for table_name in all_tables:
                    self._backup_table_copy_format(f, table_name)

        except Exception as e:
            logger.error(f"Errore ottenimento lista tabelle: {e}")
            # Fallback alla lista statica
            static_tables = [
                "AGENTE", "ARMATORE", "ATLAS", "AUDIT_LOG",
                "CODICI_ATLAS", "DEPARTMENT_NOTIFICATIONS",
                "EXPORT", "IMPORT", "LOGIN_ATTEMPTS", "NAVI",
                "ORARI", "PORTI_GESTIONE", "SOF_DOCUMENTS",
                "SYSTEM_CONFIG", "SYSTEM_STATS",
                "USER_NOTIFICATION_READ", "USER_SESSIONS", "VIAGGIO"
            ]

            for table_name in static_tables:
                self._backup_table_if_exists_copy(f, table_name)

    def _backup_table_data(self, file_handle, table_name: str):
        """Backup dati di una tabella specifica"""
        try:
            with self.SessionLocal() as db:
                # Query per ottenere tutti i dati della tabella
                result = db.execute(text(f'SELECT * FROM "{table_name}"'))
                rows = result.fetchall()
                columns = result.keys()

                if not rows:
                    file_handle.write(f"-- Table {table_name}: No data\n\n")
                    return

                file_handle.write(f"-- Table: {table_name}\n")
                file_handle.write(f"-- Rows: {len(rows)}\n")

                # Genera INSERT statements
                for row in rows:
                    values = []
                    for value in row:
                        if value is None:
                            values.append('NULL')
                        elif isinstance(value, str):
                            # Escape single quotes
                            escaped_value = value.replace("'", "''")
                            values.append(f"'{escaped_value}'")
                        elif isinstance(value, (int, float)):
                            values.append(str(value))
                        elif isinstance(value, bool):
                            values.append('TRUE' if value else 'FALSE')
                        else:
                            values.append(f"'{str(value)}'")

                    columns_str = ', '.join([f'"{col}"' for col in columns])
                    values_str = ', '.join(values)

                    file_handle.write(f'INSERT INTO "{table_name}" ({columns_str}) VALUES ({values_str});\n')

                file_handle.write(f"\n-- End of {table_name}\n\n")
                logger.info(f"[BACKUP] Tabella {table_name}: {len(rows)} righe salvate")

        except Exception as e:
            logger.error(f"Errore backup tabella {table_name}: {e}")
            file_handle.write(f"-- ERROR backing up {table_name}: {str(e)}\n\n")

    def _backup_table_if_exists(self, file_handle, table_name: str):
        """Backup tabella solo se esiste"""
        try:
            with self.SessionLocal() as db:
                # Verifica se la tabella esiste
                result = db.execute(text("""
                    SELECT EXISTS (
                        SELECT FROM information_schema.tables
                        WHERE table_name = :table_name
                    )
                """), {"table_name": table_name})

                table_exists = result.scalar()

                if table_exists:
                    self._backup_table_data(file_handle, table_name)
                else:
                    file_handle.write(f"-- Table {table_name}: Does not exist (skipped)\n\n")
                    logger.info(f"[BACKUP] Tabella {table_name}: non esiste, saltata")

        except Exception as e:
            logger.warning(f"Errore verifica tabella {table_name}: {e}")
            file_handle.write(f"-- ERROR checking {table_name}: {str(e)}\n\n")

    def _backup_table_copy_format(self, file_handle, table_name: str):
        """Backup tabella in formato COPY (stile pg_dump)"""
        try:
            with self.SessionLocal() as db:
                # Query per ottenere tutti i dati della tabella
                result = db.execute(text(f'SELECT * FROM "{table_name}"'))
                rows = result.fetchall()
                columns = result.keys()

                # Header COPY sempre presente (anche per tabelle vuote)
                file_handle.write(f"--\n-- Data for Name: {table_name}; Type: TABLE DATA; Schema: public; Owner: re77\n--\n\n")

                columns_str = ', '.join([f'"{col}"' for col in columns])
                file_handle.write(f'COPY public."{table_name}" ({columns_str}) FROM stdin;\n')

                if not rows:
                    # Tabella vuota - scrivi solo il terminatore
                    file_handle.write('\\.\n\n')
                    logger.info(f"[BACKUP] Tabella {table_name}: 0 righe salvate (formato COPY)")
                    return

                # Header COPY
                file_handle.write(f"--\n-- Data for Name: {table_name}; Type: TABLE DATA; Schema: public; Owner: re77\n--\n\n")

                columns_str = ', '.join([f'"{col}"' for col in columns])
                file_handle.write(f'COPY public."{table_name}" ({columns_str}) FROM stdin;\n')

                # Dati in formato COPY
                for row in rows:
                    values = []
                    for value in row:
                        if value is None:
                            values.append('\\N')
                        elif isinstance(value, str):
                            # Escape per formato COPY
                            escaped_value = value.replace('\\', '\\\\').replace('\t', '\\t').replace('\n', '\\n').replace('\r', '\\r')
                            values.append(escaped_value)
                        elif isinstance(value, bool):
                            values.append('t' if value else 'f')
                        else:
                            values.append(str(value))

                    file_handle.write('\t'.join(values) + '\n')

                file_handle.write('\\.\n\n')
                logger.info(f"[BACKUP] Tabella {table_name}: {len(rows)} righe salvate (formato COPY)")

        except Exception as e:
            logger.error(f"Errore backup tabella {table_name}: {e}")
            file_handle.write(f"-- ERROR backing up {table_name}: {str(e)}\n\n")

    def _backup_table_if_exists_copy(self, file_handle, table_name: str):
        """Backup tabella in formato COPY solo se esiste (include tabelle vuote)"""
        try:
            with self.SessionLocal() as db:
                # Verifica se la tabella esiste
                result = db.execute(text("""
                    SELECT EXISTS (
                        SELECT FROM information_schema.tables
                        WHERE table_name = :table_name
                    )
                """), {"table_name": table_name})

                table_exists = result.scalar()

                if table_exists:
                    # Backup tabella (anche se vuota)
                    self._backup_table_copy_format(file_handle, table_name)
                else:
                    # Tabella non esiste - scrivi commento
                    file_handle.write(f"--\n-- Data for Name: {table_name}; Type: TABLE DATA; Schema: public; Owner: re77\n--\n\n")
                    file_handle.write(f"-- Table {table_name}: Does not exist in database\n\n")
                    logger.info(f"[BACKUP] Tabella {table_name}: non esiste, saltata")

        except Exception as e:
            logger.warning(f"Errore verifica tabella {table_name}: {e}")
            file_handle.write(f"--\n-- Data for Name: {table_name}; Type: TABLE DATA; Schema: public; Owner: re77\n--\n\n")
            file_handle.write(f"-- ERROR checking {table_name}: {str(e)}\n\n")

    def _parse_db_url(self) -> Dict[str, str]:
        """Estrae parametri dalla URL database"""
        # Formato: postgresql://username:password@host:port/database
        from urllib.parse import urlparse
        
        parsed = urlparse(self.db_url)
        return {
            'host': parsed.hostname or 'localhost',
            'port': parsed.port or 5432,
            'username': parsed.username or 'postgres',
            'password': parsed.password or '',
            'database': parsed.path.lstrip('/') or 'snip'
        }
    
    def _cleanup_old_backups(self):
        """Rimuove backup vecchi secondo retention policy"""
        try:
            config = self.get_backup_config()
            retention_days = config.get('backup_retention', 30)
            
            cutoff_date = datetime.now() - timedelta(days=retention_days)
            
            removed_count = 0
            for backup_file in self.backup_dir.glob("snip_backup_*.sql*"):
                if backup_file.stat().st_mtime < cutoff_date.timestamp():
                    backup_file.unlink()
                    removed_count += 1
                    logger.info(f"[CLEANUP] Rimosso backup vecchio: {backup_file.name}")

            if removed_count > 0:
                logger.info(f"[CLEANUP] Pulizia completata: {removed_count} backup rimossi")
                
        except Exception as e:
            logger.error(f"Errore pulizia backup vecchi: {e}")
    
    def _send_backup_notification(self, backup_path: Path, file_size: int):
        """Invia notifica email del backup con file allegato all'amministratore"""
        try:
            config = self.get_backup_config()
            admin_email = config.get('admin_email')
            sender_email = config.get('sender_email')

            if not admin_email:
                logger.warning("Email amministratore non configurata - notifica non inviata")
                return

            # Importa funzione email dal main
            sys.path.append('.')
            from main import send_email

            subject = f"📦 Backup Database SNIP - {datetime.now().strftime('%d/%m/%Y %H:%M')}"

            body = f"""
Gentile Amministratore,

Il backup automatico del database SNIP è stato completato con successo.

📊 DETTAGLI BACKUP:
• Data/Ora: {datetime.now().strftime('%d/%m/%Y alle %H:%M:%S')}
• File: {backup_path.name}
• Dimensione: {file_size / 1024 / 1024:.2f} MB
• Compressione: {'Attiva' if backup_path.suffix == '.gz' else 'Disattiva'}

📋 CONFIGURAZIONE BACKUP:
• Frequenza: {config.get('backup_frequency', 'daily')}
• Retention: {config.get('backup_retention', 30)} giorni
• Orario programmato: {config.get('backup_time', '02:00')}

📎 ALLEGATO:
Il file di backup SQL è allegato a questa email e può essere utilizzato per:
• Ripristino completo del database AGENTE
• Backup di sicurezza offline
• Migrazione dati su altro server

📧 DESTINATARI:
• Email Admin: {admin_email}
• Email Mittente (CC): {sender_email if sender_email else 'Non configurata'}

⚠️ IMPORTANTE:
Conservare questo file in luogo sicuro. Contiene tutti i dati del sistema SNIP.

---
Sistema di Backup Automatico SNIP
Michele Autuori Srl - shipping and forwarding agency
            """

            # Invia email con allegato
            with self.SessionLocal() as db:
                success = send_email(
                    to_email=admin_email,
                    subject=subject,
                    body=body,
                    db=db,
                    attachment_path=str(backup_path),
                    cc_email=sender_email if sender_email else None
                )
                if success:
                    recipients = f"{admin_email}"
                    if sender_email:
                        recipients += f" (CC: {sender_email})"
                    logger.info(f"[EMAIL] Backup inviato con allegato a: {recipients}")
                    logger.info(f"[EMAIL] File allegato: {backup_path.name} ({file_size / 1024 / 1024:.2f} MB)")
                else:
                    logger.warning("Errore invio email backup con allegato")

        except Exception as e:
            logger.error(f"Errore invio notifica backup: {e}")
    
    def schedule_backups(self):
        """Programma backup automatici"""
        try:
            config = self.get_backup_config()
            frequency = config.get('backup_frequency', 'daily')
            backup_time = config.get('backup_time', '02:00')
            
            # Cancella schedule precedenti
            schedule.clear()
            
            if frequency == 'hourly':
                schedule.every().hour.do(self.create_backup)
                logger.info("📅 Backup programmati: ogni ora")
            elif frequency == 'daily':
                schedule.every().day.at(backup_time).do(self.create_backup)
                logger.info(f"[SCHEDULE] Backup programmati: giornalieri alle {backup_time}")
            elif frequency == 'weekly':
                schedule.every().monday.at(backup_time).do(self.create_backup)
                logger.info(f"[SCHEDULE] Backup programmati: settimanali (lunedì alle {backup_time})")
            elif frequency == 'monthly':
                schedule.every().month.do(self.create_backup)
                logger.info("[SCHEDULE] Backup programmati: mensili")
            else:
                logger.info("[SCHEDULE] Backup automatici disabilitati")
                return

            logger.info("[SCHEDULE] Scheduler backup configurato")
            
        except Exception as e:
            logger.error(f"Errore configurazione scheduler: {e}")
    
    def run_scheduler(self):
        """Esegue lo scheduler in background"""
        logger.info("[SCHEDULER] Avvio scheduler backup...")

        while True:
            try:
                schedule.run_pending()
                time.sleep(60)  # Controlla ogni minuto
            except KeyboardInterrupt:
                logger.info("[SCHEDULER] Scheduler backup fermato")
                break
            except Exception as e:
                logger.error(f"Errore scheduler: {e}")
                time.sleep(300)  # Aspetta 5 minuti prima di riprovare

def start_backup_service(db_url: str):
    """Avvia il servizio di backup in background"""
    backup_manager = BackupManager(db_url)
    backup_manager.schedule_backups()
    
    # Esegui in thread separato
    scheduler_thread = threading.Thread(
        target=backup_manager.run_scheduler,
        daemon=True,
        name="BackupScheduler"
    )
    scheduler_thread.start()
    
    logger.info("[SERVICE] Servizio backup avviato in background")
    return backup_manager

if __name__ == "__main__":
    # Test standalone con credenziali corrette
    db_url = "postgresql://re77:271077@localhost:5432/AGENTE"
    backup_manager = BackupManager(db_url)

    # Test backup manuale
    result = backup_manager.create_backup()
    if result:
        print(f"[SUCCESS] Backup creato: {result}")
    else:
        print("[ERROR] Errore creazione backup")
