{% extends "base_operativo.html" %}

{% block title %}SOF <PERSON>izzati - M.A.P.{% endblock %}

{% block extra_css %}
<link href="/static/css/armatori.css" rel="stylesheet">
<style>
/* ===== TEMA MARITTIMO ULTRA PREMIUM ===== */

/* Variabili CSS per coerenza */
:root {
    --maritime-primary: #1e3c72;
    --maritime-secondary: #2a5298;
    --maritime-accent: #1e40af;
    --maritime-gold: #ffd700;
    --maritime-gold-light: #ffed4e;
    --maritime-gold-dark: #b8860b;
    --maritime-success: #059669;
    --maritime-success-light: #10b981;
    --maritime-info: #0ea5e9;
    --maritime-info-light: #38bdf8;
    --maritime-warning: #f59e0b;
    --maritime-warning-light: #fbbf24;
    --maritime-danger: #dc2626;
    --maritime-danger-light: #ef4444;
    --maritime-glass: rgba(255, 255, 255, 0.1);
    --maritime-glass-strong: rgba(255, 255, 255, 0.2);
    --maritime-shadow: 0 20px 60px rgba(0, 0, 0, 0.4);
    --maritime-shadow-hover: 0 30px 80px rgba(0, 0, 0, 0.5);
}

/* Animazioni avanzate */
@keyframes maritimeWave {
    0%, 100% { transform: translateX(0) translateY(0) rotate(0deg); }
    25% { transform: translateX(5px) translateY(-5px) rotate(1deg); }
    50% { transform: translateX(-5px) translateY(5px) rotate(-1deg); }
    75% { transform: translateX(3px) translateY(-3px) rotate(0.5deg); }
}

@keyframes maritimeGlow {
    0%, 100% {
        box-shadow: 0 0 20px rgba(255, 215, 0, 0.3), 0 0 40px rgba(255, 215, 0, 0.1);
        filter: brightness(1);
    }
    50% {
        box-shadow: 0 0 30px rgba(255, 215, 0, 0.6), 0 0 60px rgba(255, 215, 0, 0.2);
        filter: brightness(1.1);
    }
}

@keyframes maritimeFloat {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-10px) rotate(1deg); }
}

@keyframes maritimePulse {
    0% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(255, 215, 0, 0.7);
    }
    70% {
        transform: scale(1.05);
        box-shadow: 0 0 0 15px rgba(255, 215, 0, 0);
    }
    100% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(255, 215, 0, 0);
    }
}

@keyframes maritimeShimmer {
    0% { background-position: -200% center; }
    100% { background-position: 200% center; }
}

/* ===== MODAL MARITTIMO ULTRA PREMIUM ===== */
body.theme-maritime .modal-content {
    background: linear-gradient(135deg,
        var(--maritime-primary) 0%,
        var(--maritime-secondary) 50%,
        var(--maritime-accent) 100%) !important;
    color: #ffffff !important;
    border: 3px solid transparent !important;
    border-radius: 25px !important;
    box-shadow: var(--maritime-shadow) !important;
    backdrop-filter: blur(20px) !important;
    overflow: hidden !important;
    position: relative !important;
    animation: maritimeFloat 6s ease-in-out infinite !important;
}

/* Bordo animato dorato */
body.theme-maritime .modal-content::before {
    content: '' !important;
    position: absolute !important;
    top: -3px !important;
    left: -3px !important;
    right: -3px !important;
    bottom: -3px !important;
    background: linear-gradient(45deg,
        var(--maritime-gold),
        var(--maritime-gold-light),
        var(--maritime-gold),
        var(--maritime-gold-dark)) !important;
    background-size: 400% 400% !important;
    border-radius: 25px !important;
    z-index: -1 !important;
    animation: maritimeGlow 3s ease-in-out infinite !important;
}

/* Effetto shimmer */
body.theme-maritime .modal-content::after {
    content: '' !important;
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    background: linear-gradient(90deg,
        transparent,
        rgba(255, 255, 255, 0.2),
        transparent) !important;
    background-size: 200% 100% !important;
    animation: maritimeShimmer 3s ease-in-out infinite !important;
    border-radius: 25px !important;
    pointer-events: none !important;
}

body.theme-maritime .modal-header {
    background: linear-gradient(135deg,
        var(--maritime-primary) 0%,
        var(--maritime-secondary) 30%,
        var(--maritime-accent) 70%,
        var(--maritime-primary) 100%) !important;
    color: #ffffff !important;
    border-bottom: 4px solid var(--maritime-gold) !important;
    box-shadow: 0 8px 25px rgba(0,0,0,0.4),
                inset 0 1px 0 rgba(255,255,255,0.2) !important;
    position: relative !important;
    overflow: hidden !important;
    padding: 1.5rem 2rem !important;
}

/* Effetto onde animate */
body.theme-maritime .modal-header::before {
    content: '' !important;
    position: absolute !important;
    top: -50% !important;
    left: -50% !important;
    width: 200% !important;
    height: 200% !important;
    background: radial-gradient(circle,
        rgba(255, 215, 0, 0.1) 0%,
        rgba(255, 215, 0, 0.05) 30%,
        transparent 70%) !important;
    animation: maritimeWave 8s ease-in-out infinite !important;
    pointer-events: none !important;
}

/* Particelle dorate animate */
body.theme-maritime .modal-header::after {
    content: '⚓ 🌊 ⛵ 🌊 ⚓ 🌊 ⛵ 🌊' !important;
    position: absolute !important;
    top: 50% !important;
    left: -100% !important;
    width: 200% !important;
    height: 2px !important;
    color: var(--maritime-gold) !important;
    font-size: 12px !important;
    opacity: 0.3 !important;
    animation: maritimeShimmer 15s linear infinite !important;
    pointer-events: none !important;
    transform: translateY(-50%) !important;
    white-space: nowrap !important;
}

body.theme-maritime .modal-title {
    color: #ffffff !important;
    text-shadow: 3px 3px 6px rgba(0,0,0,0.8),
                 0 0 20px rgba(255,215,0,0.3) !important;
    font-weight: 800 !important;
    position: relative !important;
    z-index: 10 !important;
    font-size: 1.5rem !important;
    letter-spacing: 1px !important;
    background: linear-gradient(45deg, #ffffff, var(--maritime-gold-light), #ffffff) !important;
    background-size: 200% 200% !important;
    -webkit-background-clip: text !important;
    -webkit-text-fill-color: transparent !important;
    background-clip: text !important;
    animation: maritimeShimmer 4s ease-in-out infinite !important;
}

body.theme-maritime .modal-title i {
    color: var(--maritime-gold) !important;
    text-shadow: 0 0 15px rgba(255,215,0,0.8),
                 0 0 30px rgba(255,215,0,0.4) !important;
    margin-right: 12px !important;
    animation: maritimePulse 2s ease-in-out infinite !important;
    filter: drop-shadow(0 0 10px rgba(255,215,0,0.6)) !important;
    font-size: 1.2em !important;
}

body.theme-maritime .btn-close {
    filter: invert(1) brightness(1.3) !important;
    opacity: 0.9 !important;
    position: relative !important;
    z-index: 10 !important;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
    background: rgba(255, 255, 255, 0.1) !important;
    border-radius: 50% !important;
    width: 40px !important;
    height: 40px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
}

body.theme-maritime .btn-close:hover {
    opacity: 1 !important;
    transform: scale(1.2) rotate(90deg) !important;
    filter: invert(1) brightness(1.8) drop-shadow(0 0 15px rgba(255,215,0,1)) !important;
    background: rgba(255, 215, 0, 0.2) !important;
    animation: maritimePulse 1s ease-in-out infinite !important;
}

body.theme-maritime .modal-body {
    background: linear-gradient(135deg,
        rgba(255,255,255,0.98) 0%,
        rgba(248,249,250,0.95) 50%,
        rgba(240,245,255,0.98) 100%) !important;
    color: #212529 !important;
    backdrop-filter: blur(15px) !important;
    position: relative !important;
    padding: 2rem !important;
    overflow: hidden !important;
}

/* Effetto onde di sfondo */
body.theme-maritime .modal-body::before {
    content: '' !important;
    position: absolute !important;
    top: -50% !important;
    left: -50% !important;
    width: 200% !important;
    height: 200% !important;
    background: radial-gradient(circle at 30% 70%,
        rgba(30,60,114,0.08) 0%,
        rgba(42,82,152,0.05) 30%,
        rgba(255,215,0,0.02) 60%,
        transparent 100%) !important;
    animation: maritimeWave 12s ease-in-out infinite !important;
    pointer-events: none !important;
}

/* Particelle fluttuanti */
body.theme-maritime .modal-body::after {
    content: '✨ ⭐ ✨ ⭐ ✨ ⭐ ✨ ⭐ ✨ ⭐' !important;
    position: absolute !important;
    top: 20% !important;
    left: -100% !important;
    width: 200% !important;
    color: var(--maritime-gold) !important;
    font-size: 14px !important;
    opacity: 0.2 !important;
    animation: maritimeShimmer 20s linear infinite !important;
    pointer-events: none !important;
    z-index: 1 !important;
}

body.theme-maritime .modal-body > * {
    position: relative !important;
    z-index: 2 !important;
}

body.theme-maritime .modal-footer {
    background: linear-gradient(135deg, rgba(30,60,114,0.8) 0%, rgba(42,82,152,0.8) 100%) !important;
    border-top: 2px solid rgba(255,215,0,0.4) !important;
    backdrop-filter: blur(10px) !important;
}

/* ===== BADGE MARITTIMI ULTRA PREMIUM ===== */
body.theme-maritime .badge {
    font-weight: 700 !important;
    letter-spacing: 1px !important;
    border-radius: 15px !important;
    padding: 0.6rem 1rem !important;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.5) !important;
    box-shadow: 0 8px 25px rgba(0,0,0,0.3),
                inset 0 1px 0 rgba(255,255,255,0.2) !important;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
    position: relative !important;
    overflow: hidden !important;
    border: 2px solid transparent !important;
    background-clip: padding-box !important;
}

/* Effetto holografico per tutti i badge */
body.theme-maritime .badge::before {
    content: '' !important;
    position: absolute !important;
    top: -2px !important;
    left: -2px !important;
    right: -2px !important;
    bottom: -2px !important;
    background: linear-gradient(45deg,
        var(--maritime-gold),
        var(--maritime-gold-light),
        var(--maritime-gold),
        var(--maritime-gold-dark)) !important;
    background-size: 400% 400% !important;
    border-radius: 15px !important;
    z-index: -1 !important;
    animation: maritimeGlow 3s ease-in-out infinite !important;
}

body.theme-maritime .badge.bg-primary {
    background: linear-gradient(135deg,
        var(--maritime-primary) 0%,
        var(--maritime-secondary) 50%,
        var(--maritime-accent) 100%) !important;
    color: #ffffff !important;
}

body.theme-maritime .badge.bg-success {
    background: linear-gradient(135deg,
        var(--maritime-success) 0%,
        var(--maritime-success-light) 50%,
        #34d399 100%) !important;
    color: #ffffff !important;
}

body.theme-maritime .badge.bg-warning {
    background: linear-gradient(135deg,
        var(--maritime-warning) 0%,
        var(--maritime-warning-light) 50%,
        #fcd34d 100%) !important;
    color: #1f2937 !important;
    font-weight: 800 !important;
}

body.theme-maritime .badge.bg-info {
    background: linear-gradient(135deg,
        var(--maritime-info) 0%,
        var(--maritime-info-light) 50%,
        #7dd3fc 100%) !important;
    color: #ffffff !important;
}

body.theme-maritime .badge.bg-danger {
    background: linear-gradient(135deg,
        var(--maritime-danger) 0%,
        var(--maritime-danger-light) 50%,
        #f87171 100%) !important;
    color: #ffffff !important;
}

body.theme-maritime .badge:hover {
    transform: translateY(-3px) scale(1.05) !important;
    box-shadow: 0 15px 35px rgba(0,0,0,0.4),
                inset 0 2px 0 rgba(255,255,255,0.3) !important;
    animation: maritimePulse 1.5s ease-in-out infinite !important;
}

/* Effetto shimmer sui badge */
body.theme-maritime .badge::after {
    content: '' !important;
    position: absolute !important;
    top: 0 !important;
    left: -100% !important;
    width: 100% !important;
    height: 100% !important;
    background: linear-gradient(90deg,
        transparent,
        rgba(255, 255, 255, 0.4),
        transparent) !important;
    transition: left 0.5s ease !important;
}

body.theme-maritime .badge:hover::after {
    left: 100% !important;
}

/* ===== FORM CONTROLS MARITTIMI ULTRA PREMIUM ===== */
body.theme-maritime .modal-body .form-select {
    background: linear-gradient(135deg,
        rgba(255,255,255,0.98) 0%,
        rgba(248,249,250,0.95) 50%,
        rgba(240,245,255,0.98) 100%) !important;
    border: 3px solid transparent !important;
    color: #212529 !important;
    border-radius: 15px !important;
    padding: 1rem 1.5rem !important;
    font-weight: 600 !important;
    font-size: 1.1rem !important;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
    box-shadow: 0 8px 25px rgba(0,0,0,0.15),
                inset 0 1px 0 rgba(255,255,255,0.5) !important;
    position: relative !important;
    background-clip: padding-box !important;
}

/* Bordo animato per form select */
body.theme-maritime .modal-body .form-select::before {
    content: '' !important;
    position: absolute !important;
    top: -3px !important;
    left: -3px !important;
    right: -3px !important;
    bottom: -3px !important;
    background: linear-gradient(45deg,
        var(--maritime-primary),
        var(--maritime-secondary),
        var(--maritime-gold),
        var(--maritime-accent)) !important;
    background-size: 400% 400% !important;
    border-radius: 15px !important;
    z-index: -1 !important;
    animation: maritimeGlow 4s ease-in-out infinite !important;
}

body.theme-maritime .modal-body .form-select:focus {
    transform: translateY(-2px) !important;
    box-shadow: 0 15px 35px rgba(255,215,0,0.3),
                0 5px 15px rgba(30,60,114,0.2),
                inset 0 2px 0 rgba(255,255,255,0.7) !important;
    background: rgba(255,255,255,1) !important;
}

body.theme-maritime .modal-body .form-select:hover {
    transform: translateY(-1px) !important;
    box-shadow: 0 12px 30px rgba(0,0,0,0.2) !important;
}

body.theme-maritime .modal-body .form-label {
    color: var(--maritime-primary) !important;
    font-weight: 700 !important;
    font-size: 1.1rem !important;
    margin-bottom: 0.75rem !important;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.1) !important;
    position: relative !important;
}

body.theme-maritime .modal-body .form-label i {
    color: var(--maritime-gold) !important;
    margin-right: 0.75rem !important;
    text-shadow: 0 0 10px rgba(255,215,0,0.6) !important;
    animation: maritimePulse 3s ease-in-out infinite !important;
    font-size: 1.2em !important;
    filter: drop-shadow(0 0 5px rgba(255,215,0,0.4)) !important;
}

/* ===== BOTTONI MARITTIMI ULTRA PREMIUM ===== */
body.theme-maritime .modal-footer .btn-primary {
    background: linear-gradient(135deg,
        var(--maritime-primary) 0%,
        var(--maritime-secondary) 50%,
        var(--maritime-accent) 100%) !important;
    border: 3px solid transparent !important;
    color: #ffffff !important;
    font-weight: 800 !important;
    font-size: 1.1rem !important;
    padding: 1rem 2rem !important;
    border-radius: 20px !important;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.5) !important;
    box-shadow: 0 10px 30px rgba(30,60,114,0.4),
                inset 0 2px 0 rgba(255,255,255,0.2) !important;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
    position: relative !important;
    overflow: hidden !important;
    background-clip: padding-box !important;
    letter-spacing: 1px !important;
}

/* Bordo dorato animato per btn-primary */
body.theme-maritime .modal-footer .btn-primary::before {
    content: '' !important;
    position: absolute !important;
    top: -3px !important;
    left: -3px !important;
    right: -3px !important;
    bottom: -3px !important;
    background: linear-gradient(45deg,
        var(--maritime-gold),
        var(--maritime-gold-light),
        var(--maritime-gold),
        var(--maritime-gold-dark)) !important;
    background-size: 400% 400% !important;
    border-radius: 20px !important;
    z-index: -1 !important;
    animation: maritimeGlow 3s ease-in-out infinite !important;
}

/* Effetto shimmer sui bottoni */
body.theme-maritime .modal-footer .btn-primary::after {
    content: '' !important;
    position: absolute !important;
    top: 0 !important;
    left: -100% !important;
    width: 100% !important;
    height: 100% !important;
    background: linear-gradient(90deg,
        transparent,
        rgba(255, 255, 255, 0.3),
        transparent) !important;
    transition: left 0.6s ease !important;
}

body.theme-maritime .modal-footer .btn-primary:hover {
    background: linear-gradient(135deg,
        var(--maritime-secondary) 0%,
        var(--maritime-accent) 50%,
        #3b82f6 100%) !important;
    transform: translateY(-4px) scale(1.02) !important;
    box-shadow: 0 20px 50px rgba(30,60,114,0.5),
                inset 0 3px 0 rgba(255,255,255,0.3) !important;
    animation: maritimeFloat 2s ease-in-out infinite !important;
}

body.theme-maritime .modal-footer .btn-primary:hover::after {
    left: 100% !important;
}

body.theme-maritime .modal-footer .btn-primary:active {
    transform: translateY(-2px) scale(0.98) !important;
    box-shadow: 0 8px 20px rgba(30,60,114,0.4) !important;
}

body.theme-maritime .modal-footer .btn-secondary {
    background: linear-gradient(135deg,
        rgba(255,255,255,0.95) 0%,
        rgba(248,249,250,0.9) 50%,
        rgba(240,245,255,0.95) 100%) !important;
    border: 3px solid transparent !important;
    color: var(--maritime-primary) !important;
    font-weight: 700 !important;
    font-size: 1.1rem !important;
    padding: 1rem 2rem !important;
    border-radius: 20px !important;
    box-shadow: 0 8px 25px rgba(0,0,0,0.15),
                inset 0 2px 0 rgba(255,255,255,0.5) !important;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
    position: relative !important;
    overflow: hidden !important;
    background-clip: padding-box !important;
    letter-spacing: 1px !important;
}

/* Bordo per btn-secondary */
body.theme-maritime .modal-footer .btn-secondary::before {
    content: '' !important;
    position: absolute !important;
    top: -3px !important;
    left: -3px !important;
    right: -3px !important;
    bottom: -3px !important;
    background: linear-gradient(45deg,
        var(--maritime-primary),
        var(--maritime-secondary),
        var(--maritime-primary)) !important;
    background-size: 400% 400% !important;
    border-radius: 20px !important;
    z-index: -1 !important;
    animation: maritimeGlow 4s ease-in-out infinite !important;
}

body.theme-maritime .modal-footer .btn-secondary:hover {
    background: linear-gradient(135deg,
        rgba(30,60,114,0.1) 0%,
        rgba(42,82,152,0.1) 50%,
        rgba(30,60,114,0.15) 100%) !important;
    color: var(--maritime-primary) !important;
    transform: translateY(-3px) scale(1.02) !important;
    box-shadow: 0 15px 35px rgba(0,0,0,0.2) !important;
}

body.theme-maritime .modal-footer .btn-warning {
    background: linear-gradient(135deg,
        var(--maritime-warning) 0%,
        var(--maritime-warning-light) 50%,
        #fcd34d 100%) !important;
    border: 3px solid transparent !important;
    color: #1f2937 !important;
    font-weight: 800 !important;
    font-size: 1.1rem !important;
    padding: 1rem 2rem !important;
    border-radius: 20px !important;
    text-shadow: 2px 2px 4px rgba(255,255,255,0.5) !important;
    box-shadow: 0 10px 30px rgba(251,191,36,0.4),
                inset 0 2px 0 rgba(255,255,255,0.3) !important;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
    position: relative !important;
    overflow: hidden !important;
    background-clip: padding-box !important;
    letter-spacing: 1px !important;
}

/* Bordo per btn-warning */
body.theme-maritime .modal-footer .btn-warning::before {
    content: '' !important;
    position: absolute !important;
    top: -3px !important;
    left: -3px !important;
    right: -3px !important;
    bottom: -3px !important;
    background: linear-gradient(45deg,
        var(--maritime-primary),
        var(--maritime-gold),
        var(--maritime-primary)) !important;
    background-size: 400% 400% !important;
    border-radius: 20px !important;
    z-index: -1 !important;
    animation: maritimeGlow 3s ease-in-out infinite !important;
}

body.theme-maritime .modal-footer .btn-warning:hover {
    background: linear-gradient(135deg,
        var(--maritime-warning-light) 0%,
        #fcd34d 50%,
        #fde68a 100%) !important;
    transform: translateY(-4px) scale(1.02) !important;
    box-shadow: 0 20px 50px rgba(251,191,36,0.5),
                inset 0 3px 0 rgba(255,255,255,0.4) !important;
    animation: maritimeFloat 2s ease-in-out infinite !important;
}

/* ===== CARD HEADER MIGLIORATO ===== */
body.theme-maritime .card-header {
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%) !important;
    color: #ffffff !important;
    border-bottom: 3px solid #ffd700 !important;
    box-shadow: 0 4px 15px rgba(0,0,0,0.2) !important;
}

body.theme-maritime .card-header h5 {
    color: #ffffff !important;
    text-shadow: 1px 1px 3px rgba(0,0,0,0.5) !important;
    font-weight: 700 !important;
}

body.theme-maritime .card-header h5 i {
    color: #ffd700 !important;
    text-shadow: 0 0 8px rgba(255,215,0,0.6) !important;
}

body.theme-maritime .card-header .btn-outline-primary {
    border: 2px solid #ffd700 !important;
    color: #ffd700 !important;
    font-weight: 600 !important;
    transition: all 0.3s ease !important;
}

body.theme-maritime .card-header .btn-outline-primary:hover {
    background: #ffd700 !important;
    border-color: #ffd700 !important;
    color: #1e3c72 !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 12px rgba(255,215,0,0.4) !important;
}

/* ===== STATISTICHE CARDS MARITTIMI ===== */
body.theme-maritime .card.bg-success {
    background: linear-gradient(135deg, #059669 0%, #10b981 100%) !important;
    border: 2px solid rgba(255,215,0,0.3) !important;
    box-shadow: 0 8px 25px rgba(5,150,105,0.3) !important;
    transition: all 0.3s ease !important;
}

body.theme-maritime .card.bg-info {
    background: linear-gradient(135deg, #0ea5e9 0%, #38bdf8 100%) !important;
    border: 2px solid rgba(255,215,0,0.3) !important;
    box-shadow: 0 8px 25px rgba(14,165,233,0.3) !important;
    transition: all 0.3s ease !important;
}

body.theme-maritime .card.bg-primary {
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%) !important;
    border: 2px solid rgba(255,215,0,0.3) !important;
    box-shadow: 0 8px 25px rgba(30,60,114,0.3) !important;
    transition: all 0.3s ease !important;
}

body.theme-maritime .card.bg-success:hover,
body.theme-maritime .card.bg-info:hover,
body.theme-maritime .card.bg-primary:hover {
    transform: translateY(-5px) !important;
    box-shadow: 0 12px 35px rgba(0,0,0,0.4) !important;
}

body.theme-maritime .card.bg-success .card-body i,
body.theme-maritime .card.bg-info .card-body i,
body.theme-maritime .card.bg-primary .card-body i {
    text-shadow: 0 0 15px rgba(255,215,0,0.6) !important;
    filter: drop-shadow(0 0 8px rgba(255,215,0,0.4)) !important;
}

body.theme-maritime .card.bg-success .card-body h4,
body.theme-maritime .card.bg-info .card-body h4,
body.theme-maritime .card.bg-primary .card-body h4 {
    text-shadow: 2px 2px 4px rgba(0,0,0,0.5) !important;
    font-weight: 700 !important;
}

/* ===== TABELLA MIGLIORATA ===== */
body.theme-maritime .table {
    background: rgba(255,255,255,0.95) !important;
    border-radius: 15px !important;
    overflow: hidden !important;
    box-shadow: 0 8px 25px rgba(0,0,0,0.1) !important;
}

body.theme-maritime .table thead th {
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%) !important;
    color: #ffffff !important;
    border: none !important;
    font-weight: 600 !important;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.3) !important;
    padding: 1rem !important;
}

body.theme-maritime .table thead th i {
    color: #ffd700 !important;
    text-shadow: 0 0 8px rgba(255,215,0,0.6) !important;
}

body.theme-maritime .table tbody td {
    color: #212529 !important;
    border-color: rgba(30,60,114,0.1) !important;
    font-weight: 500 !important;
    padding: 1rem !important;
}

body.theme-maritime .table tbody tr:hover {
    background-color: rgba(30,60,114,0.05) !important;
    transform: scale(1.01) !important;
    transition: all 0.3s ease !important;
}

/* ===== AVATAR CIRCLE MIGLIORATO ===== */
body.theme-maritime .avatar-circle {
    width: 40px !important;
    height: 40px !important;
    border-radius: 50% !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    box-shadow: 0 4px 12px rgba(0,0,0,0.2) !important;
    border: 2px solid #ffd700 !important;
}

body.theme-maritime .avatar-circle.bg-success {
    background: linear-gradient(135deg, #059669 0%, #10b981 100%) !important;
}

/* ===== ANIMAZIONI ===== */
@keyframes maritimePulse {
    0% {
        box-shadow: 0 0 0 0 rgba(255,215,0,0.7);
        transform: scale(1);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(255,215,0,0);
        transform: scale(1.05);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(255,215,0,0);
        transform: scale(1);
    }
}

body.theme-maritime .badge:hover {
    animation: maritimePulse 1.5s infinite !important;
}

/* ===== PAGE HEADER MIGLIORATO ===== */
body.theme-maritime .page-header {
    background: linear-gradient(135deg, rgba(255,255,255,0.95) 0%, rgba(248,249,250,0.95) 100%) !important;
    border-radius: 20px !important;
    padding: 2rem !important;
    margin-bottom: 2rem !important;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1) !important;
    border: 2px solid rgba(255,215,0,0.3) !important;
    backdrop-filter: blur(10px) !important;
}

body.theme-maritime .page-header h2 {
    color: #1e3c72 !important;
    text-shadow: 1px 1px 3px rgba(0,0,0,0.1) !important;
    font-weight: 700 !important;
}

body.theme-maritime .page-header h2 i {
    color: #ffd700 !important;
    text-shadow: 0 0 10px rgba(255,215,0,0.6) !important;
}

body.theme-maritime .page-header .subtitle {
    color: #4b5563 !important;
    font-weight: 500 !important;
}

/* ===== PULSANTI AZIONE MARITTIMI MIGLIORATI ===== */
body.theme-maritime .action-buttons .btn.rounded-pill {
    border-radius: 50% !important;
    width: 40px !important;
    height: 40px !important;
    padding: 0 !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    font-weight: 600 !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15) !important;
    border-width: 2px !important;
    position: relative !important;
    overflow: hidden !important;
}

body.theme-maritime .action-buttons .btn.rounded-pill:hover {
    transform: translateY(-3px) scale(1.1) !important;
    box-shadow: 0 8px 25px rgba(0,0,0,0.25) !important;
}

body.theme-maritime .btn-outline-info.rounded-pill {
    border-color: #0ea5e9 !important;
    color: #0ea5e9 !important;
    background: rgba(14,165,233,0.1) !important;
}

body.theme-maritime .btn-outline-info.rounded-pill:hover {
    background: linear-gradient(135deg, #0ea5e9, #38bdf8) !important;
    border-color: #ffd700 !important;
    color: #ffffff !important;
    box-shadow: 0 8px 25px rgba(14,165,233,0.4) !important;
}

body.theme-maritime .btn-outline-primary.rounded-pill {
    border-color: #1e3c72 !important;
    color: #1e3c72 !important;
    background: rgba(30,60,114,0.1) !important;
}

body.theme-maritime .btn-outline-primary.rounded-pill:hover {
    background: linear-gradient(135deg, #1e3c72, #2a5298) !important;
    border-color: #ffd700 !important;
    color: #ffffff !important;
    box-shadow: 0 8px 25px rgba(30,60,114,0.4) !important;
}

body.theme-maritime .btn-outline-success.rounded-pill {
    border-color: #059669 !important;
    color: #059669 !important;
    background: rgba(5,150,105,0.1) !important;
}

body.theme-maritime .btn-outline-success.rounded-pill:hover {
    background: linear-gradient(135deg, #059669, #10b981) !important;
    border-color: #ffd700 !important;
    color: #ffffff !important;
    box-shadow: 0 8px 25px rgba(5,150,105,0.4) !important;
}

body.theme-maritime .btn-outline-warning.rounded-pill {
    border-color: #f59e0b !important;
    color: #f59e0b !important;
    background: rgba(245,158,11,0.1) !important;
}

body.theme-maritime .btn-outline-warning.rounded-pill:hover {
    background: linear-gradient(135deg, #f59e0b, #fbbf24) !important;
    border-color: #1e3c72 !important;
    color: #1f2937 !important;
    box-shadow: 0 8px 25px rgba(245,158,11,0.4) !important;
}

/* Stili per pulsanti rotondi nella tabella SOF */
.action-buttons {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
    justify-content: center;
}

.action-buttons .btn.rounded-pill {
    border-radius: 50% !important;
    width: 36px;
    height: 36px;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 500;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    border-width: 1.5px;
}

.action-buttons .btn.rounded-pill:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.action-buttons .btn.rounded-pill:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* Colori specifici per ogni tipo di pulsante */
.btn-outline-info.rounded-pill:hover {
    background: linear-gradient(135deg, #17a2b8, #138496);
    border-color: #17a2b8;
}

.btn-outline-primary.rounded-pill:hover {
    background: linear-gradient(135deg, #007bff, #0056b3);
    border-color: #007bff;
}

.btn-outline-success.rounded-pill:hover {
    background: linear-gradient(135deg, #28a745, #1e7e34);
    border-color: #28a745;
}

.btn-outline-warning.rounded-pill:hover {
    background: linear-gradient(135deg, #ffc107, #e0a800);
    border-color: #ffc107;
    color: #212529 !important;
}

/* Responsive per mobile */
@media (max-width: 768px) {
    .action-buttons {
        justify-content: center;
        gap: 6px;
    }

    .action-buttons .btn.rounded-pill {
        width: 32px;
        height: 32px;
    }
}
</style>
{% endblock %}

{% block content %}
        <!-- Header della pagina -->
        <div class="page-header text-center">
            <h2><i class="fas fa-check-circle me-3 text-success"></i>SOF Realizzati</h2>
            <p class="subtitle">Visualizza tutti i SOF (Statement of Facts) già completati e scaricati</p>
        </div>

        <!-- Statistiche -->
        <div class="row mb-4">
            <div class="col-md-4">
                <div class="card bg-success text-white">
                    <div class="card-body text-center">
                        <i class="fas fa-check-circle fa-2x mb-2"></i>
                        <h4>{{ viaggi|length }}</h4>
                        <p class="mb-0">SOF Completati</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card bg-info text-white">
                    <div class="card-body text-center">
                        <i class="fas fa-download fa-2x mb-2"></i>
                        <h4>{{ viaggi|length }}</h4>
                        <p class="mb-0">Download Effettuati</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card bg-primary text-white">
                    <div class="card-body text-center">
                        <i class="fas fa-archive fa-2x mb-2"></i>
                        <h4>Archivio</h4>
                        <p class="mb-0">Documenti Archiviati</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Tabella dei SOF realizzati -->
        <div class="card">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-start">
                    <div class="flex-grow-1 me-4">
                        <h5 class="mb-0"><i class="fas fa-list-check me-2"></i>Lista SOF Realizzati</h5>
                        <small class="text-muted">
                            <i class="fas fa-check-circle me-1"></i>
                            Viaggi con SOF già completati e scaricati
                        </small>
                    </div>

                    <div class="d-flex flex-column align-items-end gap-2">
                        <div class="badge bg-success fs-6">
                            <i class="fas fa-check me-1"></i>{{ viaggi|length }} SOF Completati
                        </div>

                        {% if current_user.ruolo.value in ['ADMIN', 'SUPER_ADMIN'] or current_user.ruolo in ['ADMIN', 'SUPER_ADMIN'] %}
                        <button class="btn btn-outline-primary btn-sm" onclick="apriModalArchiviazioneMassa()">
                            <i class="fas fa-archive me-2"></i>Archiviazione
                        </button>
                        {% endif %}
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th><i class="fas fa-barcode me-2"></i>Codice Viaggio</th>
                                <th><i class="fas fa-ship me-2"></i>Nome Nave</th>
                                <th><i class="fas fa-anchor me-2"></i>Porto di Gestione</th>
                                <th><i class="fas fa-calendar-alt me-2"></i>Data Arrivo</th>
                                <th><i class="fas fa-calendar-alt me-2"></i>Data Partenza</th>
                                <th class="text-center"><i class="fas fa-check-circle me-2"></i>Stato</th>
                                <th class="text-center"><i class="fas fa-cogs me-2"></i>Azioni</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for viaggio in viaggi %}
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="avatar-circle me-3 bg-success">
                                            <i class="fas fa-check text-white"></i>
                                        </div>
                                        <span class="badge bg-success">{{ viaggio.viaggio }}</span>
                                    </div>
                                </td>
                                <td>
                                    <strong>{{ viaggio.nome_nave or 'N/A' }}</strong>
                                </td>
                                <td>
                                    {% if viaggio.nome_porto %}
                                        <span class="badge bg-primary">
                                            <i class="fas fa-anchor me-1"></i>{{ viaggio.nome_porto }}
                                        </span>
                                    {% else %}
                                        <span class="text-muted">
                                            <i class="fas fa-minus me-1"></i>Non assegnato
                                        </span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if viaggio.data_arrivo %}
                                        <span class="text-success fw-bold">
                                            <i class="fas fa-calendar-check me-1"></i>
                                            {{ viaggio.data_arrivo.strftime('%d/%m/%Y') if viaggio.data_arrivo.strftime else viaggio.data_arrivo }}
                                        </span>
                                    {% else %}
                                        <span class="text-muted">
                                            <i class="fas fa-calendar-times me-1"></i>N/A
                                        </span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if viaggio.data_partenza %}
                                        <span class="text-warning fw-bold">
                                            <i class="fas fa-calendar-check me-1"></i>
                                            {{ viaggio.data_partenza.strftime('%d/%m/%Y') if viaggio.data_partenza.strftime else viaggio.data_partenza }}
                                        </span>
                                    {% else %}
                                        <span class="text-muted">
                                            <i class="fas fa-calendar-times me-1"></i>N/A
                                        </span>
                                    {% endif %}
                                </td>
                                <td class="text-center">
                                    <span class="badge bg-success">
                                        <i class="fas fa-check-circle me-1"></i>SOF COMPLETATO
                                    </span>
                                </td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="btn btn-sm btn-outline-info rounded-pill" title="Visualizza Dettagli"
                                                onclick="visualizzaDettagli({{ viaggio.id }})">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="btn btn-sm btn-outline-primary rounded-pill" title="Invia Notifica Email"
                                                onclick="inviaNotificaSOF({{ viaggio.id }}, '{{ viaggio.viaggio or '' }}')">
                                            <i class="fas fa-envelope"></i>
                                        </button>
                                        <button class="btn btn-sm btn-outline-success rounded-pill" title="Ripristina Viaggio"
                                                onclick="ripristinaViaggio({{ viaggio.id }}, '{{ viaggio.viaggio or '' }}')">
                                            <i class="fas fa-undo"></i>
                                        </button>
                                        {% if current_user.ruolo.value in ['ADMIN', 'SUPER_ADMIN'] or current_user.ruolo in ['ADMIN', 'SUPER_ADMIN'] %}
                                        <button class="btn btn-sm btn-outline-warning rounded-pill" title="Archivia SOF"
                                                onclick="archiviaSOF({{ viaggio.id }}, '{{ viaggio.viaggio or '' }}')">
                                            <i class="fas fa-archive"></i>
                                        </button>
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                            {% if not viaggi %}
                            <tr>
                                <td colspan="7" class="text-center py-5">
                                    <div class="text-muted">
                                        <i class="fas fa-check-circle fa-3x mb-3 text-success"></i>
                                        <h5>Nessun SOF realizzato</h5>
                                        <p>Non ci sono ancora SOF completati nel sistema.<br>
                                        I viaggi con SOF scaricati appariranno qui automaticamente.</p>
                                    </div>
                                </td>
                            </tr>
                            {% endif %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal Ripristina Viaggio -->
    <div class="modal fade" id="ripristinaViaggioModal" tabindex="-1" aria-labelledby="ripristinaViaggioModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header bg-warning text-dark">
                    <h5 class="modal-title" id="ripristinaViaggioModalLabel">
                        <i class="fas fa-undo me-2"></i>Ripristina Viaggio
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="text-center mb-4">
                        <i class="fas fa-undo text-warning fa-4x mb-3"></i>
                        <h5>Conferma Ripristino</h5>
                        <p class="mb-0">Sei sicuro di voler ripristinare il viaggio:</p>
                        <h6 class="text-primary mt-2" id="ripristina_viaggio_nome"></h6>
                    </div>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>Nota:</strong> Il viaggio tornerà nella lista "SOF da Realizzare" e sarà nuovamente disponibile per la gestione.
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-1"></i>Annulla
                    </button>
                    <button type="button" class="btn btn-warning" id="confermaRipristinaViaggio">
                        <i class="fas fa-undo me-1"></i>Ripristina Viaggio
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal Invia Notifica SOF -->
    <div class="modal fade" id="inviaNotificaSOFModal" tabindex="-1" aria-labelledby="inviaNotificaSOFModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title" id="inviaNotificaSOFModalLabel">
                        <i class="fas fa-envelope me-2"></i>Invia Notifica SOF
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="text-center mb-4">
                        <i class="fas fa-envelope text-primary fa-4x mb-3"></i>
                        <h5>Conferma Invio Notifica</h5>
                        <p class="mb-0">Sei sicuro di voler inviare la notifica email per il SOF del viaggio:</p>
                        <h6 class="text-primary mt-2" id="notifica_viaggio_nome"></h6>
                    </div>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>Nota:</strong> Verrà inviata una email di notifica all'amministratore con i dettagli del SOF completato.
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-1"></i>Annulla
                    </button>
                    <button type="button" class="btn btn-primary" id="confermaInviaNotifica">
                        <i class="fas fa-envelope me-1"></i>Invia Notifica
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal Archivia SOF -->
    <div class="modal fade" id="archiviaSOFModal" tabindex="-1" aria-labelledby="archiviaSOFModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header bg-warning text-dark">
                    <h5 class="modal-title" id="archiviaSOFModalLabel">
                        <i class="fas fa-archive me-2"></i>Archivia SOF
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="text-center mb-4">
                        <i class="fas fa-archive text-warning fa-4x mb-3"></i>
                        <h5>Conferma Archiviazione</h5>
                        <p class="mb-0">Sei sicuro di voler archiviare il SOF del viaggio:</p>
                        <h6 class="text-primary mt-2" id="archivia_viaggio_nome"></h6>
                    </div>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>📁 ARCHIVIAZIONE SEMPLICE:</strong><br>
                        • Il viaggio verrà marcato come archiviato<br>
                        • <strong>Tutti i dati rimarranno nel database</strong><br>
                        • Il SOF sarà visibile nella sezione "SOF Archiviati"<br>
                        • <strong>L'operazione può essere annullata se necessario</strong>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-1"></i>Annulla
                    </button>
                    <button type="button" class="btn btn-warning" id="confermaArchiviaSOF">
                        <i class="fas fa-archive me-1"></i>Archivia SOF
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal Archiviazione di Massa -->
    <div class="modal fade" id="archiviazioneMassaModal" tabindex="-1" aria-labelledby="archiviazioneMassaModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title" id="archiviazioneMassaModalLabel">
                        <i class="fas fa-archive me-2"></i>Archiviazione di Massa SOF
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <!-- Filtri per Mese/Anno -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <label for="filtroMese" class="form-label">
                                <i class="fas fa-calendar-alt me-2"></i>Seleziona Mese
                            </label>
                            <select class="form-select" id="filtroMese" onchange="caricaViaggiPerPeriodo()">
                                <option value="">-- Seleziona Mese --</option>
                                <option value="01">Gennaio</option>
                                <option value="02">Febbraio</option>
                                <option value="03">Marzo</option>
                                <option value="04">Aprile</option>
                                <option value="05">Maggio</option>
                                <option value="06">Giugno</option>
                                <option value="07">Luglio</option>
                                <option value="08">Agosto</option>
                                <option value="09">Settembre</option>
                                <option value="10">Ottobre</option>
                                <option value="11">Novembre</option>
                                <option value="12">Dicembre</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="filtroAnno" class="form-label">
                                <i class="fas fa-calendar me-2"></i>Seleziona Anno
                            </label>
                            <select class="form-select" id="filtroAnno" onchange="caricaViaggiPerPeriodo()">
                                <option value="">-- Seleziona Anno --</option>
                                <!-- Gli anni verranno popolati dinamicamente -->
                            </select>
                        </div>
                    </div>

                    <!-- Area risultati -->
                    <div id="risultatiArchiviazione">
                        <div class="text-center text-muted py-5">
                            <i class="fas fa-search fa-3x mb-3"></i>
                            <h5>Seleziona Mese e Anno</h5>
                            <p>Scegli il periodo per visualizzare i viaggi disponibili per l'archiviazione</p>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-1"></i>Chiudi
                    </button>
                    <button type="button" class="btn btn-primary" id="btnArchiviaSelezionati" onclick="mostraConfermaArchiviazione()" disabled>
                        <i class="fas fa-archive me-1"></i>Archivia Selezionati
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal Conferma Archiviazione -->
    <div class="modal fade" id="confermaArchiviazioneModal" tabindex="-1" aria-labelledby="confermaArchiviazioneModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-warning text-dark">
                    <h5 class="modal-title" id="confermaArchiviazioneModalLabel">
                        <i class="fas fa-exclamation-triangle me-2"></i>Conferma Archiviazione
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-warning">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>Attenzione!</strong> Stai per archiviare <span id="numeroViaggiDaArchiviare" class="fw-bold">0</span> viaggi.
                    </div>

                    <p><strong>Cosa succederà:</strong></p>
                    <ul>
                        <li><i class="fas fa-arrow-right text-primary me-2"></i>I viaggi selezionati verranno spostati nella sezione <strong>"SOF Archiviati"</strong></li>
                        <li><i class="fas fa-eye-slash text-secondary me-2"></i>Non saranno più visibili nella lista <strong>"SOF Realizzati"</strong></li>
                        <li><i class="fas fa-undo text-success me-2"></i>Potrai sempre visualizzarli nella sezione archiviati</li>
                    </ul>

                    <div class="alert alert-info">
                        <i class="fas fa-question-circle me-2"></i>
                        <strong>Sei sicuro di voler procedere?</strong>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-1"></i>Annulla
                    </button>
                    <button type="button" class="btn btn-warning" id="btnConfermaArchiviazione" onclick="eseguiArchiviazioneConfermata()">
                        <i class="fas fa-archive me-1"></i>Sì, Archivia
                    </button>
                </div>
            </div>
        </div>
    </div>

{% endblock %}

{% block extra_js %}
<script src="/static/js/sof-realizzati.js"></script>
{% endblock %}
