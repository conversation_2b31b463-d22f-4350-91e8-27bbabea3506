#!/usr/bin/env python3
"""
Gestione sessioni utente per SNIP
Sistema completo di autenticazione con JWT tokens e gestione stato utente
"""

from datetime import datetime, timedelta
from typing import Optional, Dict, Any
import json
import base64
import hmac
import hashlib
from fastapi import HTTPException, status, Depends, Request, Response
from fastapi.security import H<PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.orm import Session
from models import Agente, RuoloEnum
from database import get_db
import secrets
import logging

# Configurazione JWT
SECRET_KEY = "snip_maritime_secret_key_2024_very_secure_change_in_production"
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 480  # 8 ore
REFRESH_TOKEN_EXPIRE_DAYS = 7      # 7 giorni

# Setup logging
logger = logging.getLogger(__name__)

# Security scheme
security = HTTPBearer(auto_error=False)

def simple_jwt_encode(payload: dict, secret: str) -> str:
    """Semplice implementazione JWT senza dipendenze esterne"""
    import json
    import base64
    import hmac
    import hashlib

    # Header
    header = {"alg": "HS256", "typ": "JWT"}
    header_encoded = base64.urlsafe_b64encode(json.dumps(header).encode()).decode().rstrip('=')

    # Payload
    payload_encoded = base64.urlsafe_b64encode(json.dumps(payload, default=str).encode()).decode().rstrip('=')

    # Signature
    message = f"{header_encoded}.{payload_encoded}"
    signature = hmac.new(secret.encode(), message.encode(), hashlib.sha256).digest()
    signature_encoded = base64.urlsafe_b64encode(signature).decode().rstrip('=')

    return f"{header_encoded}.{payload_encoded}.{signature_encoded}"

def simple_jwt_decode(token: str, secret: str) -> Optional[dict]:
    """Semplice decodifica JWT senza dipendenze esterne"""
    import json
    import base64
    import hmac
    import hashlib

    try:
        # Split token
        parts = token.split('.')
        if len(parts) != 3:
            return None

        header_encoded, payload_encoded, signature_encoded = parts

        # Verifica signature
        message = f"{header_encoded}.{payload_encoded}"
        expected_signature = hmac.new(secret.encode(), message.encode(), hashlib.sha256).digest()
        expected_signature_encoded = base64.urlsafe_b64encode(expected_signature).decode().rstrip('=')

        if signature_encoded != expected_signature_encoded:
            return None

        # Decodifica payload
        # Aggiungi padding se necessario
        payload_encoded += '=' * (4 - len(payload_encoded) % 4)
        payload_bytes = base64.urlsafe_b64decode(payload_encoded)
        payload = json.loads(payload_bytes.decode())

        # Verifica scadenza
        if 'exp' in payload:
            exp_timestamp = payload['exp']
            if isinstance(exp_timestamp, str):
                from datetime import datetime
                exp_datetime = datetime.fromisoformat(exp_timestamp.replace('Z', '+00:00'))
                if exp_datetime < datetime.now():
                    return None
            elif isinstance(exp_timestamp, (int, float)):
                from datetime import datetime
                if datetime.fromtimestamp(exp_timestamp) < datetime.now():
                    return None

        return payload

    except Exception as e:
        logger.warning(f"Errore decodifica token: {str(e)}")
        return None

class SessionManager:
    """Gestore delle sessioni utente"""
    
    def __init__(self):
        self.active_sessions: Dict[str, Dict[str, Any]] = {}
    
    def create_access_token(self, data: dict, expires_delta: Optional[timedelta] = None) -> str:
        """Crea un token di accesso JWT"""
        to_encode = data.copy()
        
        if expires_delta:
            expire = datetime.utcnow() + expires_delta
        else:
            expire = datetime.utcnow() + timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
        
        to_encode.update({"exp": expire.isoformat(), "type": "access"})
        
        # Aggiungi session_id univoco
        session_id = secrets.token_urlsafe(32)
        to_encode.update({"session_id": session_id})
        
        encoded_jwt = simple_jwt_encode(to_encode, SECRET_KEY)
        
        # Salva sessione attiva
        self.active_sessions[session_id] = {
            "user_id": data.get("sub"),
            "email": data.get("email"),
            "ruolo": data.get("ruolo"),
            "reparto": data.get("reparto"),
            "created_at": datetime.utcnow(),
            "expires_at": expire,
            "last_activity": datetime.utcnow()
        }
        
        logger.info(f"Sessione creata per utente {data.get('email')} - Session ID: {session_id}")
        return encoded_jwt
    
    def create_refresh_token(self, data: dict) -> str:
        """Crea un token di refresh"""
        to_encode = data.copy()
        expire = datetime.utcnow() + timedelta(days=REFRESH_TOKEN_EXPIRE_DAYS)
        to_encode.update({"exp": expire.isoformat(), "type": "refresh"})
        
        return simple_jwt_encode(to_encode, SECRET_KEY)
    
    def verify_token(self, token: str) -> Optional[dict]:
        """Verifica e decodifica un token JWT"""
        try:
            payload = simple_jwt_decode(token, SECRET_KEY)

            if not payload:
                return None

            # Verifica che sia un token di accesso
            if payload.get("type") != "access":
                return None

            # Verifica che la sessione sia ancora attiva
            session_id = payload.get("session_id")
            if session_id and session_id in self.active_sessions:
                # Aggiorna ultima attività
                self.active_sessions[session_id]["last_activity"] = datetime.utcnow()
                return payload

            return None

        except Exception as e:
            logger.warning(f"Errore verifica token: {str(e)}")
            return None
    
    def invalidate_session(self, session_id: str) -> bool:
        """Invalida una sessione specifica"""
        if session_id in self.active_sessions:
            del self.active_sessions[session_id]
            logger.info(f"Sessione invalidata: {session_id}")
            return True
        return False
    
    def invalidate_user_sessions(self, user_id: int) -> int:
        """Invalida tutte le sessioni di un utente"""
        sessions_to_remove = []
        
        for session_id, session_data in self.active_sessions.items():
            if session_data.get("user_id") == str(user_id):
                sessions_to_remove.append(session_id)
        
        for session_id in sessions_to_remove:
            del self.active_sessions[session_id]
        
        logger.info(f"Invalidate {len(sessions_to_remove)} sessioni per utente {user_id}")
        return len(sessions_to_remove)
    
    def cleanup_expired_sessions(self) -> int:
        """Rimuove le sessioni scadute"""
        now = datetime.utcnow()
        expired_sessions = []
        
        for session_id, session_data in self.active_sessions.items():
            if session_data.get("expires_at", now) < now:
                expired_sessions.append(session_id)
        
        for session_id in expired_sessions:
            del self.active_sessions[session_id]
        
        if expired_sessions:
            logger.info(f"Rimosse {len(expired_sessions)} sessioni scadute")
        
        return len(expired_sessions)
    
    def get_active_sessions(self) -> Dict[str, Dict[str, Any]]:
        """Ottieni tutte le sessioni attive"""
        self.cleanup_expired_sessions()
        return self.active_sessions.copy()
    
    def get_user_sessions(self, user_id: int) -> Dict[str, Dict[str, Any]]:
        """Ottieni le sessioni attive di un utente specifico"""
        self.cleanup_expired_sessions()
        user_sessions = {}
        
        for session_id, session_data in self.active_sessions.items():
            if session_data.get("user_id") == str(user_id):
                user_sessions[session_id] = session_data
        
        return user_sessions

# Istanza globale del session manager
session_manager = SessionManager()

def create_user_tokens(user: Agente) -> Dict[str, str]:
    """Crea tokens per un utente autenticato"""
    # Gestisce sia enum che stringhe per reparto e ruolo
    ruolo_value = user.ruolo.value if hasattr(user.ruolo, 'value') else str(user.ruolo) if user.ruolo else "USER"
    reparto_value = user.reparto.value if hasattr(user.reparto, 'value') else str(user.reparto) if user.reparto else None

    user_data = {
        "sub": str(user.id_user),
        "email": user.email,
        "nome": user.Nome,
        "cognome": user.Cognome,
        "ruolo": ruolo_value,
        "reparto": reparto_value
    }
    
    access_token = session_manager.create_access_token(user_data)
    refresh_token = session_manager.create_refresh_token(user_data)
    
    return {
        "access_token": access_token,
        "refresh_token": refresh_token,
        "token_type": "bearer"
    }

def get_current_user(
    request: Request,
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security),
    db: Session = Depends(get_db)
) -> Optional[Agente]:
    """Ottieni l'utente corrente dalla sessione"""
    
    # Prova prima con il token Authorization header
    token = None
    if credentials:
        token = credentials.credentials
    
    # Se non c'è token nell'header, prova nei cookies
    if not token:
        token = request.cookies.get("access_token")
    
    if not token:
        return None
    
    # Verifica il token
    payload = session_manager.verify_token(token)
    if not payload:
        return None
    
    # Ottieni l'utente dal database
    user_id = payload.get("sub")
    if not user_id:
        return None
    
    try:
        user = db.query(Agente).filter(Agente.id_user == int(user_id)).first()
        
        # Verifica che l'utente sia ancora visibile
        if user and user.visibile == 'no':
            # Invalida la sessione se l'utente è stato disattivato
            session_id = payload.get("session_id")
            if session_id:
                session_manager.invalidate_session(session_id)
            return None
        
        return user
        
    except Exception as e:
        logger.error(f"Errore recupero utente: {str(e)}")
        return None

def require_auth(
    request: Request,
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security),
    db: Session = Depends(get_db)
) -> Agente:
    """Richiede autenticazione obbligatoria"""
    user = get_current_user(request, credentials, db)

    if not user:
        # Messaggio più user-friendly basato sul contesto
        user_agent = request.headers.get("user-agent", "").lower()
        is_mobile = any(device in user_agent for device in ["mobile", "android", "iphone", "ipad"])
        is_api_request = (
            request.headers.get("accept", "").startswith("application/json") or
            request.url.path.startswith("/api/") or
            "ajax" in request.headers.get("x-requested-with", "").lower()
        )

        if is_api_request:
            # Per richieste API, messaggio tecnico ma chiaro
            detail_message = "Sessione scaduta. Effettua nuovamente l'accesso."
        elif is_mobile:
            # Per dispositivi mobili, messaggio breve
            detail_message = "Accesso scaduto. Rieffettua il login."
        else:
            # Per desktop, messaggio più dettagliato
            detail_message = "La tua sessione è scaduta per motivi di sicurezza. Effettua nuovamente l'accesso per continuare."

        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=detail_message,
            headers={"WWW-Authenticate": "Bearer"},
        )

    return user

def require_role(required_role: RuoloEnum):
    """Decorator per richiedere un ruolo specifico"""
    def role_checker(current_user: Agente = Depends(require_auth)) -> Agente:
        if current_user.ruolo != required_role and current_user.ruolo != RuoloEnum.SUPER_ADMIN:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Accesso negato. Richiesto ruolo: {required_role.value}"
            )
        return current_user
    
    return role_checker

def require_min_role(min_role: RuoloEnum):
    """Decorator per richiedere un ruolo minimo"""
    role_hierarchy = {
        RuoloEnum.VISITOR: 0,
        RuoloEnum.USER: 1,
        RuoloEnum.ADMIN: 2,
        RuoloEnum.SUPER_ADMIN: 3
    }

    def role_checker(current_user: Agente = Depends(require_auth)) -> Agente:
        user_level = role_hierarchy.get(current_user.ruolo, 0)
        required_level = role_hierarchy.get(min_role, 0)

        if user_level < required_level:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Accesso negato. Richiesto ruolo minimo: {min_role.value}"
            )
        return current_user

    return role_checker

def require_reparto_access(required_reparto):
    """
    Decorator per richiedere accesso a un reparto specifico.
    Solo gli utenti del reparto specifico o SUPER_ADMIN possono accedere.
    """
    from models import RepartoEnum

    def reparto_checker(current_user: Agente = Depends(require_auth)) -> Agente:
        # SUPER_ADMIN può accedere a tutto
        if current_user.ruolo == RuoloEnum.SUPER_ADMIN:
            logger.info(f"SUPER_ADMIN {current_user.email} accede a {required_reparto}")
            return current_user

        # Gestisce sia enum che stringhe per il reparto richiesto
        if hasattr(required_reparto, 'value'):
            required_reparto_value = required_reparto.value
        else:
            required_reparto_value = str(required_reparto)

        # Gestisce sia enum che stringhe per il reparto utente
        if hasattr(current_user.reparto, 'value'):
            user_reparto_value = current_user.reparto.value
        else:
            user_reparto_value = str(current_user.reparto)

        # Verifica corrispondenza reparto
        if user_reparto_value != required_reparto_value:
            logger.warning(f"Accesso negato: utente {current_user.email} (reparto: {user_reparto_value}) ha tentato di accedere a {required_reparto_value}")

            # Messaggi personalizzati per ogni reparto
            reparto_names = {
                "OPERATIVO": "Operativo",
                "CONTABILITA": "Contabilità",
                "SHORTSEA": "Shortsea",
                "AMMINISTRAZIONE": "Amministrazione"
            }

            reparto_friendly = reparto_names.get(required_reparto_value, required_reparto_value)
            user_reparto_friendly = reparto_names.get(user_reparto_value, user_reparto_value)

            # Messaggio carino e user-friendly
            friendly_message = f"🚫 Ops! Questa sezione è riservata al reparto {reparto_friendly}. Tu appartieni al reparto {user_reparto_friendly} e puoi accedere solo alle tue sezioni dedicate. Per assistenza, contatta l'amministratore."

            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=friendly_message
            )

        logger.info(f"Accesso consentito: utente {current_user.email} (reparto: {user_reparto_value}) accede a {required_reparto_value}")
        return current_user

    return reparto_checker

def logout_user(request: Request, response: Response) -> bool:
    """Effettua logout dell'utente corrente"""
    # Ottieni il token
    token = request.cookies.get("access_token")
    if not token:
        auth_header = request.headers.get("Authorization")
        if auth_header and auth_header.startswith("Bearer "):
            token = auth_header.split(" ")[1]
    
    if token:
        # Verifica e invalida la sessione
        payload = session_manager.verify_token(token)
        if payload:
            session_id = payload.get("session_id")
            if session_id:
                session_manager.invalidate_session(session_id)
        
        # Rimuovi cookie
        response.delete_cookie("access_token")
        response.delete_cookie("refresh_token")
        
        logger.info(f"Logout effettuato per token: {token[:20]}...")
        return True
    
    return False

def set_auth_cookies(response: Response, tokens: Dict[str, str]) -> None:
    """Imposta i cookie di autenticazione"""
    # Cookie per access token (8 ore)
    response.set_cookie(
        key="access_token",
        value=tokens["access_token"],
        max_age=ACCESS_TOKEN_EXPIRE_MINUTES * 60,
        httponly=True,
        secure=False,  # Cambiare a True in produzione con HTTPS
        samesite="lax"
    )
    
    # Cookie per refresh token (7 giorni)
    response.set_cookie(
        key="refresh_token",
        value=tokens["refresh_token"],
        max_age=REFRESH_TOKEN_EXPIRE_DAYS * 24 * 60 * 60,
        httponly=True,
        secure=False,  # Cambiare a True in produzione con HTTPS
        samesite="lax"
    )

def get_session_info(request: Request) -> Optional[Dict[str, Any]]:
    """Ottieni informazioni sulla sessione corrente"""
    token = request.cookies.get("access_token")
    if not token:
        auth_header = request.headers.get("Authorization")
        if auth_header and auth_header.startswith("Bearer "):
            token = auth_header.split(" ")[1]
    
    if not token:
        return None
    
    payload = session_manager.verify_token(token)
    if not payload:
        return None
    
    session_id = payload.get("session_id")
    if session_id and session_id in session_manager.active_sessions:
        session_data = session_manager.active_sessions[session_id].copy()
        session_data["session_id"] = session_id
        return session_data
    
    return None
