#!/usr/bin/env python3
"""
Diagnosi completa del problema invio email di test
"""

from datetime import datetime

def analyze_email_test_system():
    """Analizza il sistema di test email"""
    print("DIAGNOSI SISTEMA EMAIL DI TEST")
    print("=" * 50)
    print(f"Timestamp: {datetime.now()}")
    print()
    
    try:
        with open('main.py', 'r', encoding='utf-8') as f:
            main_content = f.read()
        
        print("1. Verifica API test email:")
        print("-" * 30)
        
        # Verifica presenza componenti
        api_checks = [
            ("Endpoint /admin/api/test-email", "@app.post(\"/admin/api/test-email\")" in main_content),
            ("Funzione api_test_email", "async def api_test_email(" in main_content),
            ("Funzione send_test_email", "def send_test_email(" in main_content),
            ("Funzione diagnose_email_config", "def diagnose_email_config(" in main_content),
            ("Import smtplib", "import smtplib" in main_content),
            ("Gestione SSL/TLS", "starttls()" in main_content),
            ("Login SMTP", "server.login(" in main_content),
            ("Invio email", "server.sendmail(" in main_content)
        ]
        
        for check_name, result in api_checks:
            status = "OK" if result else "MANCANTE"
            print(f"   {check_name}: {status}")
        
        print("\n2. Verifica configurazioni richieste:")
        print("-" * 30)
        
        # Estrai campi richiesti
        if "required_fields = [" in main_content:
            start = main_content.find("required_fields = [")
            end = main_content.find("]", start) + 1
            required_section = main_content[start:end]
            print(f"   Campi obbligatori trovati:")
            print(f"   {required_section}")
        else:
            print("   Campi obbligatori: NON TROVATI")
        
        print("\n3. Verifica gestione errori:")
        print("-" * 30)
        
        error_checks = [
            ("Try-catch generale", "try:" in main_content and "except Exception as e:" in main_content),
            ("Logging errori", "logger.error(" in main_content),
            ("Suggerimenti Gmail", "Password per le app" in main_content),
            ("Suggerimenti Outlook", "Per Outlook usa porta 587" in main_content),
            ("Verifica SSL", "smtp_ssl" in main_content)
        ]
        
        for check_name, result in error_checks:
            status = "OK" if result else "MANCANTE"
            print(f"   {check_name}: {status}")
        
        return True
        
    except FileNotFoundError:
        print("ERRORE: File main.py non trovato")
        return False
    except Exception as e:
        print(f"ERRORE: {e}")
        return False

def analyze_javascript_test():
    """Analizza il JavaScript per il test email"""
    print("\n4. Verifica JavaScript test email:")
    print("-" * 30)
    
    try:
        with open('static/js/config-management.js', 'r', encoding='utf-8') as f:
            js_content = f.read()
        
        js_checks = [
            ("Funzione testEmailConfiguration", "function testEmailConfiguration()" in js_content),
            ("Raccolta configurazioni", "emailConfig = {" in js_content),
            ("Chiamata API", "fetch('/admin/api/test-email'" in js_content),
            ("Gestione risposta", ".then(response => response.json())" in js_content),
            ("Gestione successo", "if (data.success)" in js_content),
            ("Gestione errori", "data.suggestions" in js_content),
            ("Mostra notifiche", "showNotification(" in js_content)
        ]
        
        for check_name, result in js_checks:
            status = "OK" if result else "MANCANTE"
            print(f"   {check_name}: {status}")
        
        # Verifica campi raccolti
        if "emailConfig = {" in js_content:
            start = js_content.find("emailConfig = {")
            end = js_content.find("};", start) + 2
            config_section = js_content[start:end]
            print(f"\n   Configurazioni raccolte:")
            lines = config_section.split('\n')
            for line in lines[:10]:  # Prime 10 righe
                if line.strip():
                    print(f"   {line.strip()}")
        
        return True
        
    except FileNotFoundError:
        print("   ERRORE: File config-management.js non trovato")
        return False
    except Exception as e:
        print(f"   ERRORE: {e}")
        return False

def check_common_email_issues():
    """Controlla problemi comuni con l'invio email"""
    print("\n5. Problemi comuni invio email:")
    print("-" * 30)
    
    common_issues = [
        "CONFIGURAZIONI SMTP:",
        "- Host SMTP errato (es. smtp.gmail.com per Gmail)",
        "- Porta errata (587 per TLS, 465 per SSL)",
        "- Username/password errati",
        "- SSL/TLS non configurato correttamente",
        "",
        "PROBLEMI GMAIL:",
        "- Autenticazione a 2 fattori non abilitata",
        "- Password per le app non generata",
        "- Accesso app meno sicure bloccato",
        "",
        "PROBLEMI OUTLOOK:",
        "- Porta diversa da 587",
        "- Autenticazione moderna richiesta",
        "",
        "PROBLEMI FIREWALL:",
        "- Porte SMTP bloccate dal firewall",
        "- Connessioni SMTP bloccate dall'antivirus",
        "",
        "PROBLEMI CONFIGURAZIONE:",
        "- Email mittente diversa da username SMTP",
        "- Campi obbligatori vuoti",
        "- Formato email non valido"
    ]
    
    for issue in common_issues:
        print(f"   {issue}")

def provide_solutions():
    """Fornisce soluzioni per i problemi email"""
    print("\n6. Soluzioni consigliate:")
    print("-" * 30)
    
    solutions = [
        "VERIFICA CONFIGURAZIONI:",
        "1. Controllare che tutti i campi siano compilati",
        "2. Verificare host SMTP corretto per il provider",
        "3. Controllare porta (587 per TLS, 465 per SSL)",
        "4. Verificare username e password",
        "",
        "PER GMAIL:",
        "1. Abilitare autenticazione a 2 fattori",
        "2. Generare 'Password per le app' in Google Account",
        "3. Usare la password per le app invece della password normale",
        "4. Host: smtp.gmail.com, Porta: 587, SSL: abilitato",
        "",
        "PER OUTLOOK/HOTMAIL:",
        "1. Host: smtp-mail.outlook.com",
        "2. Porta: 587",
        "3. SSL: abilitato",
        "4. Usare email e password normali",
        "",
        "TEST MANUALE:",
        "1. Aprire dashboard amministrazione",
        "2. Andare su Configurazione Email",
        "3. Compilare tutti i campi SMTP",
        "4. Cliccare 'Test Email'",
        "5. Controllare console browser per errori",
        "6. Verificare log server per dettagli errore"
    ]
    
    for solution in solutions:
        print(f"   {solution}")

def main():
    print("DIAGNOSI COMPLETA PROBLEMA EMAIL DI TEST")
    print("=" * 60)
    
    results = []
    
    # Test sistema email
    try:
        email_result = analyze_email_test_system()
        results.append(("Email System", email_result))
    except Exception as e:
        print(f"ERRORE test email system: {e}")
        results.append(("Email System", False))
    
    # Test JavaScript
    try:
        js_result = analyze_javascript_test()
        results.append(("JavaScript Test", js_result))
    except Exception as e:
        print(f"ERRORE test JavaScript: {e}")
        results.append(("JavaScript Test", False))
    
    # Problemi comuni
    check_common_email_issues()
    
    # Soluzioni
    provide_solutions()
    
    # Riepilogo
    print("\n" + "=" * 60)
    print("RIEPILOGO DIAGNOSI")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "OK" if result else "PROBLEMA"
        print(f"{test_name:<20} {status}")
        if result:
            passed += 1
    
    print(f"\nComponenti verificati: {passed}/{total}")
    
    if passed == total:
        print("\nSTATO: SISTEMA EMAIL IMPLEMENTATO CORRETTAMENTE")
        print("Il problema potrebbe essere nelle configurazioni SMTP.")
    else:
        print("\nSTATO: PROBLEMI NEL CODICE RILEVATI")
        print("Verificare i componenti che hanno fallito.")
    
    print("\nPROSSIMI PASSI PER RISOLVERE:")
    print("1. Verificare configurazioni SMTP nel dashboard")
    print("2. Controllare log browser (F12 -> Console)")
    print("3. Controllare log server per errori dettagliati")
    print("4. Testare con configurazioni Gmail/Outlook note")
    print("5. Verificare firewall e antivirus")

if __name__ == "__main__":
    main()
