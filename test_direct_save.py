#!/usr/bin/env python3
"""
Test diretto delle funzioni di salvataggio configurazioni
"""

from database import SessionLocal
from main import save_database_config, save_reporting_config, save_system_db_config
from sqlalchemy import text
from datetime import datetime

def test_direct_save():
    """Test diretto delle funzioni di salvataggio"""
    print("🔧 Testing Direct Save Functions...")
    
    db = SessionLocal()
    try:
        # Test 1: Database config
        print("\n1. Test save_database_config...")
        database_config = {
            "backup_schedule": "daily",
            "backup_time": "02:00",
            "backup_retention": 30,
            "backup_path": "/backups/snip/",
            "compress_backup": True,
            "log_cleanup": "daily",
            "archive_months": 24,
            "optimize": "weekly",
            "auto_vacuum": True,
            "analyze": True,
            "disk_threshold": 85,
            "connection_threshold": 80,
            "monitor_performance": True,
            "alert_email": True
        }
        
        result = save_database_config(database_config, "test_user", db)
        print(f"   Result: {result}")
        
        # Verifica salvataggio
        result = db.execute(text("""
            SELECT COUNT(*) FROM "SYSTEM_CONFIG" WHERE config_key LIKE 'database_%'
        """))
        count = result.scalar()
        print(f"   Database configs saved: {count}")
        
        # Test 2: Reporting config
        print("\n2. Test save_reporting_config...")
        reporting_config = {
            "daily_report": "enabled",
            "daily_report_time": "08:00",
            "weekly_report": "monday",
            "monthly_report": "first",
            "kpi_voyages": True,
            "kpi_sof": True,
            "kpi_users": True,
            "kpi_ports": True,
            "kpi_performance": True,
            "kpi_revenue": False,
            "export_format": "excel",
            "auto_export": True,
            "compress_exports": False,
            "report_recipients": "<EMAIL>",
            "include_charts": True,
            "detailed_reports": False
        }
        
        result = save_reporting_config(reporting_config, "test_user", db)
        print(f"   Result: {result}")
        
        # Verifica salvataggio
        result = db.execute(text("""
            SELECT COUNT(*) FROM "SYSTEM_CONFIG" WHERE config_key LIKE 'reporting_%'
        """))
        count = result.scalar()
        print(f"   Reporting configs saved: {count}")
        
        # Test 3: System config
        print("\n3. Test save_system_db_config...")
        system_config = {
            "app_version": "v2.0.0",
            "debug_mode": "disabled",
            "log_level": "INFO",
            "max_upload_size": 50,
            "request_timeout": 30,
            "max_connections": 100,
            "cache_enabled": True,
            "compression_enabled": True,
            "maintenance_mode": "disabled",
            "maintenance_message": "Sistema in manutenzione test",
            "auto_updates": False
        }
        
        result = save_system_db_config(system_config, "test_user", db)
        print(f"   Result: {result}")
        
        # Verifica salvataggio
        result = db.execute(text("""
            SELECT COUNT(*) FROM "SYSTEM_CONFIG" WHERE config_key LIKE 'system_%'
        """))
        count = result.scalar()
        print(f"   System configs saved: {count}")
        
        # Test 4: Verifica totale
        print("\n4. Verifica totale configurazioni...")
        result = db.execute(text("""
            SELECT config_key, config_value
            FROM "SYSTEM_CONFIG"
            WHERE config_key LIKE 'database_%' OR config_key LIKE 'reporting_%' OR config_key LIKE 'system_%'
            ORDER BY config_key
        """))
        
        configs = result.fetchall()
        print(f"   Totale configurazioni: {len(configs)}")
        
        # Raggruppa per sezione
        sections = {}
        for key, value in configs:
            section = key.split('_')[0]
            if section not in sections:
                sections[section] = []
            sections[section].append((key, value))
        
        for section, items in sections.items():
            print(f"   - {section}: {len(items)} configurazioni")
            if items:
                print(f"     Esempi: {[item[0] for item in items[:3]]}")
        
        return len(configs) > 0
        
    except Exception as e:
        print(f"   ❌ Errore: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        db.close()

def main():
    print("🧪 TESTING DIRECT SAVE FUNCTIONS")
    print("=" * 50)
    print(f"Timestamp: {datetime.now()}")
    print()
    
    try:
        result = test_direct_save()
        if result:
            print("\n✅ Test completato con successo!")
        else:
            print("\n❌ Test fallito!")
    except Exception as e:
        print(f"❌ Errore generale: {e}")

if __name__ == "__main__":
    main()
