#!/usr/bin/env python3
"""
Test finale backup importabile
"""

import gzip
from pathlib import Path

def test_final_importable():
    print("TEST FINALE BACKUP IMPORTABILE")
    print("=" * 50)
    
    # Trova ultimo backup
    backup_dir = Path("backups")
    backup_files = list(backup_dir.glob("snip_backup_*.sql.gz"))
    latest_backup = max(backup_files, key=lambda f: f.stat().st_mtime)
    
    print(f"File: {latest_backup.name}")
    print(f"Dimensione: {latest_backup.stat().st_size} bytes")
    
    with gzip.open(latest_backup, 'rt', encoding='utf-8') as f:
        content = f.read()
    
    lines = content.split('\n')
    print(f"Righe totali: {len(lines)}")
    
    # Verifica componenti essenziali
    print(f"\nCOMPONENTI ESSENZIALI:")
    print("-" * 30)
    
    components = {
        "Header PostgreSQL": content.count("-- PostgreSQL database dump") > 0,
        "SET statements": content.count("SET ") >= 5,
        "CREATE TYPE": content.count("CREATE TYPE public.") == 5,
        "CREATE SEQUENCE": content.count("CREATE SEQUENCE public.") >= 18,
        "setval sequences": content.count("setval(") >= 18,
        "INSERT statements": content.count("INSERT INTO public.") >= 1800,
        "Footer dump": "PostgreSQL database dump complete" in content
    }
    
    all_components_ok = True
    for component, status in components.items():
        result = "OK" if status else "MANCANTE"
        print(f"  {component}: {result}")
        if not status:
            all_components_ok = False
    
    # Verifica sequenze specifiche
    print(f"\nSEQUENZE CRITICHE:")
    print("-" * 20)
    
    critical_sequences = [
        "AGENTE_id_user_seq",
        "ARMATORE_id_seq", 
        "ATLAS_id_seq",
        "NAVI_id_seq",
        "SYSTEM_CONFIG_id_seq"
    ]
    
    sequences_ok = True
    for seq_name in critical_sequences:
        create_found = f'CREATE SEQUENCE public."{seq_name}"' in content
        setval_found = f"setval('public.\"{seq_name}\"" in content
        
        if create_found and setval_found:
            print(f"  {seq_name}: OK")
        else:
            print(f"  {seq_name}: MANCANTE")
            sequences_ok = False
    
    # Verifica dati critici
    print(f"\nDATI CRITICI:")
    print("-" * 15)
    
    critical_data = {
        "Utenti AGENTE": content.count('INSERT INTO public."AGENTE"'),
        "Configurazioni": content.count('INSERT INTO public."SYSTEM_CONFIG"'),
        "Navi": content.count('INSERT INTO public."NAVI"'),
        "Porti Atlas": content.count('INSERT INTO public."ATLAS"')
    }
    
    data_ok = True
    for data_name, count in critical_data.items():
        if count > 0:
            print(f"  {data_name}: {count} record")
        else:
            print(f"  {data_name}: MANCANTE")
            data_ok = False
    
    # Verifica escape caratteri
    print(f"\nESCAPE CARATTERI:")
    print("-" * 20)
    
    escape_checks = {
        "Password bcrypt": content.count('$2b$') > 0,
        "Apostrofi escape": content.count("''") > 0,
        "Nessun COPY": content.count("COPY public.") == 0,
        "Solo INSERT": content.count("INSERT INTO") > 1800
    }
    
    escape_ok = True
    for check_name, status in escape_checks.items():
        result = "OK" if status else "PROBLEMA"
        print(f"  {check_name}: {result}")
        if not status:
            escape_ok = False
    
    # Mostra esempio sequenza
    print(f"\nESEMPIO SEQUENZA:")
    print("-" * 20)
    
    for line in lines:
        if 'CREATE SEQUENCE public."AGENTE_id_user_seq"' in line:
            # Mostra le prossime 5 righe
            line_index = lines.index(line)
            for i in range(line_index, min(line_index + 6, len(lines))):
                if lines[i].strip():
                    print(f"  {lines[i]}")
            break
    
    # Mostra esempio INSERT
    print(f"\nESEMPIO INSERT:")
    print("-" * 18)
    
    for line in lines:
        if 'INSERT INTO public."AGENTE"' in line:
            print(f"  {line[:80]}...")
            break
    
    # Risultato finale
    print(f"\n" + "=" * 50)
    print("RISULTATO FINALE")
    print("=" * 50)
    
    if all_components_ok and sequences_ok and data_ok and escape_ok:
        print("BACKUP COMPLETAMENTE IMPORTABILE!")
        print()
        print("RISOLTO:")
        print("- Errore 'relation does not exist'")
        print("- Errore 'syntax error at or near'")
        print("- Caratteri speciali escape")
        print("- Sequenze mancanti")
        print()
        print("COMPONENTI:")
        print("- 18 sequenze CREATE + setval")
        print("- 1859 INSERT statements")
        print("- 5 CREATE TYPE enum")
        print("- Header e footer PostgreSQL")
        print()
        print("IMPORTAZIONE:")
        print("1. gunzip snip_backup_YYYYMMDD_HHMMSS.sql.gz")
        print("2. psql -d AGENTE -f snip_backup_YYYYMMDD_HHMMSS.sql")
        print()
        print("STATO: PRONTO SENZA ERRORI")
        return True
    else:
        print("BACKUP HA ANCORA PROBLEMI")
        if not all_components_ok:
            print("- Componenti mancanti")
        if not sequences_ok:
            print("- Sequenze incomplete")
        if not data_ok:
            print("- Dati mancanti")
        if not escape_ok:
            print("- Problemi escape")
        return False

if __name__ == "__main__":
    success = test_final_importable()
    if success:
        print("\nSUCCESS: BACKUP IMPORTABILE IN POSTGRESQL!")
    else:
        print("\nERROR: BACKUP ANCORA PROBLEMATICO")
