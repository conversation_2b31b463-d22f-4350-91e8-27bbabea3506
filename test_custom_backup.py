#!/usr/bin/env python3
"""
Test backup formato custom per pg_restore
"""

from backup_manager import BackupManager
import logging

# Configura logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

def test_custom_backup():
    print("TEST BACKUP FORMATO CUSTOM")
    print("=" * 40)
    
    try:
        # Inizializza backup manager
        db_url = "postgresql://re77:271077@localhost:5432/AGENTE"
        backup_manager = BackupManager(db_url)
        
        print("Creazione backup formato custom...")
        
        # Crea backup in formato custom (per pg_restore)
        backup_path = backup_manager.create_backup(format_type="custom")
        
        if backup_path:
            print(f"OK Backup custom creato: {backup_path}")

            # Verifica file
            from pathlib import Path
            backup_file = Path(backup_path)

            if backup_file.exists():
                file_size = backup_file.stat().st_size
                print(f"OK File esistente: {file_size} bytes")

                # Verifica estensione
                if backup_file.suffix == '.gz':
                    print("OK File compresso (.gz)")
                    # Decomprime per verificare
                    import gzip
                    try:
                        with gzip.open(backup_file, 'rb') as f:
                            header = f.read(10)
                        print(f"OK Header file: {header[:5]}")
                    except Exception as e:
                        print(f"ERROR Errore lettura file compresso: {e}")
                elif backup_file.suffix == '.dump':
                    print("OK File formato dump")
                    # Verifica header pg_dump custom
                    try:
                        with open(backup_file, 'rb') as f:
                            header = f.read(10)
                        print(f"OK Header file: {header}")
                    except Exception as e:
                        print(f"ERROR Errore lettura file dump: {e}")
                else:
                    print(f"? Estensione inaspettata: {backup_file.suffix}")
                
                print(f"\nINFORMAZIONI BACKUP:")
                print(f"- Nome file: {backup_file.name}")
                print(f"- Dimensione: {file_size / 1024:.1f} KB")
                print(f"- Formato: {'Custom (pg_restore)' if '.dump' in backup_file.name else 'SQL (psql)'}")
                
                print(f"\nCOMANDO IMPORTAZIONE:")
                if '.dump' in backup_file.name:
                    if backup_file.suffix == '.gz':
                        print("1. gunzip " + backup_file.name)
                        print("2. pg_restore -d AGENTE " + backup_file.stem)
                    else:
                        print("pg_restore -d AGENTE " + backup_file.name)
                else:
                    print("psql -d AGENTE -f " + backup_file.name)
                
                return True
            else:
                print(f"✗ File non esistente: {backup_path}")
                return False
        else:
            print("ERROR Backup fallito")
            return False

    except Exception as e:
        print(f"ERROR Errore durante test: {e}")
        return False

def test_sql_backup():
    print("\nTEST BACKUP FORMATO SQL")
    print("=" * 40)
    
    try:
        # Inizializza backup manager
        db_url = "postgresql://re77:271077@localhost:5432/AGENTE"
        backup_manager = BackupManager(db_url)
        
        print("Creazione backup formato SQL...")
        
        # Crea backup in formato SQL (default)
        backup_path = backup_manager.create_backup(format_type="sql")
        
        if backup_path:
            print(f"✓ Backup SQL creato: {backup_path}")
            
            # Verifica file
            from pathlib import Path
            backup_file = Path(backup_path)
            
            if backup_file.exists():
                file_size = backup_file.stat().st_size
                print(f"✓ File esistente: {file_size} bytes")
                print(f"✓ Formato SQL per psql")
                
                print(f"\nCOMANDO IMPORTAZIONE:")
                if backup_file.suffix == '.gz':
                    print("1. gunzip " + backup_file.name)
                    print("2. psql -d AGENTE -f " + backup_file.stem)
                else:
                    print("psql -d AGENTE -f " + backup_file.name)
                
                return True
            else:
                print(f"✗ File non esistente: {backup_path}")
                return False
        else:
            print("✗ Backup fallito")
            return False
            
    except Exception as e:
        print(f"✗ Errore durante test: {e}")
        return False

def main():
    print("TEST BACKUP MANAGER - FORMATI MULTIPLI")
    print("=" * 50)
    
    # Test formato custom
    custom_success = test_custom_backup()
    
    # Test formato SQL
    sql_success = test_sql_backup()
    
    print(f"\n" + "=" * 50)
    print("RISULTATI FINALI")
    print("=" * 50)
    
    print(f"Backup Custom: {'✓ SUCCESSO' if custom_success else '✗ FALLITO'}")
    print(f"Backup SQL: {'✓ SUCCESSO' if sql_success else '✗ FALLITO'}")
    
    if custom_success:
        print(f"\n✓ FORMATO CUSTOM DISPONIBILE")
        print("  - Compatibile con pg_restore")
        print("  - Formato binario compatto")
        print("  - Ripristino selettivo possibile")
    
    if sql_success:
        print(f"\n✓ FORMATO SQL DISPONIBILE")
        print("  - Compatibile con psql")
        print("  - Formato testo leggibile")
        print("  - Modificabile manualmente")
    
    if custom_success and sql_success:
        print(f"\n🎉 ENTRAMBI I FORMATI FUNZIONANTI!")
        print("Scegli il formato in base alle necessità:")
        print("- Custom per pg_restore (più veloce)")
        print("- SQL per psql (più flessibile)")
    elif custom_success:
        print(f"\n⚠ Solo formato custom disponibile")
        print("pg_dump funziona, SQLAlchemy fallback no")
    elif sql_success:
        print(f"\n⚠ Solo formato SQL disponibile")
        print("SQLAlchemy fallback funziona, pg_dump custom no")
    else:
        print(f"\n❌ NESSUN FORMATO FUNZIONANTE")
        print("Verificare configurazione PostgreSQL")

if __name__ == "__main__":
    main()
