// JavaScript per gestione import dati viaggio
document.addEventListener('DOMContentLoaded', function() {
    const importForm = document.getElementById('importForm');
    const fileInput = document.getElementById('file_upload');
    const previewContainer = document.getElementById('preview_container');
    const validateBtn = document.getElementById('validate_btn');
    const importBtn = document.getElementById('import_btn');
    const importStatusText = document.getElementById('import_status_text');
    const rowsCount = document.getElementById('rows_count');
    const importLog = document.getElementById('import_log');
    const deleteImportBtn = document.getElementById('delete_import_btn');

    let uploadedData = null;
    let validatedData = null;

    // Gestione upload file
    importForm.addEventListener('submit', function(e) {
        e.preventDefault();

        if (!fileInput.files[0]) {
            updateStatus('Seleziona un file', 'error');
            addLog('❌ Nessun file selezionato');
            return;
        }

        const formData = new FormData();
        formData.append('file', fileInput.files[0]);

        uploadFile(formData);
    });

    // Funzione upload file
    function uploadFile(formData) {
        updateStatus('Caricamento file...', 'warning');
        addLog('📤 Inizio caricamento file...');

        fetch(`/operativo/sof/viaggio/${VIAGGIO_ID}/import/upload`, {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                uploadedData = data.data;
                showPreview(data.data, data.columns);
                updateStatus('File caricato', 'success');
                addLog(`✅ File caricato con successo: ${data.rows} righe`);
                rowsCount.textContent = data.rows;
                validateBtn.disabled = false;
                updateImportStatusIndicator('green');
            } else {
                updateStatus('Errore caricamento', 'error');
                addLog(`❌ Errore: ${data.message}`);
                updateImportStatusIndicator('red');
            }
        })
        .catch(error => {
            updateStatus('Errore caricamento', 'error');
            addLog(`❌ Errore di rete: ${error.message}`);
            updateImportStatusIndicator('red');
        });
    }

    // Mostra anteprima dati
    function showPreview(data, columns) {
        let html = '<div class="table-responsive">';
        html += '<table class="table table-sm table-striped">';

        // Header
        html += '<thead class="table-dark"><tr>';
        columns.forEach(col => {
            html += `<th class="small">${col}</th>`;
        });
        html += '</tr></thead>';

        // Righe (max 10 per anteprima)
        html += '<tbody>';
        const maxRows = Math.min(data.length, 10);
        for (let i = 0; i < maxRows; i++) {
            html += '<tr>';
            columns.forEach(col => {
                const value = data[i][col] || '';
                html += `<td class="small">${value}</td>`;
            });
            html += '</tr>';
        }
        html += '</tbody></table>';

        if (data.length > 10) {
            html += `<small class="text-muted">Mostrate prime 10 righe di ${data.length}</small>`;
        }

        html += '</div>';
        previewContainer.innerHTML = html;
    }

    // Validazione dati
    validateBtn.addEventListener('click', function() {
        if (!uploadedData) return;

        updateStatus('Validazione in corso...', 'warning');
        addLog('🔍 Inizio validazione dati...');

        fetch(`/operativo/sof/viaggio/${VIAGGIO_ID}/import/validate`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                data: uploadedData
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                validatedData = data.validated_data;
                updateStatus('Dati validati', 'success');
                addLog(`✅ Validazione completata: ${data.valid_rows} righe valide`);

                // Mostra messaggio di successo moderno
                if (typeof mostraSuccesso === 'function') {
                    if (data.errors && data.errors.length > 0) {
                        mostraAvviso(`Validazione completata: ${data.valid_rows} righe valide, ${data.errors.length} avvisi`);
                    } else {
                        mostraSuccesso(`Validazione completata: ${data.valid_rows} righe valide`);
                    }
                }

                if (data.errors && data.errors.length > 0) {
                    data.errors.forEach(error => {
                        addLog(`⚠️ Riga ${error.row}: ${error.message}`);
                    });
                }

                importBtn.disabled = false;
            } else {
                updateStatus('Errori validazione', 'error');
                addLog(`❌ Validazione fallita: ${data.message}`);

                // Mostra messaggio di errore moderno
                if (typeof mostraErrore === 'function') {
                    mostraErrore(data.message || 'Errore durante la validazione');
                }

                updateImportStatusIndicator('red');
            }
        })
        .catch(error => {
            updateStatus('Errore validazione', 'error');
            addLog(`❌ Errore validazione: ${error.message}`);

            // Mostra messaggio di errore moderno
            if (typeof mostraErrore === 'function') {
                mostraErrore('Errore di connessione durante la validazione');
            }

            updateImportStatusIndicator('red');
        });
    });

    // Import dati
    importBtn.addEventListener('click', function() {
        if (!validatedData) return;

        updateStatus('Import in corso...', 'warning');
        addLog('💾 Inizio import dati nel database...');

        fetch(`/operativo/sof/viaggio/${VIAGGIO_ID}/import/execute`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                data: validatedData
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                updateStatus('Import completato', 'success');
                addLog(`🎉 Import completato con successo: ${data.imported_rows} righe importate`);

                // Mostra messaggio di successo moderno
                if (typeof mostraSuccesso === 'function') {
                    mostraSuccesso(`Import completato: ${data.imported_rows} righe importate`);
                }

                // Reset form
                resetImportForm();

                // Aggiorna tab orari
                addLog('🔄 Aggiornamento dati orari...');

                // Carica dati IMPORT aggiornati
                addLog('📦 Caricamento dati IMPORT...');
                loadImportData();

                updateImportStatusIndicator('green');
            } else {
                updateStatus('Errore import', 'error');
                addLog(`❌ Import fallito: ${data.message}`);

                // Mostra messaggio di errore moderno
                if (typeof mostraErrore === 'function') {
                    mostraErrore(data.message || 'Errore durante l\'import');
                }

                updateImportStatusIndicator('red');
            }
        })
        .catch(error => {
            updateStatus('Errore import', 'error');
            addLog(`❌ Errore import: ${error.message}`);

            // Mostra messaggio di errore moderno
            if (typeof mostraErrore === 'function') {
                mostraErrore('Errore di connessione durante l\'import');
            }

            updateImportStatusIndicator('red');
        });
    });

    // Elimina dati IMPORT
    deleteImportBtn.addEventListener('click', function() {
        // Mostra dialog di conferma moderno
        mostraConfermaEliminazione(() => {
            eseguiEliminazione();
        });
    });

    function eseguiEliminazione() {

        updateStatus('Eliminazione in corso...', 'warning');
        addLog('🗑️ Inizio eliminazione dati IMPORT...');

        fetch(`/operativo/sof/viaggio/${VIAGGIO_ID}/import/delete`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                updateStatus('Dati eliminati', 'success');
                addLog(`🎉 Eliminazione completata: ${data.deleted_rows} record eliminati`);

                // Mostra messaggio di successo moderno
                if (typeof mostraSuccesso === 'function') {
                    mostraSuccesso(`Eliminati ${data.deleted_rows} record IMPORT`);
                }

                // Aggiorna visualizzazione
                loadImportData();
                updateImportStatusIndicator('red');
            } else {
                updateStatus('Errore eliminazione', 'error');
                addLog(`❌ Eliminazione fallita: ${data.message}`);

                // Mostra messaggio di errore moderno
                if (typeof mostraErrore === 'function') {
                    mostraErrore(data.message || 'Errore durante l\'eliminazione');
                }
            }
        })
        .catch(error => {
            updateStatus('Errore eliminazione', 'error');
            addLog(`❌ Errore eliminazione: ${error.message}`);

            // Mostra messaggio di errore moderno
            if (typeof mostraErrore === 'function') {
                mostraErrore('Errore di connessione durante l\'eliminazione');
            }
        });
    }

    // Funzioni utility
    function updateStatus(text, type) {
        importStatusText.textContent = text;
        importStatusText.className = type === 'success' ? 'text-success' :
                                    type === 'error' ? 'text-danger' :
                                    type === 'warning' ? 'text-warning' : '';
    }

    function addLog(message) {
        const timestamp = new Date().toLocaleTimeString();
        const logEntry = document.createElement('div');
        logEntry.innerHTML = `<small class="text-white-50">[${timestamp}]</small> ${message}`;
        logEntry.style.marginBottom = '5px';

        importLog.appendChild(logEntry);
        importLog.scrollTop = importLog.scrollHeight;
    }



    function resetImportForm() {
        importForm.reset();
        uploadedData = null;
        validatedData = null;
        validateBtn.disabled = true;
        importBtn.disabled = true;
        rowsCount.textContent = '0';

        previewContainer.innerHTML = `
            <div class="text-center text-muted py-4">
                <div style="font-size: 2em; margin-bottom: 10px;">📋</div>
                <p class="mb-0">Carica un file per vedere l'anteprima</p>
            </div>
        `;
    }

    // Carica dati IMPORT esistenti all'avvio (aggiorna anche la lucina)
    loadImportData();
});

// Funzione globale per aggiornare la lucina IMPORT
function updateImportStatusIndicator(color) {
    const importStatus = document.getElementById('import-status');
    if (importStatus) {
        importStatus.className = `status-indicator status-${color === 'green' ? 'green' : 'red'}`;
        console.log(`🔄 Lucina IMPORT aggiornata: ${color}`);

        // Controlla se mostrare il tab SOF
        if (typeof updateStatusAndCheck === 'function') {
            updateStatusAndCheck();
        }
    } else {
        console.error('❌ Elemento import-status non trovato');
    }
}

// Funzione per caricare i dati IMPORT
function loadImportData() {
    fetch(`/operativo/sof/viaggio/${VIAGGIO_ID}/import/data`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showImportData(data.data);

                // Aggiorna lucina in base ai dati presenti
                if (data.data && data.data.length > 0) {
                    updateImportStatusIndicator('green');
                    console.log(`✅ Dati IMPORT presenti: ${data.data.length} record`);
                } else {
                    updateImportStatusIndicator('red');
                    console.log('❌ Nessun dato IMPORT presente');
                }
            } else {
                console.error('Errore caricamento dati IMPORT:', data.message);
                updateImportStatusIndicator('red');
            }
        })
        .catch(error => {
            console.error('Errore rete caricamento IMPORT:', error);
            updateImportStatusIndicator('red');
        });
}

// Funzione per mostrare i dati IMPORT
function showImportData(importData) {
    const container = document.getElementById('import_data_container');

    if (!importData || importData.length === 0) {
        container.innerHTML = `
            <div class="text-center text-muted py-4">
                <div style="font-size: 2em; margin-bottom: 10px;">📋</div>
                <p class="mb-0">Nessun dato IMPORT presente</p>
            </div>
        `;
        return;
    }

    // Raggruppa i dati per POD
    const groupedByPod = {};
    importData.forEach(row => {
        if (!groupedByPod[row.pod]) {
            groupedByPod[row.pod] = [];
        }
        groupedByPod[row.pod].push(row);
    });

    let html = '<div class="table-responsive">';
    html += '<table class="table table-sm table-striped table-hover">';

    // Header
    html += `
        <thead class="table-dark">
            <tr>
                <th class="small">POL</th>
                <th class="small">POD</th>
                <th class="small">QT</th>
                <th class="small">TYPE</th>
                <th class="small">DATA</th>
            </tr>
        </thead>
    `;

    // Righe dati raggruppate per POD
    html += '<tbody>';

    Object.keys(groupedByPod).forEach(pod => {
        const podRows = groupedByPod[pod];

        podRows.forEach((row, index) => {
            html += `
                <tr>
                    <td class="small fw-bold text-primary">${row.pol}</td>
                    <td class="small fw-bold text-success ${index === 0 ? 'border-top-3' : ''}"
                        style="${index === 0 ? 'border-top: 3px solid #28a745 !important;' : ''}">${row.pod}</td>
                    <td class="small text-center">${row.qt}</td>
                    <td class="small">${row.type}</td>
                    <td class="small text-muted">${row.created_at}</td>
                </tr>
            `;
        });

        // Aggiungi riga separatrice tra gruppi POD diversi
        if (Object.keys(groupedByPod).indexOf(pod) < Object.keys(groupedByPod).length - 1) {
            html += `
                <tr style="height: 8px; background-color: #f8f9fa;">
                    <td colspan="5" style="border: none; padding: 2px;"></td>
                </tr>
            `;
        }
    });

    html += '</tbody></table>';

    // Statistiche
    const totalQt = importData.reduce((sum, row) => sum + row.qt, 0);
    const uniquePods = Object.keys(groupedByPod).length;
    html += `
        <div class="mt-2 p-2 bg-light rounded">
            <small class="text-muted">
                <strong>Totale record:</strong> ${importData.length} |
                <strong>POD diversi:</strong> ${uniquePods} |
                <strong>Quantità totale:</strong> ${totalQt}
            </small>
        </div>
    `;

    html += '</div>';
    container.innerHTML = html;
}
