#!/usr/bin/env python3
"""
Test backup con INSERT statements
"""

import gzip
from pathlib import Path

def test_insert_backup():
    print("TEST BACKUP CON INSERT STATEMENTS")
    print("=" * 50)
    
    # Trova ultimo backup
    backup_dir = Path("backups")
    backup_files = list(backup_dir.glob("snip_backup_*.sql.gz"))
    latest_backup = max(backup_files, key=lambda f: f.stat().st_mtime)
    
    print(f"File: {latest_backup.name}")
    print(f"Dimensione: {latest_backup.stat().st_size} bytes")
    
    with gzip.open(latest_backup, 'rt', encoding='utf-8') as f:
        content = f.read()
    
    lines = content.split('\n')
    print(f"Righe totali: {len(lines)}")
    
    # Verifica INSERT statements
    print(f"\nFORMATO DATI:")
    print("-" * 20)
    
    insert_count = content.count('INSERT INTO public.')
    copy_count = content.count('COPY public.')
    
    print(f"INSERT statements: {insert_count}")
    print(f"COPY statements: {copy_count}")
    
    if insert_count > 0 and copy_count == 0:
        print("✓ Formato INSERT utilizzato (più sicuro)")
    elif copy_count > 0:
        print("⚠ Formato COPY utilizzato (problematico)")
    else:
        print("✗ Nessun dato trovato")
    
    # Verifica escape caratteri speciali
    print(f"\nVERIFICA ESCAPE:")
    print("-" * 20)
    
    # Cerca esempi di password hash bcrypt
    bcrypt_lines = [line for line in lines if '$2b$' in line and 'INSERT INTO' in line]
    
    if bcrypt_lines:
        print(f"Password bcrypt trovate: {len(bcrypt_lines)}")
        print("Esempio escape:")
        for i, line in enumerate(bcrypt_lines[:2], 1):
            # Trova la parte della password
            if '$2b$' in line:
                start = line.find('$2b$')
                end = line.find("'", start)
                if end > start:
                    password_part = line[start:end]
                    print(f"  {i}. {password_part[:30]}...")
    else:
        print("Nessuna password bcrypt trovata")
    
    # Verifica sintassi SQL
    print(f"\nVERIFICA SINTASSI SQL:")
    print("-" * 25)
    
    syntax_checks = [
        ("Header PostgreSQL", "-- PostgreSQL database dump" in content),
        ("CREATE TYPE", content.count("CREATE TYPE public.") == 5),
        ("CREATE SEQUENCE", content.count("CREATE SEQUENCE public.") > 0),
        ("CREATE TABLE", content.count("CREATE TABLE public.") >= 18),
        ("INSERT statements", insert_count >= 1800),  # Circa 1859 righe
        ("Escape apostrofi", "''" in content),  # Doppio apice per escape
        ("Footer completo", "PostgreSQL database dump complete" in content)
    ]
    
    all_syntax_ok = True
    for check_name, check_result in syntax_checks:
        status = "OK" if check_result else "ERRORE"
        print(f"  {check_name}: {status}")
        if not check_result:
            all_syntax_ok = False
    
    # Mostra esempio INSERT
    print(f"\nESEMPIO INSERT STATEMENT:")
    print("-" * 30)
    
    insert_found = False
    for i, line in enumerate(lines):
        if 'INSERT INTO public."AGENTE"' in line:
            print(f"  {line[:100]}...")
            insert_found = True
            break
    
    if not insert_found:
        print("  Nessun INSERT trovato!")
    
    # Test caratteri problematici
    print(f"\nTEST CARATTERI PROBLEMATICI:")
    print("-" * 35)
    
    problematic_chars = {
        "Dollar sign ($)": content.count('$'),
        "Single quote (')": content.count("'"),
        "Double quote (\")": content.count('"'),
        "Backslash (\\)": content.count('\\'),
        "Tab (\\t)": content.count('\\t'),
        "Newline (\\n)": content.count('\\n')
    }
    
    for char_name, count in problematic_chars.items():
        print(f"  {char_name}: {count}")
    
    # Risultato finale
    print(f"\n" + "=" * 50)
    print("RISULTATO FINALE")
    print("=" * 50)
    
    if all_syntax_ok and insert_count > 0:
        print("BACKUP CON INSERT STATEMENTS: PERFETTO!")
        print()
        print("VANTAGGI INSERT:")
        print("- Escape automatico caratteri speciali")
        print("- Compatibilità PostgreSQL garantita")
        print("- Nessun problema con $, ', \\, etc.")
        print("- Sintassi SQL standard")
        print()
        print("COMANDO IMPORTAZIONE:")
        print("1. gunzip snip_backup_YYYYMMDD_HHMMSS.sql.gz")
        print("2. psql -d AGENTE -f snip_backup_YYYYMMDD_HHMMSS.sql")
        print()
        print("STATO: PRONTO PER IMPORTAZIONE SENZA ERRORI")
        return True
    else:
        print("BACKUP HA PROBLEMI")
        if not all_syntax_ok:
            print("- Problemi sintassi SQL")
        if insert_count == 0:
            print("- Nessun INSERT statement")
        return False

if __name__ == "__main__":
    success = test_insert_backup()
    if success:
        print("\nBACKUP PRONTO PER IMPORTAZIONE POSTGRESQL!")
    else:
        print("\nBACKUP NECESSITA CORREZIONI")
