from sqlalchemy import Column, Integer, String, Boolean, Enum, ForeignKey, Table, MetaData, Numeric, DateTime, Date, Text
from sqlalchemy.orm import declarative_base, relationship
import enum
from datetime import datetime, timezone

Base = declarative_base()
metadata = MetaData()

class RepartoEnum(str, enum.Enum):
    OPERATIVO = "OPERATIVO"
    AMMINISTRAZIONE = "AMMINISTRAZIONE"
    SHORTSEA = "SHORTSEA"
    CONTABILITA = "CONTABILITA"  # CORRETTO: rimosso apostrofo

class RuoloEnum(str, enum.Enum):
    ADMIN = "ADMIN"
    SUPER_ADMIN = "SUPER_ADMIN"
    USER = "USER"
    VISITOR = "VISITOR"

class TemaEnum(str, enum.Enum):
    light = "light"
    dark = "dark"
    maritime = "maritime"

class Agente(Base):
    __tablename__ = "AGENTE"

    id_user = Column(Integer, primary_key=True, index=True)
    Nome = Column(String)
    Cognome = Column(String)
    email = Column(String, unique=True, index=True)
    password = Column(String)
    reparto = Column(Enum(RepartoEnum))
    ruolo = Column(Enum(RuoloEnum), default=RuoloEnum.USER)
    visibile = Column(String, default='no')
    tema_preferito = Column(Enum(TemaEnum), default=TemaEnum.maritime)

class PortiGestione(Base):
    __tablename__ = "PORTI_GESTIONE"

    id_porto = Column(Integer, primary_key=True, index=True)
    nome_porto = Column(String, unique=True, index=True)
    codice_porto = Column(String, unique=True)

class CodiciAtlas(Base):
    __tablename__ = "CODICI_ATLAS"

    id = Column(Integer, primary_key=True, index=True)
    codice_porto = Column(String, unique=True, index=True)
    nazione = Column(String, index=True)

class Navi(Base):
    __tablename__ = "NAVI"

    id = Column(Integer, primary_key=True, index=True)
    Nave = Column(String, index=True)
    Codice_Nave = Column(String, index=True)
    Prefisso_viaggio = Column(String, index=True)
    Agemar = Column(Numeric(10, 2), nullable=True)  # Campo valuta in €
    armatore_id = Column(Integer, ForeignKey("ARMATORE.id"), nullable=True)

    # Relazione con la tabella Armatore
    armatore = relationship("Armatore", back_populates="navi")

    def __repr__(self):
        return f"<Nave(id={self.id}, nave='{self.Nave}', codice_nave='{self.Codice_Nave}', prefisso_viaggio='{self.Prefisso_viaggio}', agemar={self.Agemar}, armatore_id={self.armatore_id})>"

class Armatore(Base):
    __tablename__ = "ARMATORE"

    id = Column(Integer, primary_key=True, index=True)
    Nome_Armatore = Column(String, index=True)

    # Relazione inversa con la tabella Navi
    navi = relationship("Navi", back_populates="armatore")

    def __repr__(self):
        return f"<Armatore(id={self.id}, nome_armatore='{self.Nome_Armatore}')>"

class Viaggio(Base):
    __tablename__ = "VIAGGIO"

    id = Column(Integer, primary_key=True, index=True)
    porto_gestione_id = Column(Integer, ForeignKey("PORTI_GESTIONE.id_porto"), nullable=False)
    nave_id = Column(Integer, ForeignKey("NAVI.id"), nullable=False)
    viaggio = Column(String, index=True)  # Auto-popolato dal prefisso_viaggio della nave
    data_arrivo = Column(Date, nullable=False)
    data_partenza = Column(Date, nullable=False)
    eta_originale = Column(Date, nullable=True, index=True)  # ETA originale per calcolare variazioni
    porto_arrivo = Column(String(10), nullable=True, index=True)  # FK verso ATLAS.ID_COD
    porto_destinazione = Column(String(10), nullable=True, index=True)  # FK verso ATLAS.ID_COD
    visibile = Column(String, default="si")  # "si" o "no"
    archiviato = Column(String(2), default="no")  # "si" o "no"

    # Relazioni
    porto_gestione = relationship("PortiGestione")
    nave = relationship("Navi")
    orari = relationship("Orari", back_populates="viaggio", cascade="all, delete-orphan")
    imports = relationship("Import", back_populates="viaggio", cascade="all, delete-orphan")
    exports = relationship("Export", back_populates="viaggio", cascade="all, delete-orphan")

    def __repr__(self):
        return f"<Viaggio(id={self.id}, viaggio='{self.viaggio}', nave_id={self.nave_id})>"

class Orari(Base):
    __tablename__ = "ORARI"

    id = Column(Integer, primary_key=True, index=True)
    viaggio_id = Column(Integer, ForeignKey("VIAGGIO.id"), nullable=False)

    # Orari di Arrivo
    porto_arrivo = Column(String, index=True)  # FK verso ATLAS.PORTI (ID_COD)
    sbe = Column(DateTime, nullable=True)  # DD/MM/YYYY HH:MM
    pilota_arrivo = Column(DateTime, nullable=True)  # DD/MM/YYYY HH:MM
    all_fast = Column(DateTime, nullable=True)  # DD/MM/YYYY HH:MM
    tug_arrivo = Column(Integer, nullable=True)  # Numerico
    draft = Column(Numeric(10, 2), nullable=True)  # Decimale
    soc = Column(DateTime, nullable=True)  # DD/MM/YYYY HH:MM

    # Orari di Partenza
    porto_di_destinazione = Column(String, index=True)  # FK verso ATLAS.PORTI (ID_COD)
    pilota_partenza = Column(DateTime, nullable=True)  # DD/MM/YYYY HH:MM
    tug_partenza = Column(Integer, nullable=True)  # Numerico
    foc = Column(DateTime, nullable=True)  # DD/MM/YYYY HH:MM

    # Nuovi campi numerici
    fo = Column(Numeric(10, 2), nullable=True)  # Numerico decimale
    do = Column(Numeric(10, 2), nullable=True)  # Numerico decimale
    lo = Column(Numeric(10, 2), nullable=True)  # Numerico decimale

    # Relazione
    viaggio = relationship("Viaggio", back_populates="orari")

    def __repr__(self):
        return f"<Orari(id={self.id}, viaggio_id={self.viaggio_id}, porto_arrivo='{self.porto_arrivo}')>"

class Import(Base):
    __tablename__ = "IMPORT"

    id = Column(Integer, primary_key=True, index=True)
    viaggio_id = Column(Integer, ForeignKey("VIAGGIO.id"), nullable=False)
    pol = Column(String, nullable=False)                    # Nome porto di carico (da LOADING → ATLAS.PORTI)
    pod = Column(String, nullable=False)                    # Nome porto di scarico (da DISCH → ATLAS.PORTI)
    qt = Column(Numeric(10, 2), nullable=False)            # Quantità (campo numerico per raggruppamento)
    type = Column(String, nullable=False)                   # Tipo processato (C/VAN FULL/EMPTY, NCAR - Make Model)
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    updated_at = Column(DateTime, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))

    # Relazione con Viaggio
    viaggio = relationship("Viaggio", back_populates="imports")

    def __repr__(self):
        return f"<Import(id={self.id}, viaggio_id={self.viaggio_id}, pol='{self.pol}', pod='{self.pod}', qt={self.qt}, type='{self.type}')>"

class Export(Base):
    __tablename__ = "EXPORT"

    id = Column(Integer, primary_key=True, index=True)
    viaggio_id = Column(Integer, ForeignKey("VIAGGIO.id"), nullable=False)
    pol = Column(String, nullable=False)                    # Nome porto di carico (da LOADING → ATLAS.PORTI)
    pod = Column(String, nullable=False)                    # Nome porto di scarico (da DISCH → ATLAS.PORTI)
    qt = Column(Numeric(10, 2), nullable=False)            # Quantità (campo numerico per raggruppamento)
    type = Column(String, nullable=False)                   # Tipo processato (C/VAN FULL/EMPTY, NCAR - Make Model)
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    updated_at = Column(DateTime, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))

    # Relazione con Viaggio
    viaggio = relationship("Viaggio", back_populates="exports")

    def __repr__(self):
        return f"<Export(id={self.id}, viaggio_id={self.viaggio_id}, pol='{self.pol}', pod='{self.pod}', qt={self.qt}, type='{self.type}')>"

# ===== MODELLI SISTEMA AMMINISTRAZIONE =====

class SystemConfig(Base):
    """Configurazioni di sistema"""
    __tablename__ = "SYSTEM_CONFIG"

    id = Column(Integer, primary_key=True, index=True)
    config_key = Column(String(100), unique=True, nullable=False, index=True)
    config_value = Column(Text)
    description = Column(Text)
    config_type = Column(String(50), default='string')  # string, integer, boolean, json
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    updated_at = Column(DateTime, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))

    def __repr__(self):
        return f"<SystemConfig(key='{self.config_key}', value='{self.config_value}', type='{self.config_type}')>"

class AuditLog(Base):
    """Log delle attività per audit trail"""
    __tablename__ = "AUDIT_LOG"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("AGENTE.id_user"), nullable=True)
    action = Column(String(100), nullable=False, index=True)
    table_name = Column(String(100), index=True)
    record_id = Column(Integer)
    old_values = Column(Text)  # JSON string
    new_values = Column(Text)  # JSON string
    ip_address = Column(String(45))
    user_agent = Column(Text)
    timestamp = Column(DateTime, default=lambda: datetime.now(timezone.utc), index=True)

    # Relazione
    user = relationship("Agente", foreign_keys=[user_id])

    def __repr__(self):
        return f"<AuditLog(id={self.id}, user_id={self.user_id}, action='{self.action}', table='{self.table_name}')>"

class UserSession(Base):
    """Sessioni utente attive"""
    __tablename__ = "USER_SESSIONS"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("AGENTE.id_user"), nullable=False)
    session_token = Column(String(1000), unique=True, nullable=False, index=True)
    ip_address = Column(String(45))
    user_agent = Column(Text)
    is_active = Column(Boolean, default=True, index=True)
    last_activity = Column(DateTime, default=lambda: datetime.now(timezone.utc), index=True)
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    expires_at = Column(DateTime, index=True)

    # Relazione
    user = relationship("Agente", foreign_keys=[user_id])

    def __repr__(self):
        return f"<UserSession(id={self.id}, user_id={self.user_id}, active={self.is_active})>"

class NotificationTypeEnum(str, enum.Enum):
    INFO = "INFO"
    WARNING = "WARNING"
    SUCCESS = "SUCCESS"
    ERROR = "ERROR"
    URGENT = "URGENT"

class DepartmentNotification(Base):
    """Notifiche per reparto"""
    __tablename__ = "DEPARTMENT_NOTIFICATIONS"

    id = Column(Integer, primary_key=True, index=True)
    title = Column(String(255), nullable=False)
    message = Column(Text, nullable=False)
    notification_type = Column(Enum(NotificationTypeEnum), default=NotificationTypeEnum.INFO)
    target_reparto = Column(Enum(RepartoEnum), nullable=False, index=True)
    created_by = Column(Integer, ForeignKey("AGENTE.id_user"), nullable=False)
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc), index=True)
    expires_at = Column(DateTime, nullable=True, index=True)
    is_active = Column(Boolean, default=True, index=True)
    priority = Column(Integer, default=1)  # 1=bassa, 2=media, 3=alta, 4=urgente
    send_email = Column(Boolean, default=False)

    # Relazioni
    creator = relationship("Agente", foreign_keys=[created_by])

    def __repr__(self):
        return f"<DepartmentNotification(id={self.id}, reparto={self.target_reparto}, title='{self.title}')>"

class UserNotificationRead(Base):
    """Traccia notifiche lette dagli utenti"""
    __tablename__ = "USER_NOTIFICATION_READ"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("AGENTE.id_user"), nullable=False)
    notification_id = Column(Integer, ForeignKey("DEPARTMENT_NOTIFICATIONS.id"), nullable=False)
    read_at = Column(DateTime, default=lambda: datetime.now(timezone.utc))

    # Relazioni
    user = relationship("Agente", foreign_keys=[user_id])
    notification = relationship("DepartmentNotification", foreign_keys=[notification_id])

    def __repr__(self):
        return f"<UserNotificationRead(user_id={self.user_id}, notification_id={self.notification_id})>"

class SystemStats(Base):
    """Statistiche di sistema"""
    __tablename__ = "SYSTEM_STATS"

    id = Column(Integer, primary_key=True, index=True)
    stat_date = Column(Date, nullable=False, index=True)
    total_users = Column(Integer, default=0)
    active_users = Column(Integer, default=0)
    total_viaggi = Column(Integer, default=0)
    sof_generated = Column(Integer, default=0)
    login_count = Column(Integer, default=0)
    error_count = Column(Integer, default=0)
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc))

    def __repr__(self):
        return f"<SystemStats(date={self.stat_date}, users={self.total_users}, viaggi={self.total_viaggi})>"