#!/usr/bin/env python3
"""
Test sequenze corrette nel backup
"""

import gzip
from pathlib import Path

def test_sequences_fixed():
    print("TEST SEQUENZE CORRETTE")
    print("=" * 30)
    
    # Trova ultimo backup
    backup_dir = Path("backups")
    backup_files = list(backup_dir.glob("snip_backup_*.sql.gz"))
    
    if not backup_files:
        print("Nessun backup trovato!")
        return False
    
    latest_backup = max(backup_files, key=lambda f: f.stat().st_mtime)
    print(f"File: {latest_backup.name}")
    
    with gzip.open(latest_backup, 'rt', encoding='utf-8') as f:
        content = f.read()
    
    lines = content.split('\n')
    print(f"Righe totali: {len(lines)}")
    
    # Verifica problemi doppi apici
    print(f"\nVERIFICA DOPPI APICI:")
    print("-" * 25)
    
    double_quotes_empty = content.count('""')
    double_quotes_problem = content.count('public.""')
    
    print(f"Doppi apici vuoti: {double_quotes_empty}")
    print(f"public.\"\" problematici: {double_quotes_problem}")
    
    if double_quotes_problem == 0:
        print("OK - Nessun doppio apice problematico")
    else:
        print("ERRORE - Doppi apici problematici trovati")
    
    # Verifica sequenze CREATE
    print(f"\nSEQUENZE CREATE:")
    print("-" * 20)
    
    create_sequences = []
    for i, line in enumerate(lines, 1):
        if 'CREATE SEQUENCE public.' in line:
            create_sequences.append((i, line.strip()))
    
    print(f"Sequenze CREATE trovate: {len(create_sequences)}")
    
    for line_num, line_content in create_sequences[:5]:  # Prime 5
        print(f"  Riga {line_num}: {line_content}")
    
    # Verifica setval
    print(f"\nSEQUENZE SETVAL:")
    print("-" * 18)
    
    setval_sequences = []
    for i, line in enumerate(lines, 1):
        if 'setval(' in line:
            setval_sequences.append((i, line.strip()))
    
    print(f"setval() trovate: {len(setval_sequences)}")
    
    for line_num, line_content in setval_sequences[:5]:  # Prime 5
        print(f"  Riga {line_num}: {line_content}")
    
    # Verifica valori sequenze
    print(f"\nVALORI SEQUENZE:")
    print("-" * 18)
    
    sequence_values = {
        "AGENTE_id_user_seq": 0,
        "ARMATORE_id_seq": 0,
        "ATLAS_id_seq": 0,
        "NAVI_id_seq": 0,
        "SYSTEM_CONFIG_id_seq": 0
    }
    
    for seq_name in sequence_values.keys():
        # Cerca setval per questa sequenza
        for line in lines:
            if f"setval('public.\"{seq_name}\"" in line:
                # Estrai il valore
                try:
                    start = line.find(', ') + 2
                    end = line.find(',', start)
                    if end == -1:
                        end = line.find(')', start)
                    value = int(line[start:end])
                    sequence_values[seq_name] = value
                except:
                    sequence_values[seq_name] = -1
                break
    
    for seq_name, value in sequence_values.items():
        if value > 0:
            print(f"  {seq_name}: {value}")
        elif value == 0:
            print(f"  {seq_name}: NON TROVATA")
        else:
            print(f"  {seq_name}: ERRORE PARSING")
    
    # Test sintassi generale
    print(f"\nTEST SINTASSI:")
    print("-" * 15)
    
    syntax_tests = {
        "Header PostgreSQL": "-- PostgreSQL database dump" in content,
        "CREATE TYPE": content.count("CREATE TYPE public.") == 5,
        "CREATE SEQUENCE": len(create_sequences) >= 18,
        "setval corretti": len(setval_sequences) >= 18,
        "INSERT statements": content.count("INSERT INTO public.") >= 1800,
        "Nessun doppio apice vuoto": double_quotes_problem == 0,
        "Footer completo": "PostgreSQL database dump complete" in content
    }
    
    all_syntax_ok = True
    for test_name, result in syntax_tests.items():
        status = "OK" if result else "ERRORE"
        print(f"  {test_name}: {status}")
        if not result:
            all_syntax_ok = False
    
    # Risultato finale
    print(f"\n" + "=" * 30)
    print("RISULTATO")
    print("=" * 30)
    
    if all_syntax_ok and double_quotes_problem == 0:
        print("BACKUP PERFETTO!")
        print()
        print("RISOLTO:")
        print("- Errore doppi apici vuoti")
        print("- Sequenze con valori reali")
        print("- Sintassi PostgreSQL corretta")
        print()
        print("PRONTO PER IMPORTAZIONE:")
        print("gunzip + psql senza errori")
        return True
    else:
        print("BACKUP HA PROBLEMI")
        if double_quotes_problem > 0:
            print("- Doppi apici problematici")
        if not all_syntax_ok:
            print("- Problemi sintassi")
        return False

if __name__ == "__main__":
    success = test_sequences_fixed()
    if success:
        print("\nSUCCESS: SEQUENZE CORRETTE!")
    else:
        print("\nERROR: SEQUENZE PROBLEMATICHE")
