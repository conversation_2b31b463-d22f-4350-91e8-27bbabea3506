{% extends "base_operativo.html" %}

{% block title %}Dashboard Operativo - M.A.P.{% endblock %}

{% block content %}
        <h1 class="mb-4">Dashboard Reparto Operativo</h1>

        <!-- Stat Cards Row -->
        <div class="row mt-4">
            <div class="col-md-3">
                <div class="card stat-card primary">
                    <div class="card-body text-center">
                        <i class="fas fa-ship stat-icon"></i>
                        <div class="stat-number counter" data-target="{{ navi_totali }}">0</div>
                        <div class="stat-label">Navi Totali</div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stat-card success">
                    <div class="card-body text-center">
                        <i class="fas fa-calendar-alt stat-icon"></i>
                        <div class="stat-number counter" data-target="{{ navi_schedulate }}">0</div>
                        <div class="stat-label">Navi Schedulate</div>
                        <small class="text-muted d-block mt-1">{{ periodo_schedulazione }}</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stat-card warning">
                    <div class="card-body text-center">
                        <i class="fas fa-file-alt stat-icon"></i>
                        <div class="stat-number counter" data-target="{{ sof_da_completare }}">0</div>
                        <div class="stat-label">SOF da Completare</div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stat-card info">
                    <div class="card-body text-center">
                        <i class="fas fa-users stat-icon"></i>
                        <div class="stat-number counter" data-target="{{ utenti_online }}">0</div>
                        <div class="stat-label">Utenti Online</div>
                        <small class="text-muted d-block mt-1">Attivi negli ultimi 30 min</small>
                    </div>
                </div>
            </div>
        </div>
        <!-- Dettaglio Navi per Porto di Gestione -->
        {% if porti_dettaglio %}
        <div class="row mt-4">
            <div class="col-12">
                <div class="card porto-section">
                    <div class="card-header bg-info text-white">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h5 class="mb-0">
                                    <i class="fas fa-anchor me-2"></i>Navi Schedulate per Porto di Gestione
                                </h5>
                                <small class="text-light">Dettaglio programmazione per ogni porto</small>
                            </div>
                            <div>
                                <button type="button" class="btn btn-light btn-sm" data-bs-toggle="modal" data-bs-target="#reportNaviModal">
                                    <i class="fas fa-file-alt me-1"></i>Genera Report
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            {% for porto in porti_dettaglio %}
                            <div class="col-md-6 col-lg-4 mb-4">
                                <div class="porto-detail-card" data-porto="{{ porto.nome_porto.lower().replace(' ', '-') }}">
                                    <div class="porto-header">
                                        <i class="fas fa-map-marker-alt"></i>
                                        <strong>{{ porto.nome_porto }}</strong>
                                    </div>
                                    <div class="porto-period">
                                        {{ porto.periodo }}
                                    </div>
                                    <div class="porto-ships">
                                        <span class="badge">
                                            <i class="fas fa-ship"></i>
                                            {{ porto.navi_count }}
                                            {% if porto.navi_count == 1 %}nave prevista{% else %}navi previste{% endif %}
                                        </span>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}
        <!-- Modal per Report Navi -->
        <div class="modal fade" id="reportNaviModal" tabindex="-1" aria-labelledby="reportNaviModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header bg-info text-white">
                        <h5 class="modal-title" id="reportNaviModalLabel">
                            <i class="fas fa-file-alt me-2"></i>Report Navi in Arrivo
                        </h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <form id="reportNaviForm">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="reportType" class="form-label">
                                            <i class="fas fa-filter me-1"></i>Tipo di Report
                                        </label>
                                        <select class="form-select" id="reportType" name="reportType" required>
                                            <option value="">Seleziona tipo di report...</option>
                                            <option value="tutti">Tutte le Navi (Tutti i Porti)</option>
                                            <option value="porto">Navi per Porto Specifico</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="portoSelect" class="form-label">
                                            <i class="fas fa-map-marker-alt me-1"></i>Porto di Gestione
                                        </label>
                                        <select class="form-select" id="portoSelect" name="portoSelect" disabled>
                                            <option value="">Seleziona porto...</option>
                                            {% for porto in porti_dettaglio %}
                                            <option value="{{ porto.nome_porto }}">{{ porto.nome_porto }}</option>
                                            {% endfor %}
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="dataInizio" class="form-label">
                                            <i class="fas fa-calendar-alt me-1"></i>Data Inizio Periodo
                                        </label>
                                        <input type="date" class="form-control" id="dataInizio" name="dataInizio" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="dataFine" class="form-label">
                                            <i class="fas fa-calendar-alt me-1"></i>Data Fine Periodo
                                        </label>
                                        <input type="date" class="form-control" id="dataFine" name="dataFine" required>
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="formatoReport" class="form-label">
                                    <i class="fas fa-file-download me-1"></i>Formato Report
                                </label>
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="formatoReport" id="formatoPDF" value="pdf" checked>
                                            <label class="form-check-label" for="formatoPDF">
                                                <i class="fas fa-file-pdf text-danger me-1"></i>PDF
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="formatoReport" id="formatoExcel" value="excel">
                                            <label class="form-check-label" for="formatoExcel">
                                                <i class="fas fa-file-excel text-success me-1"></i>Excel
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="formatoReport" id="formatoCSV" value="csv">
                                            <label class="form-check-label" for="formatoCSV">
                                                <i class="fas fa-file-csv text-primary me-1"></i>CSV
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                <strong>Informazioni Report:</strong><br>
                                Il report includerà: Nome nave, Porto di gestione, Data arrivo, Porto di partenza, Porto di destinazione, Stato viaggio.
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                            <i class="fas fa-times me-1"></i>Annulla
                        </button>
                        <button type="button" class="btn btn-info" id="generaReportBtn">
                            <i class="fas fa-download me-1"></i>Genera Report
                        </button>
                    </div>
                </div>
            </div>
        </div>





{% endblock %}

{% block extra_css %}
<!-- CSS Card Dashboard Professionali -->
<link rel="stylesheet" href="{{ url_for('static', path='css/dashboard-cards.css') }}">
<style>
        /* Stile per card Utenti Online */
        .stat-card.info {
            border-left: 5px solid #17a2b8;
            background: linear-gradient(135deg, rgba(23, 162, 184, 0.1) 0%, rgba(23, 162, 184, 0.05) 100%);
        }

        .stat-card.info .stat-icon {
            color: #17a2b8;
        }
/* ===== CARD PORTO MODERNE CON TEMI ===== */
.porto-detail-card {
    /* Base styling - sarà sovrascritto dai temi */
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 25px;
    height: 100%;
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    position: relative;
    overflow: hidden;
}

/* Tema Marittimo */
body.theme-maritime .porto-detail-card,
body:not([class*="theme-"]) .porto-detail-card {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 249, 250, 0.9) 100%);
    border: 1px solid rgba(30, 60, 114, 0.3);
    box-shadow:
        0 8px 25px rgba(30, 60, 114, 0.15),
        0 4px 15px rgba(30, 60, 114, 0.1);
}

/* Tema Scuro */
body.theme-dark .porto-detail-card {
    background: linear-gradient(135deg, rgba(52, 73, 94, 0.95) 0%, rgba(44, 62, 80, 0.9) 100%);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow:
        0 8px 25px rgba(0, 0, 0, 0.3),
        0 4px 15px rgba(0, 0, 0, 0.2);
}

/* Tema Chiaro */
body.theme-light .porto-detail-card {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.98) 0%, rgba(248, 249, 250, 0.95) 100%);
    border: 1px solid rgba(0, 0, 0, 0.1);
    box-shadow:
        0 8px 25px rgba(0, 0, 0, 0.08),
        0 4px 15px rgba(0, 0, 0, 0.05);
}

.porto-detail-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #667eea, #764ba2, #4facfe);
    z-index: 1;
}

.porto-detail-card::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.6s ease;
    z-index: 2;
}

.porto-detail-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow:
        0 20px 60px rgba(0, 0, 0, 0.15),
        0 8px 30px rgba(102, 126, 234, 0.2);
    border-color: rgba(102, 126, 234, 0.3);
}

.porto-detail-card:hover::after {
    left: 100%;
}

.porto-header {
    font-size: 1.3rem;
    margin-bottom: 12px;
    font-weight: 700;
    display: flex;
    align-items: center;
    gap: 10px;
    position: relative;
    z-index: 3;
}

/* Colori testo porto header per temi */
body.theme-maritime .porto-header,
body:not([class*="theme-"]) .porto-header {
    color: #2c3e50;
}

body.theme-dark .porto-header {
    color: #ecf0f1;
}

body.theme-light .porto-header {
    color: #495057;
}

.porto-header i {
    color: white;
    padding: 8px;
    border-radius: 50%;
    font-size: 1rem;
    transition: all 0.3s ease;
}

/* Icone porto header per temi */
body.theme-maritime .porto-header i,
body:not([class*="theme-"]) .porto-header i {
    background: linear-gradient(135deg, #1e3c72, #2a5298);
    box-shadow: 0 4px 15px rgba(30, 60, 114, 0.3);
}

body.theme-dark .porto-header i {
    background: linear-gradient(135deg, #34495e, #2c3e50);
    box-shadow: 0 4px 15px rgba(52, 73, 94, 0.4);
}

body.theme-light .porto-header i {
    background: linear-gradient(135deg, #6c757d, #495057);
    box-shadow: 0 4px 15px rgba(108, 117, 125, 0.3);
}

.porto-detail-card:hover .porto-header i {
    transform: scale(1.1) rotate(5deg);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

.porto-period {
    margin-bottom: 15px;
    font-size: 0.95rem;
    color: #6c757d;
    font-weight: 500;
    position: relative;
    z-index: 3;
    background: rgba(108, 117, 125, 0.1);
    padding: 8px 12px;
    border-radius: 12px;
    text-align: center;
}

.porto-ships {
    display: flex;
    justify-content: center;
    position: relative;
    z-index: 3;
}

.porto-ships .badge {
    font-size: 1rem;
    padding: 12px 20px;
    border-radius: 25px;
    background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
    border: 2px solid rgba(255, 255, 255, 0.3);
    color: white;
    font-weight: 600;
    box-shadow: 0 6px 20px rgba(17, 153, 142, 0.3);
    transition: all 0.3s ease;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.porto-ships .badge i {
    margin-right: 8px;
    background: rgba(255, 255, 255, 0.2);
    padding: 4px;
    border-radius: 50%;
    font-size: 0.9rem;
}

.porto-detail-card:hover .porto-ships .badge {
    transform: scale(1.05);
    box-shadow: 0 8px 25px rgba(17, 153, 142, 0.4);
}

/* Varianti colore per diversi porti */
.porto-detail-card[data-porto="salerno"] .porto-header i {
    background: linear-gradient(135deg, #667eea, #764ba2);
}

.porto-detail-card[data-porto="gioia-tauro"] .porto-header i {
    background: linear-gradient(135deg, #4facfe, #00f2fe);
}

.porto-detail-card[data-porto="napoli"] .porto-header i {
    background: linear-gradient(135deg, #f093fb, #f5576c);
}

.porto-detail-card[data-porto="palermo"] .porto-header i {
    background: linear-gradient(135deg, #11998e, #38ef7d);
}

/* Animazione di entrata per le card porto */
.porto-detail-card {
    opacity: 0;
    transform: translateY(30px);
    animation: portoCardSlideIn 0.6s ease forwards;
}

.porto-detail-card:nth-child(1) { animation-delay: 0.1s; }
.porto-detail-card:nth-child(2) { animation-delay: 0.2s; }
.porto-detail-card:nth-child(3) { animation-delay: 0.3s; }
.porto-detail-card:nth-child(4) { animation-delay: 0.4s; }
.porto-detail-card:nth-child(5) { animation-delay: 0.5s; }
.porto-detail-card:nth-child(6) { animation-delay: 0.6s; }

@keyframes portoCardSlideIn {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* ===== HEADER SEZIONE PORTO MODERNO ===== */
.card-header.bg-info {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    border-radius: 20px 20px 0 0 !important;
    padding: 25px !important;
    border: none !important;
    position: relative !important;
    overflow: hidden !important;
}

.card-header.bg-info::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.8s ease;
}

.card-header.bg-info:hover::before {
    left: 100%;
}

.card-header.bg-info h5 {
    font-size: 1.4rem !important;
    font-weight: 700 !important;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3) !important;
    margin-bottom: 5px !important;
}

.card-header.bg-info .text-light {
    color: rgba(255, 255, 255, 0.9) !important;
    font-weight: 500 !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2) !important;
}

.card-header.bg-info .btn-light {
    background: rgba(255, 255, 255, 0.2) !important;
    border: 2px solid rgba(255, 255, 255, 0.3) !important;
    color: white !important;
    font-weight: 600 !important;
    padding: 10px 20px !important;
    border-radius: 25px !important;
    backdrop-filter: blur(10px) !important;
    transition: all 0.3s ease !important;
    text-shadow: none !important;
}

.card-header.bg-info .btn-light:hover {
    background: rgba(255, 255, 255, 0.3) !important;
    border-color: rgba(255, 255, 255, 0.5) !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2) !important;
}

.card-header.bg-info i {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3) !important;
}

/* Card body per sezione porto */
.porto-section .card-body {
    padding: 30px !important;
    background: rgba(255, 255, 255, 0.95) !important;
    backdrop-filter: blur(10px) !important;
    border-radius: 0 0 20px 20px !important;
}

/* Card container per sezione porto */
.porto-section .card {
    border-radius: 20px !important;
    border: none !important;
    box-shadow:
        0 15px 35px rgba(0, 0, 0, 0.1),
        0 5px 15px rgba(0, 0, 0, 0.05) !important;
    overflow: hidden !important;
}

/* Effetti aggiuntivi per card moderne */
@keyframes ripple-effect {
    to {
        transform: scale(4);
        opacity: 0;
    }
}

.stat-card {
    cursor: pointer;
    overflow: hidden;
}

.stat-card .stat-number {
    transition: all 0.3s ease;
}

.stat-card .stat-icon {
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}
</style>
{% endblock %}

{% block extra_js %}
<script src="/static/js/alerts.js"></script>
<script>
// Animazione counter per le statistiche con effetti moderni
document.addEventListener('DOMContentLoaded', function() {
    console.log('🎨 Inizializzazione dashboard moderna...');

    // Trova tutti gli elementi con classe counter
    const counters = document.querySelectorAll('.counter');

    // Aggiungi effetti alle card
    initializeCardEffects();

    // Inizializza effetti per le card porto
    initializePortoCardEffects();

    counters.forEach(counter => {
        const target = parseInt(counter.getAttribute('data-target')) || 0;
        console.log(`Counter trovato con target: ${target}`);

        // Anima il counter con effetti moderni
        animateCounterModern(counter, target);
    });
});

// Inizializza effetti moderni per le card
function initializeCardEffects() {
    const cards = document.querySelectorAll('.stat-card');

    cards.forEach((card, index) => {
        // Animazione di entrata scaglionata
        card.style.opacity = '0';
        card.style.transform = 'translateY(30px)';

        setTimeout(() => {
            card.style.transition = 'all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94)';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 150);

        // Effetto hover avanzato
        card.addEventListener('mouseenter', function() {
            this.classList.add('glow');
            const icon = this.querySelector('.stat-icon');
            if (icon) {
                icon.style.transform = 'scale(1.1) rotate(5deg)';
            }
        });

        card.addEventListener('mouseleave', function() {
            this.classList.remove('glow');
            const icon = this.querySelector('.stat-icon');
            if (icon) {
                icon.style.transform = 'scale(1) rotate(0deg)';
            }
        });

        // Effetto click con ripple
        card.addEventListener('click', function(e) {
            createRippleEffect(e, this);
        });
    });
}

// Crea effetto ripple al click
function createRippleEffect(event, element) {
    const ripple = document.createElement('span');
    const rect = element.getBoundingClientRect();
    const size = Math.max(rect.width, rect.height);
    const x = event.clientX - rect.left - size / 2;
    const y = event.clientY - rect.top - size / 2;

    ripple.style.width = ripple.style.height = size + 'px';
    ripple.style.left = x + 'px';
    ripple.style.top = y + 'px';
    ripple.style.position = 'absolute';
    ripple.style.borderRadius = '50%';
    ripple.style.background = 'rgba(255, 255, 255, 0.6)';
    ripple.style.transform = 'scale(0)';
    ripple.style.animation = 'ripple-effect 0.6s linear';
    ripple.style.pointerEvents = 'none';
    ripple.style.zIndex = '10';

    element.style.position = 'relative';
    element.appendChild(ripple);

    setTimeout(() => {
        ripple.remove();
    }, 600);
}

// Inizializza effetti per le card porto
function initializePortoCardEffects() {
    const portoCards = document.querySelectorAll('.porto-detail-card');

    portoCards.forEach((card, index) => {
        // Effetto hover avanzato
        card.addEventListener('mouseenter', function() {
            this.style.zIndex = '10';

            // Effetto sulle icone
            const icon = this.querySelector('.porto-header i');
            if (icon) {
                icon.style.transform = 'scale(1.1) rotate(5deg)';
            }

            // Effetto sul badge
            const badge = this.querySelector('.porto-ships .badge');
            if (badge) {
                badge.style.transform = 'scale(1.05)';
            }
        });

        card.addEventListener('mouseleave', function() {
            this.style.zIndex = '1';

            // Reset icone
            const icon = this.querySelector('.porto-header i');
            if (icon) {
                icon.style.transform = 'scale(1) rotate(0deg)';
            }

            // Reset badge
            const badge = this.querySelector('.porto-ships .badge');
            if (badge) {
                badge.style.transform = 'scale(1)';
            }
        });

        // Effetto click con ripple
        card.addEventListener('click', function(e) {
            createPortoRippleEffect(e, this);

            // Animazione di feedback
            this.style.transform = 'translateY(-8px) scale(0.98)';
            setTimeout(() => {
                this.style.transform = 'translateY(-8px) scale(1.02)';
            }, 150);
        });

        // Animazione di entrata con delay
        setTimeout(() => {
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 100);
    });

    // Effetto shimmer per l'header della sezione
    const portoHeader = document.querySelector('.porto-section .card-header.bg-info');
    if (portoHeader) {
        portoHeader.addEventListener('mouseenter', function() {
            this.style.background = 'linear-gradient(135deg, #764ba2 0%, #667eea 100%)';
        });

        portoHeader.addEventListener('mouseleave', function() {
            this.style.background = 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';
        });
    }
}

// Crea effetto ripple specifico per le card porto
function createPortoRippleEffect(event, element) {
    const ripple = document.createElement('span');
    const rect = element.getBoundingClientRect();
    const size = Math.max(rect.width, rect.height);
    const x = event.clientX - rect.left - size / 2;
    const y = event.clientY - rect.top - size / 2;

    ripple.style.width = ripple.style.height = size + 'px';
    ripple.style.left = x + 'px';
    ripple.style.top = y + 'px';
    ripple.style.position = 'absolute';
    ripple.style.borderRadius = '50%';
    ripple.style.background = 'rgba(102, 126, 234, 0.4)';
    ripple.style.transform = 'scale(0)';
    ripple.style.animation = 'ripple-effect 0.6s linear';
    ripple.style.pointerEvents = 'none';
    ripple.style.zIndex = '5';

    element.style.position = 'relative';
    element.appendChild(ripple);

    setTimeout(() => {
        ripple.remove();
    }, 600);
}

// Animazione counter moderna con effetti
function animateCounterModern(element, target) {
    const duration = 2000; // 2 secondi per effetto più drammatico
    const start = 0;
    const startTime = performance.now();

    // Aggiungi classe per pulse animation
    element.classList.add('animate');

    function updateCounter(currentTime) {
        const elapsed = currentTime - startTime;
        const progress = Math.min(elapsed / duration, 1);

        // Easing function più fluida (ease out cubic)
        const easeOutCubic = 1 - Math.pow(1 - progress, 3);
        const currentValue = Math.floor(start + (target - start) * easeOutCubic);

        element.textContent = currentValue;

        // Effetto di "bounce" quando raggiunge il target
        if (progress >= 0.9 && progress < 1) {
            element.style.transform = 'scale(1.1)';
        } else if (progress === 1) {
            element.style.transform = 'scale(1)';
            element.classList.remove('animate');

            // Effetto finale di completamento
            element.style.textShadow = '0 0 20px rgba(255, 255, 255, 0.8)';
            setTimeout(() => {
                element.style.textShadow = '0 2px 4px rgba(0, 0, 0, 0.3)';
            }, 500);
        }

        if (progress < 1) {
            requestAnimationFrame(updateCounter);
        } else {
            element.textContent = target; // Assicura il valore finale
        }
    }

    // Ritarda l'inizio dell'animazione per effetto più drammatico
    setTimeout(() => {
        requestAnimationFrame(updateCounter);
    }, 300);
}

// Funzione per animare counter esistente (backward compatibility)
function animateCounter(element, target) {
    animateCounterModern(element, target);
}

// ===== AGGIORNAMENTO UTENTI ONLINE =====
function updateOnlineUsers() {
    fetch('/api/users/online', {
        method: 'GET',
        credentials: 'include',
        headers: {
            'Content-Type': 'application/json'
        }
    })
    .then(response => {
        if (response.ok) {
            return response.json();
        }
        throw new Error('Errore nel recupero utenti online');
    })
    .then(data => {
        if (data.success) {
            // Trova l'elemento del counter utenti online
            const onlineUsersCounter = document.querySelector('.stat-card.info .counter');
            if (onlineUsersCounter) {
                // Aggiorna il valore con animazione
                const newValue = data.utenti_online;
                const currentValue = parseInt(onlineUsersCounter.textContent) || 0;

                if (newValue !== currentValue) {
                    animateCounter(onlineUsersCounter, newValue);
                }
            }
        }
    })
    .catch(error => {
        console.log('Errore aggiornamento utenti online:', error);
    });
}

// Avvia aggiornamento automatico utenti online
document.addEventListener('DOMContentLoaded', function() {
    // Aggiorna subito
    setTimeout(updateOnlineUsers, 2000); // Aspetta 2 secondi per il caricamento iniziale

    // Poi aggiorna ogni 30 secondi
    setInterval(updateOnlineUsers, 30000);
});

// Gestione modal report navi
document.addEventListener('DOMContentLoaded', function() {
    // Variabile per prevenire richieste multiple
    let isGeneratingReport = false;

    const reportType = document.getElementById('reportType');
    const portoSelect = document.getElementById('portoSelect');
    const generaReportBtn = document.getElementById('generaReportBtn');
    const dataInizio = document.getElementById('dataInizio');
    const dataFine = document.getElementById('dataFine');

    // Gestione abilitazione/disabilitazione select porto
    if (reportType) {
        reportType.addEventListener('change', function() {
            if (this.value === 'porto') {
                portoSelect.disabled = false;
                portoSelect.required = true;
            } else {
                portoSelect.disabled = true;
                portoSelect.required = false;
                portoSelect.value = '';
            }
        });
    }

    // Imposta date di default (periodo attuale dei viaggi)
    const oggi = new Date();
    const dataOggi = oggi.toISOString().split('T')[0];
    const dataFutura = new Date(oggi.getTime() + (30 * 24 * 60 * 60 * 1000)); // +30 giorni
    const dataFuturaStr = dataFutura.toISOString().split('T')[0];

    if (dataInizio) dataInizio.value = dataOggi;
    if (dataFine) dataFine.value = dataFuturaStr;

    // Gestione generazione report
    if (generaReportBtn) {
        generaReportBtn.addEventListener('click', function() {
            // Previeni richieste multiple
            if (isGeneratingReport) {
                console.log('Report già in generazione, richiesta ignorata');
                return;
            }

            const form = document.getElementById('reportNaviForm');
            const formData = new FormData(form);

            // Validazione form
            if (!form.checkValidity()) {
                form.reportValidity();
                return;
            }

            // Validazione date
            const dataInizioVal = new Date(formData.get('dataInizio'));
            const dataFineVal = new Date(formData.get('dataFine'));

            if (dataInizioVal > dataFineVal) {
                alert('La data di inizio deve essere precedente alla data di fine');
                return;
            }

            // Imposta flag e disabilita pulsante durante generazione
            isGeneratingReport = true;
            generaReportBtn.disabled = true;
            generaReportBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Generando...';

            // Prepara parametri per la richiesta
            const params = new URLSearchParams();
            params.append('tipo', formData.get('reportType'));
            params.append('data_inizio', formData.get('dataInizio'));
            params.append('data_fine', formData.get('dataFine'));
            params.append('formato', formData.get('formatoReport'));

            if (formData.get('reportType') === 'porto' && formData.get('portoSelect')) {
                params.append('porto', formData.get('portoSelect'));
            }

            console.log('Generazione report con parametri:', params.toString());

            // Effettua richiesta per generare report con timeout esteso
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), 60000); // 60 secondi timeout

            fetch('/api/report/navi-arrivo?' + params.toString(), {
                method: 'GET',
                signal: controller.signal,
                headers: {
                    'Accept': 'application/pdf, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, text/csv'
                }
            })
            .then(response => {
                clearTimeout(timeoutId); // Cancella il timeout se la risposta arriva in tempo

                console.log('Response status:', response.status);
                console.log('Response headers:', [...response.headers.entries()]);

                if (response.ok) {
                    // Se la risposta è ok, scarica il file
                    const contentDisposition = response.headers.get('Content-Disposition');
                    let filename = 'report_navi_arrivo.' + formData.get('formatoReport');

                    if (contentDisposition) {
                        const filenameMatch = contentDisposition.match(/filename="(.+)"/);
                        if (filenameMatch) {
                            filename = filenameMatch[1];
                        }
                    }

                    return response.blob().then(blob => {
                        console.log('Blob size:', blob.size, 'bytes');
                        console.log('Blob type:', blob.type);

                        // Crea e avvia il download
                        const url = window.URL.createObjectURL(blob);
                        const a = document.createElement('a');
                        a.href = url;
                        a.download = filename;
                        document.body.appendChild(a);
                        a.click();
                        window.URL.revokeObjectURL(url);
                        document.body.removeChild(a);

                        // Chiudi modal
                        const modal = bootstrap.Modal.getInstance(document.getElementById('reportNaviModal'));
                        modal.hide();

                        // Mostra messaggio di successo
                        showAlert('Report generato e scaricato con successo!', 'success');
                    });
                } else {
                    // Prova a leggere la risposta come JSON per errori strutturati
                    const contentType = response.headers.get('content-type');
                    if (contentType && contentType.includes('application/json')) {
                        return response.json().then(data => {
                            throw new Error(data.detail || data.message || `Errore HTTP ${response.status}`);
                        });
                    } else {
                        // Se non è JSON, leggi come testo
                        return response.text().then(text => {
                            throw new Error(`Errore HTTP ${response.status}: ${text}`);
                        });
                    }
                }
            })
            .catch(error => {
                clearTimeout(timeoutId); // Assicurati che il timeout sia cancellato

                console.error('Errore generazione report:', error);

                let errorMessage = 'Errore nella generazione del report';

                if (error.name === 'AbortError') {
                    errorMessage = 'Timeout: La generazione del report sta richiedendo troppo tempo. Riprova con un periodo più breve.';
                } else if (error.message.includes('NetworkError') || error.message.includes('fetch')) {
                    errorMessage = 'Errore di connessione: Verifica la connessione internet e riprova.';
                } else if (error.message.includes('Failed to fetch')) {
                    errorMessage = 'Impossibile contattare il server. Verifica che il servizio sia attivo.';
                } else if (error.message) {
                    errorMessage = 'Errore: ' + error.message;
                }

                showAlert(errorMessage, 'error');
            })
            .finally(() => {
                // Ripristina flag e pulsante
                isGeneratingReport = false;
                generaReportBtn.disabled = false;
                generaReportBtn.innerHTML = '<i class="fas fa-download me-1"></i>Genera Report';
            });
        });
    }
});
</script>
{% endblock %}