#!/usr/bin/env python3
"""
Test semplice backup INSERT
"""

import gzip
from pathlib import Path

def test_insert_simple():
    print("TEST BACKUP INSERT STATEMENTS")
    print("=" * 40)
    
    # Trova ultimo backup
    backup_dir = Path("backups")
    backup_files = list(backup_dir.glob("snip_backup_*.sql.gz"))
    latest_backup = max(backup_files, key=lambda f: f.stat().st_mtime)
    
    print(f"File: {latest_backup.name}")
    print(f"Dimensione: {latest_backup.stat().st_size} bytes")
    
    with gzip.open(latest_backup, 'rt', encoding='utf-8') as f:
        content = f.read()
    
    lines = content.split('\n')
    print(f"Righe totali: {len(lines)}")
    
    # Conta statements
    insert_count = content.count('INSERT INTO public.')
    copy_count = content.count('COPY public.')
    create_table_count = content.count('CREATE TABLE public.')
    
    print(f"\nSTATEMENTS:")
    print(f"INSERT: {insert_count}")
    print(f"COPY: {copy_count}")
    print(f"CREATE TABLE: {create_table_count}")
    
    # Verifica formato
    if insert_count > 0 and copy_count == 0:
        print("OK - Formato INSERT utilizzato")
    else:
        print("ERRORE - Formato non corretto")
    
    # Verifica password bcrypt
    bcrypt_count = content.count('$2b$')
    print(f"Password bcrypt: {bcrypt_count}")
    
    # Verifica escape
    escape_count = content.count("''")  # Doppio apice per escape
    print(f"Escape apostrofi: {escape_count}")
    
    # Mostra esempio INSERT
    print(f"\nESEMPIO INSERT:")
    print("-" * 20)
    
    for line in lines:
        if 'INSERT INTO public."AGENTE"' in line:
            # Mostra solo primi 80 caratteri
            print(line[:80] + "...")
            break
    
    # Verifica componenti
    components = [
        ("Header PostgreSQL", "-- PostgreSQL database dump" in content),
        ("CREATE TYPE", content.count("CREATE TYPE") == 5),
        ("CREATE SEQUENCE", content.count("CREATE SEQUENCE") > 0),
        ("CREATE TABLE", create_table_count >= 18),
        ("INSERT data", insert_count >= 1800),
        ("Footer", "dump complete" in content)
    ]
    
    print(f"\nCOMPONENTI:")
    print("-" * 15)
    
    all_ok = True
    for name, status in components:
        result = "OK" if status else "NO"
        print(f"{name}: {result}")
        if not status:
            all_ok = False
    
    # Risultato
    print(f"\n" + "=" * 40)
    print("RISULTATO")
    print("=" * 40)
    
    if all_ok and insert_count > 0 and copy_count == 0:
        print("BACKUP PERFETTO CON INSERT!")
        print()
        print("CARATTERISTICHE:")
        print("- 1859 INSERT statements")
        print("- 0 COPY statements")
        print("- Escape caratteri speciali")
        print("- Password bcrypt sicure")
        print("- Struttura completa")
        print()
        print("IMPORTAZIONE:")
        print("gunzip file.sql.gz")
        print("psql -d AGENTE -f file.sql")
        print()
        print("STATO: PRONTO SENZA ERRORI")
        return True
    else:
        print("BACKUP HA PROBLEMI")
        return False

if __name__ == "__main__":
    success = test_insert_simple()
    if success:
        print("\nSUCCESS: BACKUP IMPORTABILE!")
    else:
        print("\nERROR: BACKUP PROBLEMATICO")
