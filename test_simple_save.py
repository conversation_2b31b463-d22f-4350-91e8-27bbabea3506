#!/usr/bin/env python3
"""
Test semplice per verificare il salvataggio configurazioni
"""

from database import SessionLocal
from sqlalchemy import text
from datetime import datetime

def test_simple_save():
    """Test semplice di salvataggio configurazioni"""
    print("🔧 Testing Simple Save...")
    
    db = SessionLocal()
    try:
        # Crea tabella se non esiste
        db.execute(text("""
            CREATE TABLE IF NOT EXISTS "SYSTEM_CONFIG" (
                config_key VARCHAR(100) PRIMARY KEY,
                config_value TEXT NOT NULL,
                config_type VARCHAR(20) DEFAULT 'string',
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_by VARCHAR(255)
            )
        """))
        db.commit()
        
        # Test inserimento configurazioni database
        print("1. Inserimento configurazioni database...")
        test_configs = [
            ("database_backup_schedule", "daily", "string"),
            ("database_backup_retention", "30", "integer"),
            ("database_auto_vacuum", "true", "boolean"),
            ("reporting_daily_report", "enabled", "string"),
            ("reporting_export_format", "excel", "string"),
            ("reporting_auto_export", "true", "boolean"),
            ("system_debug_mode", "disabled", "string"),
            ("system_log_level", "INFO", "string"),
            ("system_cache_enabled", "true", "boolean")
        ]
        
        saved_count = 0
        for key, value, config_type in test_configs:
            try:
                # Prova update
                result = db.execute(text("""
                    UPDATE "SYSTEM_CONFIG"
                    SET config_value = :value, config_type = :type, updated_at = CURRENT_TIMESTAMP
                    WHERE config_key = :key
                """), {
                    "key": key,
                    "value": value,
                    "type": config_type
                })
                
                # Se non ha aggiornato, inserisci
                if hasattr(result, "rowcount") and getattr(result, "rowcount", 0) == 0:
                    db.execute(text("""
                        INSERT INTO "SYSTEM_CONFIG" (config_key, config_value, config_type, updated_at)
                        VALUES (:key, :value, :type, CURRENT_TIMESTAMP)
                    """), {
                        "key": key,
                        "value": value,
                        "type": config_type
                    })
                
                saved_count += 1
                print(f"   ✅ Salvato: {key} = {value}")
                
            except Exception as e:
                print(f"   ❌ Errore salvando {key}: {e}")
        
        db.commit()
        print(f"   Totale salvate: {saved_count}/{len(test_configs)}")
        
        # Verifica salvataggio
        print("\n2. Verifica salvataggio...")
        result = db.execute(text("""
            SELECT config_key, config_value, config_type
            FROM "SYSTEM_CONFIG"
            WHERE config_key LIKE 'database_%' OR config_key LIKE 'reporting_%' OR config_key LIKE 'system_%'
            ORDER BY config_key
        """))
        
        configs = result.fetchall()
        print(f"   Configurazioni trovate: {len(configs)}")
        
        # Raggruppa per sezione
        sections = {}
        for row in configs:
            section = row[0].split('_')[0]
            if section not in sections:
                sections[section] = []
            sections[section].append(row)
        
        for section, items in sections.items():
            print(f"   - {section}: {len(items)} configurazioni")
            for key, value, config_type in items[:3]:  # Mostra prime 3
                print(f"     {key}: {value} ({config_type})")
            if len(items) > 3:
                print(f"     ... e altre {len(items) - 3}")
        
        return len(configs) >= len(test_configs)
        
    except Exception as e:
        print(f"❌ Errore: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        db.close()

def test_get_all_configurations():
    """Test lettura configurazioni come fa get_all_configurations"""
    print("\n🔍 Testing Configuration Reading...")
    
    db = SessionLocal()
    try:
        # Configurazioni predefinite (come in get_all_configurations)
        default_configs = {
            "database": {
                "backup_schedule": "daily",
                "backup_time": "02:00",
                "backup_retention": 30,
                "auto_vacuum": True
            },
            "reporting": {
                "daily_report": "enabled",
                "export_format": "excel",
                "auto_export": True
            },
            "system": {
                "debug_mode": "disabled",
                "log_level": "INFO",
                "cache_enabled": True
            }
        }
        
        # Leggi configurazioni dal database
        result = db.execute(text("""
            SELECT config_key, config_value, config_type
            FROM "SYSTEM_CONFIG"
        """))
        
        saved_configs = result.fetchall()
        print(f"   Configurazioni nel database: {len(saved_configs)}")
        
        # Applica configurazioni salvate (come fa get_all_configurations)
        for row in saved_configs:
            key_parts = row[0].split('_', 1)
            if len(key_parts) < 2:
                continue
            
            section = key_parts[0]
            key = key_parts[1]
            value = row[1]
            config_type = row[2]
            
            # Converti il valore nel tipo corretto
            if config_type == 'integer':
                value = int(value)
            elif config_type == 'boolean':
                value = value.lower() == 'true'
            
            # Applica la configurazione
            if section in default_configs:
                default_configs[section][key] = value
                print(f"   ✅ Applicato: {section}.{key} = {value}")
        
        # Mostra risultato finale
        print("\n   Configurazioni finali:")
        for section, config in default_configs.items():
            print(f"   - {section}: {len(config)} campi")
            for key, value in list(config.items())[:3]:
                print(f"     {key}: {value}")
        
        # Verifica che le sezioni abbiano configurazioni
        database_has_configs = len(default_configs.get('database', {})) > 4
        reporting_has_configs = len(default_configs.get('reporting', {})) > 3
        system_has_configs = len(default_configs.get('system', {})) > 3
        
        print(f"\n   Database complete: {'✅' if database_has_configs else '❌'}")
        print(f"   Reporting complete: {'✅' if reporting_has_configs else '❌'}")
        print(f"   System complete: {'✅' if system_has_configs else '❌'}")
        
        return database_has_configs and reporting_has_configs and system_has_configs
        
    except Exception as e:
        print(f"❌ Errore lettura: {e}")
        return False
    finally:
        db.close()

def main():
    print("🧪 TESTING SIMPLE CONFIGURATION SAVE")
    print("=" * 50)
    print(f"Timestamp: {datetime.now()}")
    print()
    
    results = []
    
    # Test 1: Salvataggio semplice
    try:
        result = test_simple_save()
        results.append(("Simple Save", result))
    except Exception as e:
        print(f"❌ Errore test save: {e}")
        results.append(("Simple Save", False))
    
    # Test 2: Lettura configurazioni
    try:
        result = test_get_all_configurations()
        results.append(("Configuration Reading", result))
    except Exception as e:
        print(f"❌ Errore test reading: {e}")
        results.append(("Configuration Reading", False))
    
    # Riepilogo
    print("\n" + "=" * 50)
    print("📊 RIEPILOGO TEST")
    print("=" * 50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name:<25} {status}")
        if result:
            passed += 1
    
    print(f"\nRisultato: {passed}/{total} test passati")
    
    if passed == total:
        print("🎉 Tutti i test sono passati!")
        print("✅ Il salvataggio delle configurazioni funziona!")
    else:
        print("⚠️ Alcuni test hanno fallito")

if __name__ == "__main__":
    main()
