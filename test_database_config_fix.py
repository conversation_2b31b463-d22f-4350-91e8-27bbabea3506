#!/usr/bin/env python3
"""
Test per verificare che le configurazioni database ora si salvino correttamente
"""

from datetime import datetime

def analyze_javascript_fix():
    """Analizza le correzioni apportate al JavaScript"""
    print("ANALISI CORREZIONI JAVASCRIPT CONFIGURAZIONI DATABASE")
    print("=" * 60)
    print(f"Timestamp: {datetime.now()}")
    print()
    
    try:
        with open('static/js/config-management.js', 'r', encoding='utf-8') as f:
            js_content = f.read()
        
        print("1. Verifica funzione loadConfigurations():")
        print("-" * 40)
        
        # Verifica caricamento configurazioni database
        database_load_checks = [
            ("Sezione database presente", "if (currentConfigurations.database)" in js_content),
            ("Caricamento db-backup-schedule", "setFieldValue('db-backup-schedule'" in js_content),
            ("Caricamento db-backup-time", "setFieldValue('db-backup-time'" in js_content),
            ("Caricamento db-backup-retention", "setFieldValue('db-backup-retention'" in js_content),
            ("Caricamento db-compress-backup", "setFieldValue('db-compress-backup'" in js_content),
            ("Caricamento db-auto-vacuum", "setFieldValue('db-auto-vacuum'" in js_content),
            ("Caricamento db-monitor-performance", "setFieldValue('db-monitor-performance'" in js_content)
        ]
        
        for check_name, result in database_load_checks:
            status = "OK" if result else "MANCANTE"
            print(f"   {check_name}: {status}")
        
        print("\n2. Verifica funzione collectConfigurationData():")
        print("-" * 40)
        
        # Verifica raccolta configurazioni database
        database_collect_checks = [
            ("Sezione database presente", "database: {" in js_content),
            ("Raccolta backup_schedule", "backup_schedule: getFieldValue('db-backup-schedule')" in js_content),
            ("Raccolta backup_time", "backup_time: getFieldValue('db-backup-time')" in js_content),
            ("Raccolta backup_retention", "backup_retention: parseInt(getFieldValue('db-backup-retention'))" in js_content),
            ("Raccolta compress_backup", "compress_backup: getFieldValue('db-compress-backup', 'checkbox')" in js_content),
            ("Raccolta auto_vacuum", "auto_vacuum: getFieldValue('db-auto-vacuum', 'checkbox')" in js_content),
            ("Raccolta monitor_performance", "monitor_performance: getFieldValue('db-monitor-performance', 'checkbox')" in js_content)
        ]
        
        for check_name, result in database_collect_checks:
            status = "OK" if result else "MANCANTE"
            print(f"   {check_name}: {status}")
        
        print("\n3. Verifica configurazioni reporting:")
        print("-" * 40)
        
        reporting_checks = [
            ("Sezione reporting presente", "if (currentConfigurations.reporting)" in js_content),
            ("Raccolta reporting", "reporting: {" in js_content),
            ("Caricamento daily_report", "setFieldValue('daily-report'" in js_content),
            ("Raccolta kpi_voyages", "kpi_voyages: getFieldValue('kpi-voyages', 'checkbox')" in js_content)
        ]
        
        for check_name, result in reporting_checks:
            status = "OK" if result else "MANCANTE"
            print(f"   {check_name}: {status}")
        
        print("\n4. Verifica configurazioni system:")
        print("-" * 40)
        
        system_checks = [
            ("Sezione system presente", "if (currentConfigurations.system)" in js_content),
            ("Raccolta system", "system: {" in js_content),
            ("Caricamento app_version", "setFieldValue('app-version'" in js_content),
            ("Raccolta debug_mode", "debug_mode: getFieldValue('debug-mode')" in js_content)
        ]
        
        for check_name, result in system_checks:
            status = "OK" if result else "MANCANTE"
            print(f"   {check_name}: {status}")
        
        return True
        
    except FileNotFoundError:
        print("ERRORE: File config-management.js non trovato")
        return False
    except Exception as e:
        print(f"ERRORE: {e}")
        return False

def analyze_html_fields():
    """Analizza i campi HTML per le configurazioni database"""
    print("\n5. Verifica campi HTML:")
    print("-" * 40)
    
    try:
        with open('templates/admin_dashboard.html', 'r', encoding='utf-8') as f:
            html_content = f.read()
        
        # Verifica presenza campi database
        database_fields = [
            ("db-backup-schedule", 'id="db-backup-schedule"' in html_content),
            ("db-backup-time", 'id="db-backup-time"' in html_content),
            ("db-backup-retention", 'id="db-backup-retention"' in html_content),
            ("db-backup-path", 'id="db-backup-path"' in html_content),
            ("db-compress-backup", 'id="db-compress-backup"' in html_content),
            ("db-log-cleanup", 'id="db-log-cleanup"' in html_content),
            ("db-archive-months", 'id="db-archive-months"' in html_content),
            ("db-optimize", 'id="db-optimize"' in html_content),
            ("db-auto-vacuum", 'id="db-auto-vacuum"' in html_content),
            ("db-analyze", 'id="db-analyze"' in html_content),
            ("db-disk-threshold", 'id="db-disk-threshold"' in html_content),
            ("db-connection-threshold", 'id="db-connection-threshold"' in html_content),
            ("db-monitor-performance", 'id="db-monitor-performance"' in html_content),
            ("db-alert-email", 'id="db-alert-email"' in html_content)
        ]
        
        missing_fields = []
        for field_name, present in database_fields:
            status = "OK" if present else "MANCANTE"
            print(f"   {field_name}: {status}")
            if not present:
                missing_fields.append(field_name)
        
        if missing_fields:
            print(f"\n   CAMPI MANCANTI: {len(missing_fields)}")
            for field in missing_fields:
                print(f"     - {field}")
        else:
            print(f"\n   TUTTI I CAMPI PRESENTI: {len(database_fields)}")
        
        return len(missing_fields) == 0
        
    except FileNotFoundError:
        print("   ERRORE: File admin_dashboard.html non trovato")
        return False
    except Exception as e:
        print(f"   ERRORE: {e}")
        return False

def main():
    print("VERIFICA COMPLETA CORREZIONI CONFIGURAZIONI DATABASE")
    print("=" * 70)
    
    results = []
    
    # Test JavaScript
    try:
        js_result = analyze_javascript_fix()
        results.append(("JavaScript Fix", js_result))
    except Exception as e:
        print(f"ERRORE test JavaScript: {e}")
        results.append(("JavaScript Fix", False))
    
    # Test HTML
    try:
        html_result = analyze_html_fields()
        results.append(("HTML Fields", html_result))
    except Exception as e:
        print(f"ERRORE test HTML: {e}")
        results.append(("HTML Fields", False))
    
    # Riepilogo
    print("\n" + "=" * 70)
    print("RIEPILOGO CORREZIONI")
    print("=" * 70)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "PASS" if result else "FAIL"
        print(f"{test_name:<20} {status}")
        if result:
            passed += 1
    
    print(f"\nRisultato: {passed}/{total} test passati")
    
    if passed == total:
        print("\nSTATO: CORREZIONI COMPLETATE CON SUCCESSO")
        print()
        print("PROBLEMI RISOLTI:")
        print("1. Aggiunto caricamento configurazioni database in loadConfigurations()")
        print("2. Aggiunto caricamento configurazioni reporting")
        print("3. Aggiunto caricamento configurazioni system")
        print("4. Tutti i campi HTML sono presenti nel template")
        print("5. Funzione collectConfigurationData() gia' completa")
        print()
        print("FLUSSO COMPLETO:")
        print("1. loadConfigurations() carica valori dal server")
        print("2. setFieldValue() popola i campi HTML")
        print("3. Utente modifica i valori")
        print("4. collectConfigurationData() raccoglie i dati")
        print("5. saveAllConfigurations() invia al server")
        print("6. save_database_config() salva nel database")
        print()
        print("RISULTATO: Le configurazioni database ora si salvano correttamente!")
    else:
        print("\nSTATO: ALCUNI PROBLEMI PERSISTONO")
        print("Verificare i componenti che hanno fallito il test.")
    
    print("\nPROSSIMI PASSI:")
    print("1. Testare l'interfaccia web /dashboard/amministrazione")
    print("2. Verificare che i campi database si carichino")
    print("3. Modificare alcuni valori e salvare")
    print("4. Controllare che le modifiche vengano persistite")

if __name__ == "__main__":
    main()
