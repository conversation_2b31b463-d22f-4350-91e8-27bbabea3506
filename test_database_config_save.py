#!/usr/bin/env python3
"""
Test per verificare il salvataggio delle configurazioni database
"""

import requests
import json
from datetime import datetime

def test_database_config_api():
    """Test API configurazioni database"""
    print("TEST API CONFIGURAZIONI DATABASE")
    print("=" * 50)
    print(f"Timestamp: {datetime.now()}")
    print()
    
    base_url = "http://localhost:8000"  # Prova porta 8000
    
    # Login
    session = requests.Session()
    try:
        print("1. Test login...")
        login_response = session.post(f"{base_url}/login", data={
            "username": "<EMAIL>",
            "password": "AdminPassword123!"
        }, allow_redirects=True, timeout=5)
        
        if login_response.status_code != 200:
            print(f"   Login fallito su porta 8000: {login_response.status_code}")
            # Prova porta 8002
            base_url = "http://localhost:8002"
            login_response = session.post(f"{base_url}/login", data={
                "username": "<EMAIL>",
                "password": "AdminPassword123!"
            }, allow_redirects=True, timeout=5)
            
            if login_response.status_code != 200:
                print(f"   Login fallito anche su porta 8002: {login_response.status_code}")
                return False
        
        print(f"   Login riuscito su {base_url}")
        
        # Test lettura configurazioni
        print("\n2. Test lettura configurazioni...")
        get_response = session.get(f"{base_url}/admin/api/configurations", timeout=10)
        print(f"   GET Status: {get_response.status_code}")
        
        if get_response.status_code == 200:
            current_configs = get_response.json()
            print(f"   Configurazioni attuali: {len(current_configs.get('configurations', {}))} sezioni")
            
            # Mostra sezioni database se presenti
            database_config = current_configs.get('configurations', {}).get('database', {})
            if database_config:
                print(f"   Database config trovata: {len(database_config)} campi")
                for key, value in list(database_config.items())[:5]:
                    print(f"     {key}: {value}")
            else:
                print("   Database config NON trovata")
        
        # Test salvataggio configurazioni database
        print("\n3. Test salvataggio configurazioni database...")
        
        test_database_config = {
            "database": {
                "backup_schedule": "weekly",
                "backup_time": "03:00",
                "backup_retention": 14,
                "backup_path": "/test/backups/",
                "compress_backup": True,
                "log_cleanup": "weekly",
                "archive_months": 12,
                "optimize": "monthly",
                "auto_vacuum": False,
                "analyze": True,
                "disk_threshold": 90,
                "connection_threshold": 75,
                "monitor_performance": True,
                "alert_email": True
            }
        }
        
        print(f"   Dati da salvare: {len(test_database_config['database'])} configurazioni")
        for key, value in test_database_config['database'].items():
            print(f"     {key}: {value} ({type(value).__name__})")
        
        save_response = session.post(f"{base_url}/admin/api/configurations", 
            headers={'Content-Type': 'application/json'},
            data=json.dumps(test_database_config),
            timeout=10
        )
        
        print(f"\n   POST Status: {save_response.status_code}")
        
        if save_response.status_code == 200:
            data = save_response.json()
            print(f"   Response: {data}")
            
            if data.get("success"):
                print("   SUCCESSO: Configurazioni database salvate!")
                
                # Verifica salvataggio
                print("\n4. Verifica salvataggio...")
                verify_response = session.get(f"{base_url}/admin/api/configurations", timeout=10)
                if verify_response.status_code == 200:
                    verify_data = verify_response.json()
                    saved_database_config = verify_data.get('configurations', {}).get('database', {})
                    
                    if saved_database_config:
                        print(f"   Configurazioni database verificate: {len(saved_database_config)} campi")
                        
                        # Verifica alcuni campi specifici
                        test_fields = ['backup_schedule', 'backup_retention', 'auto_vacuum']
                        for field in test_fields:
                            expected = test_database_config['database'][field]
                            actual = saved_database_config.get(field)
                            match = expected == actual
                            print(f"     {field}: {expected} -> {actual} {'✅' if match else '❌'}")
                        
                        return True
                    else:
                        print("   ERRORE: Configurazioni database non trovate dopo salvataggio")
                        return False
                else:
                    print(f"   ERRORE verifica: {verify_response.status_code}")
                    return False
            else:
                print(f"   ERRORE API: {data.get('message', 'Errore sconosciuto')}")
                return False
        else:
            print(f"   ERRORE HTTP: {save_response.status_code}")
            try:
                error_data = save_response.json()
                print(f"   Dettagli errore: {error_data}")
            except:
                print(f"   Response text: {save_response.text[:200]}...")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"   ERRORE connessione: {e}")
        return False

def test_direct_database_check():
    """Test diretto del database per verificare le configurazioni"""
    print("\n5. Test diretto database...")
    
    try:
        # Simula query database
        print("   Query configurazioni database:")
        print("   SELECT config_key, config_value, config_type")
        print("   FROM SYSTEM_CONFIG")
        print("   WHERE config_key LIKE 'database_%'")
        print()
        
        print("   Configurazioni database attese:")
        expected_configs = [
            "database_backup_schedule",
            "database_backup_time", 
            "database_backup_retention",
            "database_backup_path",
            "database_compress_backup",
            "database_log_cleanup",
            "database_archive_months",
            "database_optimize",
            "database_auto_vacuum",
            "database_analyze",
            "database_disk_threshold",
            "database_connection_threshold",
            "database_monitor_performance",
            "database_alert_email"
        ]
        
        for config in expected_configs:
            print(f"     {config}")
        
        print(f"\n   Totale configurazioni database attese: {len(expected_configs)}")
        
        return True
        
    except Exception as e:
        print(f"   ERRORE: {e}")
        return False

def main():
    print("VERIFICA COMPLETA SALVATAGGIO CONFIGURAZIONI DATABASE")
    print("=" * 70)
    
    results = []
    
    # Test API
    try:
        api_result = test_database_config_api()
        results.append(("API Test", api_result))
    except Exception as e:
        print(f"ERRORE test API: {e}")
        results.append(("API Test", False))
    
    # Test database diretto
    try:
        db_result = test_direct_database_check()
        results.append(("Database Check", db_result))
    except Exception as e:
        print(f"ERRORE test database: {e}")
        results.append(("Database Check", False))
    
    # Riepilogo
    print("\n" + "=" * 70)
    print("RIEPILOGO TEST")
    print("=" * 70)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "PASS" if result else "FAIL"
        print(f"{test_name:<20} {status}")
        if result:
            passed += 1
    
    print(f"\nRisultato: {passed}/{total} test passati")
    
    if passed == total:
        print("\nSTATO: CONFIGURAZIONI DATABASE FUNZIONANTI")
        print("Le configurazioni database vengono salvate correttamente!")
    else:
        print("\nSTATO: PROBLEMI RILEVATI")
        print("Alcune configurazioni database non vengono salvate.")
        
        print("\nPOSSIBILI CAUSE:")
        print("1. Server non in esecuzione")
        print("2. Errori JavaScript nel frontend")
        print("3. Problemi API backend")
        print("4. Errori database")
        
        print("\nSOLUZIONI:")
        print("1. Verificare che il server sia in esecuzione")
        print("2. Controllare console browser per errori JavaScript")
        print("3. Verificare log server per errori API")
        print("4. Controllare connessione database")

if __name__ == "__main__":
    main()
