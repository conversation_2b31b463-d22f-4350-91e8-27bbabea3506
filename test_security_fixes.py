#!/usr/bin/env python3
"""
Test per verificare le correzioni ai problemi di sicurezza
"""

import requests
import json
import time
from datetime import datetime

def test_account_lockout():
    """Test blocco account con configurazione corretta"""
    print("🔐 Testing Account Lockout Fix...")
    
    base_url = "http://localhost:8002"
    test_email = "<EMAIL>"
    wrong_password = "wrong_password"
    
    print(f"1. Test tentativi login falliti per {test_email}")
    
    # Simula 5 tentativi falliti
    for i in range(5):
        response = requests.post(f"{base_url}/login", data={
            "username": test_email,
            "password": wrong_password
        }, allow_redirects=False)
        
        print(f"   Tentativo {i+1}: Status {response.status_code}")
        time.sleep(1)
    
    # Il 6° tentativo dovrebbe essere bloccato
    response = requests.post(f"{base_url}/login", data={
        "username": test_email,
        "password": wrong_password
    }, allow_redirects=False)
    
    if "bloccato" in response.text.lower():
        print("✅ Account lockout funziona correttamente")
        return True
    else:
        print("❌ Account lockout non funziona")
        return False

def test_password_validation_registration():
    """Test validazione password in registrazione"""
    print("\n🔒 Testing Password Validation in Registration...")
    
    base_url = "http://localhost:8002"
    
    # Test password troppo corta
    test_cases = [
        {
            "password": "123",
            "expected": "troppo corta",
            "description": "Password troppo corta"
        },
        {
            "password": "password",
            "expected": "maiuscola",
            "description": "Password senza maiuscole (se richieste)"
        },
        {
            "password": "Password",
            "expected": "numero",
            "description": "Password senza numeri (se richiesti)"
        },
        {
            "password": "Password123",
            "expected": "carattere speciale",
            "description": "Password senza caratteri speciali (se richiesti)"
        }
    ]
    
    for test_case in test_cases:
        print(f"   Test: {test_case['description']}")
        
        response = requests.post(f"{base_url}/register", data={
            "nome": "Test",
            "cognome": "User",
            "email": f"test_{int(time.time())}@example.com",
            "password": test_case["password"],
            "reparto": "OPERATIVO"
        }, allow_redirects=False)
        
        if test_case["expected"].lower() in response.text.lower():
            print(f"   ✅ Validazione corretta per: {test_case['description']}")
        else:
            print(f"   ⚠️ Validazione non trovata per: {test_case['description']}")
    
    return True

def test_backup_system():
    """Test sistema backup automatico"""
    print("\n💾 Testing Backup System...")
    
    base_url = "http://localhost:8002"
    
    # Test configurazioni backup (richiede autenticazione admin)
    try:
        response = requests.get(f"{base_url}/api/admin/backup/config")
        
        if response.status_code == 401:
            print("   ✅ API backup protetta correttamente (richiede autenticazione)")
        elif response.status_code == 200:
            data = response.json()
            if data.get("success"):
                print("   ✅ API configurazioni backup funziona")
                print(f"   📋 Configurazioni: {data.get('config', {})}")
            else:
                print("   ❌ Errore API configurazioni backup")
        else:
            print(f"   ⚠️ Status inaspettato: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ Errore test backup: {e}")
    
    return True

def test_security_config_api():
    """Test API configurazioni sicurezza"""
    print("\n⚙️ Testing Security Config API...")
    
    base_url = "http://localhost:8002"
    
    try:
        response = requests.get(f"{base_url}/api/security-config")
        
        if response.status_code == 200:
            data = response.json()
            if data.get("success"):
                config = data.get("config", {})
                print("   ✅ API configurazioni sicurezza funziona")
                print(f"   🔧 Configurazioni caricate:")
                print(f"      - Min password length: {config.get('password_min_length', 'N/A')}")
                print(f"      - Require uppercase: {config.get('password_uppercase', 'N/A')}")
                print(f"      - Require numbers: {config.get('password_numbers', 'N/A')}")
                print(f"      - Require special: {config.get('password_special', 'N/A')}")
                print(f"      - Max login attempts: {config.get('max_login_attempts', 'N/A')}")
                print(f"      - Account lockout minutes: {config.get('account_lockout_minutes', 'N/A')}")
                return True
            else:
                print("   ❌ API restituisce errore")
        else:
            print(f"   ❌ Status code: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ Errore test security config: {e}")
    
    return False

def main():
    """Esegue tutti i test"""
    print("🧪 TESTING SECURITY FIXES")
    print("=" * 50)
    print(f"Timestamp: {datetime.now()}")
    print()
    
    results = []
    
    # Test 1: Account Lockout
    try:
        result = test_account_lockout()
        results.append(("Account Lockout", result))
    except Exception as e:
        print(f"❌ Errore test account lockout: {e}")
        results.append(("Account Lockout", False))
    
    # Test 2: Password Validation
    try:
        result = test_password_validation_registration()
        results.append(("Password Validation", result))
    except Exception as e:
        print(f"❌ Errore test password validation: {e}")
        results.append(("Password Validation", False))
    
    # Test 3: Security Config API
    try:
        result = test_security_config_api()
        results.append(("Security Config API", result))
    except Exception as e:
        print(f"❌ Errore test security config: {e}")
        results.append(("Security Config API", False))
    
    # Test 4: Backup System
    try:
        result = test_backup_system()
        results.append(("Backup System", result))
    except Exception as e:
        print(f"❌ Errore test backup system: {e}")
        results.append(("Backup System", False))
    
    # Riepilogo
    print("\n" + "=" * 50)
    print("📊 RIEPILOGO TEST")
    print("=" * 50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name:<25} {status}")
        if result:
            passed += 1
    
    print(f"\nRisultato: {passed}/{total} test passati")
    
    if passed == total:
        print("🎉 Tutti i test sono passati!")
    else:
        print("⚠️ Alcuni test hanno fallito - verificare le correzioni")
    
    print("\n🔧 AZIONI CONSIGLIATE:")
    print("1. Verifica che il server sia in esecuzione su localhost:8002")
    print("2. Controlla le configurazioni nel dashboard amministrazione")
    print("3. Testa manualmente la registrazione con password diverse")
    print("4. Verifica i log del sistema per errori")

if __name__ == "__main__":
    main()
