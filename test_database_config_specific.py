#!/usr/bin/env python3
"""
Test specifico per il salvataggio configurazioni database dal tab Database
"""

from datetime import datetime

def test_database_tab_functionality():
    """Test funzionalità tab database"""
    print("TEST FUNZIONALITA TAB DATABASE")
    print("=" * 50)
    print(f"Timestamp: {datetime.now()}")
    print()
    
    # 1. Verifica presenza tab database
    print("1. Verifica tab database nel template:")
    print("-" * 40)
    
    try:
        with open('templates/admin_dashboard.html', 'r', encoding='utf-8') as f:
            html_content = f.read()
        
        tab_checks = [
            ("Tab button database", 'id="database-tab"' in html_content),
            ("Tab target database-config", 'data-bs-target="#database-config"' in html_content),
            ("Tab pane database-config", 'id="database-config"' in html_content),
            ("Pulsante Salva Tutto", 'onclick="saveAllConfigurations()"' in html_content),
            ("Sezione configurazioni", 'id="config-section"' in html_content)
        ]
        
        for check_name, result in tab_checks:
            status = "OK" if result else "MANCANTE"
            print(f"   {check_name}: {status}")
        
        # Verifica tutti i campi database
        print("\n   Campi database nel template:")
        database_fields = [
            'id="db-backup-schedule"',
            'id="db-backup-time"', 
            'id="db-backup-retention"',
            'id="db-backup-path"',
            'id="db-compress-backup"',
            'id="db-log-cleanup"',
            'id="db-archive-months"',
            'id="db-optimize"',
            'id="db-auto-vacuum"',
            'id="db-analyze"',
            'id="db-disk-threshold"',
            'id="db-connection-threshold"',
            'id="db-monitor-performance"',
            'id="db-alert-email"'
        ]
        
        missing_fields = []
        for field in database_fields:
            if field not in html_content:
                missing_fields.append(field)
        
        if missing_fields:
            print(f"   CAMPI MANCANTI: {len(missing_fields)}")
            for field in missing_fields[:5]:  # Mostra primi 5
                print(f"     - {field}")
        else:
            print(f"   TUTTI I CAMPI PRESENTI: {len(database_fields)}")
        
        return len(missing_fields) == 0
        
    except FileNotFoundError:
        print("   ERRORE: File admin_dashboard.html non trovato")
        return False
    except Exception as e:
        print(f"   ERRORE: {e}")
        return False

def test_javascript_database_handling():
    """Test gestione database nel JavaScript"""
    print("\n2. Verifica JavaScript gestione database:")
    print("-" * 40)
    
    try:
        with open('static/js/config-management.js', 'r', encoding='utf-8') as f:
            js_content = f.read()
        
        js_checks = [
            ("Funzione saveAllConfigurations", "function saveAllConfigurations()" in js_content),
            ("Funzione collectConfigurationData", "function collectConfigurationData()" in js_content),
            ("Sezione database in collect", "database: {" in js_content),
            ("Caricamento database in load", "if (currentConfigurations.database)" in js_content),
            ("Funzione getFieldValue", "function getFieldValue(" in js_content),
            ("Funzione setFieldValue", "function setFieldValue(" in js_content)
        ]
        
        for check_name, result in js_checks:
            status = "OK" if result else "MANCANTE"
            print(f"   {check_name}: {status}")
        
        # Verifica raccolta campi database
        print("\n   Verifica raccolta campi database:")
        database_collect_fields = [
            "backup_schedule: getFieldValue('db-backup-schedule')",
            "backup_time: getFieldValue('db-backup-time')",
            "backup_retention: parseInt(getFieldValue('db-backup-retention'))",
            "compress_backup: getFieldValue('db-compress-backup', 'checkbox')",
            "auto_vacuum: getFieldValue('db-auto-vacuum', 'checkbox')",
            "monitor_performance: getFieldValue('db-monitor-performance', 'checkbox')"
        ]
        
        missing_collect = []
        for field in database_collect_fields:
            if field not in js_content:
                missing_collect.append(field)
        
        if missing_collect:
            print(f"   RACCOLTA MANCANTE: {len(missing_collect)}")
            for field in missing_collect[:3]:
                print(f"     - {field}")
        else:
            print(f"   RACCOLTA COMPLETA: {len(database_collect_fields)} campi")
        
        # Verifica caricamento campi database
        print("\n   Verifica caricamento campi database:")
        database_load_fields = [
            "setFieldValue('db-backup-schedule', database.backup_schedule)",
            "setFieldValue('db-backup-time', database.backup_time)",
            "setFieldValue('db-backup-retention', database.backup_retention)",
            "setFieldValue('db-compress-backup', database.compress_backup, 'checkbox')",
            "setFieldValue('db-auto-vacuum', database.auto_vacuum, 'checkbox')",
            "setFieldValue('db-monitor-performance', database.monitor_performance, 'checkbox')"
        ]
        
        missing_load = []
        for field in database_load_fields:
            if field not in js_content:
                missing_load.append(field)
        
        if missing_load:
            print(f"   CARICAMENTO MANCANTE: {len(missing_load)}")
            for field in missing_load[:3]:
                print(f"     - {field}")
        else:
            print(f"   CARICAMENTO COMPLETO: {len(database_load_fields)} campi")
        
        return len(missing_collect) == 0 and len(missing_load) == 0
        
    except FileNotFoundError:
        print("   ERRORE: File config-management.js non trovato")
        return False
    except Exception as e:
        print(f"   ERRORE: {e}")
        return False

def test_backend_database_save():
    """Test funzione backend save_database_config"""
    print("\n3. Verifica backend save_database_config:")
    print("-" * 40)
    
    try:
        with open('main.py', 'r', encoding='utf-8') as f:
            main_content = f.read()
        
        backend_checks = [
            ("Funzione save_database_config", "def save_database_config(" in main_content),
            ("API /admin/api/configurations", "@app.post(\"/admin/api/configurations\")" in main_content),
            ("Gestione sezione database", "if 'database' in configurations:" in main_content),
            ("Chiamata save_database_config", "save_database_config(" in main_content),
            ("Mapping database_", "database_" in main_content)
        ]
        
        for check_name, result in backend_checks:
            status = "OK" if result else "MANCANTE"
            print(f"   {check_name}: {status}")
        
        # Verifica mapping campi database
        if "def save_database_config(" in main_content:
            print("\n   Funzione save_database_config trovata")
            
            # Cerca i campi mappati
            database_mappings = [
                "database_backup_schedule",
                "database_backup_time",
                "database_backup_retention",
                "database_compress_backup",
                "database_auto_vacuum",
                "database_monitor_performance"
            ]
            
            missing_mappings = []
            for mapping in database_mappings:
                if mapping not in main_content:
                    missing_mappings.append(mapping)
            
            if missing_mappings:
                print(f"   MAPPING MANCANTI: {len(missing_mappings)}")
                for mapping in missing_mappings[:3]:
                    print(f"     - {mapping}")
            else:
                print(f"   MAPPING COMPLETI: {len(database_mappings)} campi")
        else:
            print("   ERRORE: Funzione save_database_config non trovata")
        
        return "def save_database_config(" in main_content
        
    except FileNotFoundError:
        print("   ERRORE: File main.py non trovato")
        return False
    except Exception as e:
        print(f"   ERRORE: {e}")
        return False

def analyze_potential_issues():
    """Analizza potenziali problemi"""
    print("\n4. Analisi potenziali problemi:")
    print("-" * 40)
    
    issues = [
        "POSSIBILI CAUSE DEL PROBLEMA:",
        "",
        "1. JAVASCRIPT:",
        "   - Event listeners non collegati ai campi database",
        "   - Funzione collectConfigurationData() non raccoglie database",
        "   - Errori JavaScript che bloccano il salvataggio",
        "",
        "2. BACKEND:",
        "   - API non gestisce sezione 'database'",
        "   - Funzione save_database_config() non chiamata",
        "   - Errori nel mapping dei campi",
        "",
        "3. FRONTEND:",
        "   - Campi database non inclusi nel form",
        "   - ID campi non corrispondenti al JavaScript",
        "   - Tab database non attivo durante il salvataggio",
        "",
        "4. CONFIGURAZIONI:",
        "   - get_all_configurations() non include database",
        "   - Valori predefiniti non impostati",
        "   - Caricamento iniziale fallisce"
    ]
    
    for issue in issues:
        print(f"   {issue}")

def provide_debugging_steps():
    """Fornisce passi per il debugging"""
    print("\n5. Passi per il debugging:")
    print("-" * 40)
    
    steps = [
        "DEBUGGING PASSO-PASSO:",
        "",
        "1. VERIFICA BROWSER:",
        "   - Aprire /dashboard/amministrazione",
        "   - Andare su tab 'Database'",
        "   - Aprire Console (F12)",
        "   - Modificare un campo database",
        "   - Cliccare 'Salva Tutto'",
        "   - Controllare errori in console",
        "",
        "2. VERIFICA NETWORK:",
        "   - Tab Network in DevTools",
        "   - Vedere se viene fatta chiamata POST a /admin/api/configurations",
        "   - Controllare payload della richiesta",
        "   - Verificare risposta del server",
        "",
        "3. VERIFICA LOG SERVER:",
        "   - Controllare log per errori durante salvataggio",
        "   - Verificare se save_database_config() viene chiamata",
        "   - Controllare errori database",
        "",
        "4. TEST MANUALE:",
        "   - Modificare un solo campo database",
        "   - Salvare e ricaricare pagina",
        "   - Verificare se il valore è persistito"
    ]
    
    for step in steps:
        print(f"   {step}")

def main():
    print("TEST SPECIFICO CONFIGURAZIONI DATABASE")
    print("=" * 60)
    
    results = []
    
    # Test HTML
    try:
        html_result = test_database_tab_functionality()
        results.append(("HTML Template", html_result))
    except Exception as e:
        print(f"ERRORE test HTML: {e}")
        results.append(("HTML Template", False))
    
    # Test JavaScript
    try:
        js_result = test_javascript_database_handling()
        results.append(("JavaScript Handling", js_result))
    except Exception as e:
        print(f"ERRORE test JavaScript: {e}")
        results.append(("JavaScript Handling", False))
    
    # Test Backend
    try:
        backend_result = test_backend_database_save()
        results.append(("Backend Save Function", backend_result))
    except Exception as e:
        print(f"ERRORE test backend: {e}")
        results.append(("Backend Save Function", False))
    
    # Analisi problemi
    analyze_potential_issues()
    
    # Debugging steps
    provide_debugging_steps()
    
    # Riepilogo
    print("\n" + "=" * 60)
    print("RIEPILOGO TEST")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "OK" if result else "PROBLEMA"
        print(f"{test_name:<25} {status}")
        if result:
            passed += 1
    
    print(f"\nComponenti verificati: {passed}/{total}")
    
    if passed == total:
        print("\nSTATO: TUTTI I COMPONENTI SONO PRESENTI")
        print("Il problema potrebbe essere in runtime o configurazioni.")
    else:
        print("\nSTATO: PROBLEMI NEI COMPONENTI RILEVATI")
        print("Alcuni componenti necessari mancano o sono errati.")
    
    print("\nPROSSIMI PASSI:")
    print("1. Seguire i passi di debugging sopra")
    print("2. Controllare console browser per errori JavaScript")
    print("3. Verificare Network tab per chiamate API")
    print("4. Controllare log server per errori backend")

if __name__ == "__main__":
    main()
