#!/usr/bin/env python3
"""
Test per verificare il salvataggio delle configurazioni database nel dashboard amministrazione
"""

import requests
import json
import time
from datetime import datetime

def test_config_save():
    """Test salvataggio configurazioni complete"""
    print("🔧 Testing Configuration Save...")
    
    base_url = "http://localhost:8002"
    
    # Credenziali admin
    admin_email = "<EMAIL>"
    admin_password = "AdminPassword123!"
    
    print(f"1. Login come SUPER_ADMIN: {admin_email}")
    
    # Step 1: Login
    session = requests.Session()
    login_response = session.post(f"{base_url}/login", data={
        "username": admin_email,
        "password": admin_password
    }, allow_redirects=True)
    
    if login_response.status_code != 200:
        print(f"   ❌ Login fallito: {login_response.status_code}")
        return False
    
    print("   ✅ Login riuscito")

    # Test endpoint esistenza
    print("1.5. Test endpoint esistenza...")
    test_response = session.get(f"{base_url}/admin/api/configurations")
    print(f"   GET /admin/api/configurations: {test_response.status_code}")
    if test_response.status_code != 200:
        print(f"   Response: {test_response.text[:200]}...")
        return False

    # Step 2: Test configurazioni complete
    print("2. Test salvataggio configurazioni complete...")
    
    test_configurations = {
        "security": {
            "password_min_length": 8,
            "password_expiry_days": 90,
            "session_timeout": 60,
            "max_login_attempts": 5,
            "account_lockout_minutes": 15,
            "log_retention": 90,
            "password_uppercase": True,
            "password_numbers": True,
            "password_special": False,
            "two_factor_auth": False,
            "ip_whitelist": False,
            "log_login": True,
            "log_actions": True,
            "log_errors": True,
            "backup_frequency": "daily",
            "backup_retention": 30,
            "encrypt_backups": True,
            "ssl_only": True
        },
        "email": {
            "smtp_host": "smtp.gmail.com",
            "smtp_port": 587,
            "smtp_username": "<EMAIL>",
            "smtp_password": "test_password",
            "sender_email": "<EMAIL>",
            "sender_name": "Test SNIP",
            "admin_email": "<EMAIL>",
            "smtp_ssl": True
        },
        "ports": {
            "salerno": {
                "name": "Porto di Salerno Test",
                "code": "ITSRN",
                "harbor": "+39 089 123456",
                "customs": "+39 089 654321",
                "email": "<EMAIL>"
            },
            "gioia": {
                "name": "Porto di Gioia Tauro Test",
                "code": "ITGIT",
                "harbor": "+39 0966 123456",
                "customs": "+39 0966 654321",
                "email": "<EMAIL>"
            }
        },
        "sof": {
            "title": "STATEMENT OF FACTS TEST",
            "subtitle": "test shipping agency",
            "logo_size": "medium",
            "numbering": "auto",
            "logo_top": True,
            "logo_bottom": False
        },
        "interface": {
            "theme": "maritime",
            "primary_color": "#0066cc",
            "secondary_color": "#004499",
            "accent_color": "#ff6b35",
            "language": "it",
            "timezone": "Europe/Rome",
            "date_format": "DD/MM/YYYY",
            "sidebar_default": "expanded",
            "responsive_design": True,
            "dark_mode": False,
            "glassmorphism": True
        },
        "database": {
            "backup_schedule": "daily",
            "backup_time": "02:00",
            "backup_retention": 30,
            "backup_path": "/backups/snip/",
            "compress_backup": True,
            "log_cleanup": "daily",
            "archive_months": 24,
            "optimize": "weekly",
            "auto_vacuum": True,
            "analyze": True,
            "disk_threshold": 85,
            "connection_threshold": 80,
            "monitor_performance": True,
            "alert_email": True
        },
        "reporting": {
            "daily_report": "enabled",
            "daily_report_time": "08:00",
            "weekly_report": "monday",
            "monthly_report": "first",
            "kpi_voyages": True,
            "kpi_sof": True,
            "kpi_users": True,
            "kpi_ports": True,
            "kpi_performance": True,
            "kpi_revenue": False,
            "export_format": "excel",
            "auto_export": True,
            "compress_exports": False,
            "report_recipients": "<EMAIL>\<EMAIL>",
            "include_charts": True,
            "detailed_reports": False
        },
        "system": {
            "app_version": "v2.0.0",
            "debug_mode": "disabled",
            "log_level": "INFO",
            "max_upload_size": 50,
            "request_timeout": 30,
            "max_connections": 100,
            "cache_enabled": True,
            "compression_enabled": True,
            "maintenance_mode": "disabled",
            "maintenance_message": "Sistema in manutenzione test",
            "auto_updates": False
        }
    }
    
    save_response = session.post(f"{base_url}/admin/api/configurations", 
        headers={'Content-Type': 'application/json'},
        data=json.dumps(test_configurations)
    )
    
    print(f"   Save Status: {save_response.status_code}")

    # Debug: mostra la risposta
    if save_response.status_code != 200:
        print(f"   Response text: {save_response.text[:300]}...")

    if save_response.status_code == 200:
        try:
            data = save_response.json()
        except json.JSONDecodeError as e:
            print(f"   ❌ Errore parsing JSON: {e}")
            print(f"   Response text: {save_response.text[:500]}...")
            return False

        if data.get("success"):
            print("   ✅ Configurazioni salvate con successo")
            
            # Step 3: Verifica lettura configurazioni
            print("3. Verifica lettura configurazioni...")
            
            get_response = session.get(f"{base_url}/admin/api/configurations")
            
            if get_response.status_code == 200:
                get_data = get_response.json()
                if get_data.get("success"):
                    configs = get_data.get("configurations", {})
                    print(f"   ✅ Configurazioni lette: {len(configs)} sezioni")

                    # Debug: mostra le chiavi trovate
                    print(f"   Debug - Chiavi trovate: {list(configs.keys())[:10]}...")

                    # Verifica alcune configurazioni specifiche (formato strutturato)
                    security_found = 'security' in configs and isinstance(configs['security'], dict)
                    database_found = 'database' in configs and isinstance(configs['database'], dict)
                    system_found = 'system' in configs and isinstance(configs['system'], dict)
                    reporting_found = 'reporting' in configs and isinstance(configs['reporting'], dict)

                    print(f"   - Security configs: {'✅' if security_found else '❌'}")
                    print(f"   - Database configs: {'✅' if database_found else '❌'}")
                    print(f"   - System configs: {'✅' if system_found else '❌'}")
                    print(f"   - Reporting configs: {'✅' if reporting_found else '❌'}")

                    # Verifica che le configurazioni salvate siano presenti
                    if security_found:
                        security_config = configs['security']
                        test_fields = ['password_min_length', 'session_timeout', 'two_factor_auth']
                        security_complete = all(field in security_config for field in test_fields)
                        print(f"   - Security fields complete: {'✅' if security_complete else '❌'}")

                    if database_found:
                        database_config = configs['database']
                        test_fields = ['backup_schedule', 'backup_retention', 'auto_vacuum']
                        database_complete = all(field in database_config for field in test_fields)
                        print(f"   - Database fields complete: {'✅' if database_complete else '❌'}")

                    if system_found:
                        system_config = configs['system']
                        test_fields = ['debug_mode', 'log_level', 'cache_enabled']
                        system_complete = all(field in system_config for field in test_fields)
                        print(f"   - System fields complete: {'✅' if system_complete else '❌'}")

                    # Debug: mostra alcune configurazioni salvate
                    if configs:
                        print("   Debug - Alcune configurazioni salvate:")
                        for k, v in list(configs.items())[:5]:
                            print(f"     {k}: {v}")

                    return security_found and database_found and system_found and reporting_found
                else:
                    print(f"   ❌ Errore lettura: {get_data.get('message', 'Unknown error')}")
                    return False
            else:
                print(f"   ❌ Errore HTTP lettura: {get_response.status_code}")
                return False
            
        else:
            print(f"   ❌ Errore salvataggio: {data.get('message', 'Unknown error')}")
            return False
    else:
        print(f"   ❌ Errore HTTP salvataggio: {save_response.status_code}")
        try:
            error_data = save_response.json()
            print(f"   Error details: {error_data}")
        except:
            print(f"   Response text: {save_response.text[:200]}...")
        return False

def test_individual_sections():
    """Test salvataggio sezioni individuali"""
    print("\n🔧 Testing Individual Section Save...")
    
    base_url = "http://localhost:8002"
    
    # Login
    session = requests.Session()
    login_response = session.post(f"{base_url}/login", data={
        "username": "<EMAIL>",
        "password": "AdminPassword123!"
    }, allow_redirects=True)
    
    if login_response.status_code != 200:
        print("   ❌ Login fallito")
        return False
    
    # Test sezioni individuali
    test_cases = [
        ("security", {"password_min_length": 6, "session_timeout": 30}),
        ("database", {"backup_schedule": "weekly", "backup_retention": 14}),
        ("system", {"debug_mode": "enabled", "log_level": "DEBUG"}),
        ("reporting", {"daily_report": "disabled", "export_format": "csv"})
    ]
    
    results = []
    
    for section_name, section_config in test_cases:
        print(f"   Testing {section_name} section...")
        
        config_data = {section_name: section_config}
        
        response = session.post(f"{base_url}/admin/api/configurations", 
            headers={'Content-Type': 'application/json'},
            data=json.dumps(config_data)
        )
        
        if response.status_code == 200:
            data = response.json()
            success = data.get("success", False)
            print(f"   {section_name}: {'✅' if success else '❌'}")
            results.append(success)
        else:
            print(f"   {section_name}: ❌ HTTP {response.status_code}")
            results.append(False)
    
    return all(results)

def main():
    """Esegue tutti i test"""
    print("🧪 TESTING CONFIGURATION SAVE FIXES")
    print("=" * 50)
    print(f"Timestamp: {datetime.now()}")
    print()
    
    results = []
    
    # Test 1: Salvataggio configurazioni complete
    try:
        result = test_config_save()
        results.append(("Complete Config Save", result))
    except Exception as e:
        print(f"❌ Errore test config save: {e}")
        results.append(("Complete Config Save", False))
    
    # Test 2: Salvataggio sezioni individuali
    try:
        result = test_individual_sections()
        results.append(("Individual Sections Save", result))
    except Exception as e:
        print(f"❌ Errore test individual sections: {e}")
        results.append(("Individual Sections Save", False))
    
    # Riepilogo
    print("\n" + "=" * 50)
    print("📊 RIEPILOGO TEST CONFIGURAZIONI")
    print("=" * 50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name:<30} {status}")
        if result:
            passed += 1
    
    print(f"\nRisultato: {passed}/{total} test passati")
    
    if passed == total:
        print("🎉 Tutti i test sono passati!")
        print("✅ Il salvataggio delle configurazioni database funziona!")
    else:
        print("⚠️ Alcuni test hanno fallito")
    
    print("\n🔧 COSA È STATO CORRETTO:")
    print("1. ✅ Aggiunto supporto per tutti i campi di configurazione")
    print("2. ✅ Creato save_database_config() per configurazioni DB")
    print("3. ✅ Creato save_reporting_config() per configurazioni report")
    print("4. ✅ Creato save_system_db_config() per configurazioni sistema")
    print("5. ✅ Corretto JavaScript per raccogliere tutti i campi")
    print("6. ✅ Aggiunto mapping completo per tutte le sezioni")

if __name__ == "__main__":
    main()
