#!/usr/bin/env python3
"""
Test specifico per le sezioni database, reporting e system
"""

import requests
import json
from datetime import datetime

def test_specific_sections():
    """Test salvataggio sezioni specifiche"""
    print("🔧 Testing Specific Sections Save...")
    
    base_url = "http://localhost:8000"
    
    # Login
    session = requests.Session()
    login_response = session.post(f"{base_url}/login", data={
        "username": "<EMAIL>",
        "password": "AdminPassword123!"
    }, allow_redirects=True)
    
    if login_response.status_code != 200:
        print(f"   ❌ Login fallito: {login_response.status_code}")
        return False
    
    print("   ✅ Login riuscito")
    
    # Test 1: Solo database
    print("\n1. Test salvataggio solo DATABASE...")
    database_config = {
        "database": {
            "backup_schedule": "daily",
            "backup_time": "02:00",
            "backup_retention": 30,
            "backup_path": "/backups/snip/",
            "compress_backup": True,
            "log_cleanup": "daily",
            "archive_months": 24,
            "optimize": "weekly",
            "auto_vacuum": True,
            "analyze": True,
            "disk_threshold": 85,
            "connection_threshold": 80,
            "monitor_performance": True,
            "alert_email": True
        }
    }
    
    response = session.post(f"{base_url}/admin/api/configurations", 
        headers={'Content-Type': 'application/json'},
        data=json.dumps(database_config)
    )
    
    print(f"   Status: {response.status_code}")
    if response.status_code == 200:
        data = response.json()
        print(f"   Success: {data.get('success')}")
        if not data.get('success'):
            print(f"   Error: {data.get('message')}")
    else:
        print(f"   Error: {response.text[:200]}...")
    
    # Test 2: Solo reporting
    print("\n2. Test salvataggio solo REPORTING...")
    reporting_config = {
        "reporting": {
            "daily_report": "enabled",
            "daily_report_time": "08:00",
            "weekly_report": "monday",
            "monthly_report": "first",
            "kpi_voyages": True,
            "kpi_sof": True,
            "kpi_users": True,
            "kpi_ports": True,
            "kpi_performance": True,
            "kpi_revenue": False,
            "export_format": "excel",
            "auto_export": True,
            "compress_exports": False,
            "report_recipients": "<EMAIL>",
            "include_charts": True,
            "detailed_reports": False
        }
    }
    
    response = session.post(f"{base_url}/admin/api/configurations", 
        headers={'Content-Type': 'application/json'},
        data=json.dumps(reporting_config)
    )
    
    print(f"   Status: {response.status_code}")
    if response.status_code == 200:
        data = response.json()
        print(f"   Success: {data.get('success')}")
        if not data.get('success'):
            print(f"   Error: {data.get('message')}")
    else:
        print(f"   Error: {response.text[:200]}...")
    
    # Test 3: Solo system
    print("\n3. Test salvataggio solo SYSTEM...")
    system_config = {
        "system": {
            "app_version": "v2.0.0",
            "debug_mode": "disabled",
            "log_level": "INFO",
            "max_upload_size": 50,
            "request_timeout": 30,
            "max_connections": 100,
            "cache_enabled": True,
            "compression_enabled": True,
            "maintenance_mode": "disabled",
            "maintenance_message": "Sistema in manutenzione test",
            "auto_updates": False
        }
    }
    
    response = session.post(f"{base_url}/admin/api/configurations", 
        headers={'Content-Type': 'application/json'},
        data=json.dumps(system_config)
    )
    
    print(f"   Status: {response.status_code}")
    if response.status_code == 200:
        data = response.json()
        print(f"   Success: {data.get('success')}")
        if not data.get('success'):
            print(f"   Error: {data.get('message')}")
    else:
        print(f"   Error: {response.text[:200]}...")
    
    # Test 4: Verifica database
    print("\n4. Verifica database dopo salvataggio...")
    from database import SessionLocal
    from sqlalchemy import text
    
    db = SessionLocal()
    try:
        result = db.execute(text("""
            SELECT config_key, config_value
            FROM "SYSTEM_CONFIG"
            WHERE config_key LIKE 'database_%' OR config_key LIKE 'reporting_%' OR config_key LIKE 'system_%'
            ORDER BY config_key
        """))
        
        configs = result.fetchall()
        print(f"   Configurazioni trovate: {len(configs)}")
        
        database_count = len([c for c in configs if c[0].startswith('database_')])
        reporting_count = len([c for c in configs if c[0].startswith('reporting_')])
        system_count = len([c for c in configs if c[0].startswith('system_')])
        
        print(f"   - Database: {database_count} configurazioni")
        print(f"   - Reporting: {reporting_count} configurazioni")
        print(f"   - System: {system_count} configurazioni")
        
        if configs:
            print("   Esempi:")
            for key, value in configs[:5]:
                print(f"     {key}: {value}")
        
    except Exception as e:
        print(f"   Errore verifica database: {e}")
    finally:
        db.close()
    
    return True

def main():
    print("🧪 TESTING SPECIFIC SECTIONS")
    print("=" * 50)
    print(f"Timestamp: {datetime.now()}")
    print()
    
    try:
        test_specific_sections()
    except Exception as e:
        print(f"❌ Errore: {e}")

if __name__ == "__main__":
    main()
