/**
 * JavaScript per la gestione della pagina SOF Archiviati
 */

// Variabili globali
let archivioData = [];
let currentFilename = null;

// Inizializzazione pagina
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 SOF Archiviati - Inizializzazione...');

    // Carica statistiche
    calcolaStatistiche();

    // Inizializza event listeners
    initEventListeners();

    // Verifica autenticazione per upload
    checkAuthenticationStatus();

    console.log('✅ SOF Archiviati - Inizializzazione completata');
});

/**
 * Inizializza gli event listeners
 */
function initEventListeners() {
    // Nascondi il pulsante "Scarica SOF" nel modal
    const btnScaricaSOFModal = document.getElementById('btnScaricaSOFModal');
    if (btnScaricaSOFModal) {
        btnScaricaSOFModal.style.display = 'none';
    }
}

/**
 * Calcola e aggiorna le statistiche
 */
function calcolaStatistiche() {
    const archiveItems = document.querySelectorAll('.archive-item');
    
    let totalNavi = new Set();
    let totalImport = 0;
    let totalExport = 0;
    
    archiveItems.forEach(item => {
        const nave = item.dataset.nave;
        const importRecords = parseInt(item.dataset.import) || 0;
        const exportRecords = parseInt(item.dataset.export) || 0;
        
        if (nave && nave !== 'N/A') {
            totalNavi.add(nave);
        }
        totalImport += importRecords;
        totalExport += exportRecords;
    });
    
    // Aggiorna i contatori
    const totalNaviElement = document.getElementById('totalNavi');
    const totalImportElement = document.getElementById('totalImport');
    const totalExportElement = document.getElementById('totalExport');
    
    if (totalNaviElement) totalNaviElement.textContent = totalNavi.size;
    if (totalImportElement) totalImportElement.textContent = totalImport.toLocaleString();
    if (totalExportElement) totalExportElement.textContent = totalExport.toLocaleString();
}

/**
 * Filtra i file per data
 */
function filtraPerData() {
    const dataInizio = document.getElementById('dataInizio').value;
    const dataFine = document.getElementById('dataFine').value;

    if (!dataInizio || !dataFine) {
        alert('Seleziona entrambe le date per filtrare');
        return;
    }

    const startDate = new Date(dataInizio);
    const endDate = new Date(dataFine);

    if (startDate > endDate) {
        alert('La data di inizio deve essere precedente alla data di fine');
        return;
    }

    console.log(`🔍 Filtro per data: ${dataInizio} - ${dataFine}`);

    const archiveItems = document.querySelectorAll('.archive-item');
    let visibleCount = 0;

    archiveItems.forEach(item => {
        const itemDate = new Date(item.dataset.date);

        if (itemDate >= startDate && itemDate <= endDate) {
            item.style.display = 'block';
            visibleCount++;
        } else {
            item.style.display = 'none';
        }
    });

    // Aggiorna contatore
    const badge = document.querySelector('.badge.bg-info');
    if (badge) {
        badge.textContent = `${visibleCount} file`;
    }

    // Mostra le visualizzazioni quando si applica un filtro
    toggleVisualizationsVisibility(true);

    // Forza re-layout delle card per allineamento corretto
    forceCardReLayout();

    // Ricalcola statistiche per i file visibili
    calcolaStatisticheFiltrate();
}

/**
 * Reset filtri e nasconde tutte le visualizzazioni
 */
function resetFiltri() {
    const archiveItems = document.querySelectorAll('.archive-item');

    // Mostra tutti gli elementi
    archiveItems.forEach(item => {
        item.style.display = 'block';
    });

    // Reset contatore
    const badge = document.querySelector('.badge.bg-info');
    if (badge) {
        const totalItems = archiveItems.length;
        badge.textContent = `${totalItems} file`;
    }

    // Reset campi data ai valori originali
    const dataInizio = document.getElementById('dataInizio');
    const dataFine = document.getElementById('dataFine');
    if (dataInizio && dataFine) {
        // Ripristina ai valori min/max originali
        dataInizio.value = dataInizio.getAttribute('value');
        dataFine.value = dataFine.getAttribute('value');
    }

    // Nasconde tutte le visualizzazioni sotto usando la funzione dedicata
    toggleVisualizationsVisibility(false);

    // Reset upload results se presenti
    const uploadResults = document.getElementById('uploadResults');
    if (uploadResults) {
        uploadResults.style.display = 'none';
        uploadResults.innerHTML = '';
    }

    // Ricalcola statistiche complete (ma non le mostra)
    calcolaStatistiche();

    console.log('🔄 Filtri resettati e visualizzazioni nascoste');
}

/**
 * Calcola statistiche per i file filtrati
 */
function calcolaStatisticheFiltrate() {
    const visibleItems = document.querySelectorAll('.archive-item[style="display: block"], .archive-item:not([style*="display: none"])');
    
    let totalNavi = new Set();
    let totalImport = 0;
    let totalExport = 0;
    
    visibleItems.forEach(item => {
        if (item.style.display !== 'none') {
            const nave = item.dataset.nave;
            const importRecords = parseInt(item.dataset.import) || 0;
            const exportRecords = parseInt(item.dataset.export) || 0;
            
            if (nave && nave !== 'N/A') {
                totalNavi.add(nave);
            }
            totalImport += importRecords;
            totalExport += exportRecords;
        }
    });
    
    // Aggiorna i contatori
    const totalNaviElement = document.getElementById('totalNavi');
    const totalImportElement = document.getElementById('totalImport');
    const totalExportElement = document.getElementById('totalExport');
    
    if (totalNaviElement) totalNaviElement.textContent = totalNavi.size;
    if (totalImportElement) totalImportElement.textContent = totalImport.toLocaleString();
    if (totalExportElement) totalExportElement.textContent = totalExport.toLocaleString();
}

/**
 * Visualizza i dettagli di un SOF archiviato
 */
window.visualizzaDettagli = async function(filename) {
    try {
        console.log(`📋 Caricamento dettagli per: ${filename}`);
        
        // Salva il filename corrente
        currentFilename = filename;
        
        // Mostra loading
        const modalContent = document.getElementById('modalDettagliContent');
        modalContent.innerHTML = `
            <div class="text-center py-4">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Caricamento...</span>
                </div>
                <div class="mt-2">Caricamento dettagli SOF...</div>
            </div>
        `;
        
        // Apri modal
        const modal = new bootstrap.Modal(document.getElementById('modalDettagliSOF'));
        modal.show();
        
        // Carica dettagli via API
        const response = await fetch(`/api/sof/archiviati/${encodeURIComponent(filename)}/dettagli`);
        
        if (!response.ok) {
            throw new Error(`Errore HTTP: ${response.status}`);
        }
        
        const data = await response.json();
        
        if (data.success) {
            // Determina il numero di viaggi per il titolo del modal
            let numViaggi = 1; // Default per un singolo viaggio
            
            // Se ci sono dati orari, conta i viaggi unici basandosi sui codici viaggio
            if (data.data.orari && Array.isArray(data.data.orari)) {
                // Conta i codici viaggio unici
                const codiciViaggio = new Set();
                data.data.orari.forEach(orario => {
                    if (orario.codice_viaggio) {
                        codiciViaggio.add(orario.codice_viaggio);
                    }
                });
                numViaggi = codiciViaggio.size > 0 ? codiciViaggio.size : data.data.orari.length;
            }
            
            // Aggiorna il titolo del modal
            const modalTitle = document.getElementById('modalDettagliTitle');
            if (modalTitle) {
                if (numViaggi > 1) {
                    modalTitle.innerHTML = `
                        <i class="fas fa-info-circle me-2"></i>
                        Dettagli SOF Archiviati
                    `;
                } else {
                    modalTitle.innerHTML = `
                        <i class="fas fa-info-circle me-2"></i>
                        Dettagli SOF Archiviato
                    `;
                }
            }
            
            // Mostra dettagli
            modalContent.innerHTML = generaHTMLDettagli(data.data);
        } else {
            throw new Error(data.message || 'Errore sconosciuto');
        }
        
    } catch (error) {
        console.error('❌ Errore caricamento dettagli:', error);
        
        const modalContent = document.getElementById('modalDettagliContent');
        modalContent.innerHTML = `
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle me-2"></i>
                Errore nel caricamento dei dettagli: ${error.message}
            </div>
        `;
    }
}

/**
 * Visualizza i dettagli di un file JSON archiviato
 */
window.visualizzaDettagliJSON = async function(filename) {
    try {
        console.log('📋 Visualizza dettagli JSON:', filename);

        // Apri il modal
        const modal = new bootstrap.Modal(document.getElementById('modalDettagliSOF'));
        modal.show();

        // Aggiorna il titolo
        const modalTitle = document.getElementById('modalDettagliTitle');
        if (modalTitle) {
            modalTitle.innerHTML = `
                <i class="fas fa-info-circle me-2"></i>
                Dettagli SOF Archiviato - ${filename}
            `;
        }

        // Mostra loading
        const modalContent = document.getElementById('modalDettagliContent');
        modalContent.innerHTML = `
            <div class="text-center py-4">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Caricamento...</span>
                </div>
                <div class="mt-2">Caricamento dettagli JSON...</div>
            </div>
        `;

        const response = await fetch(`/api/operativo/sof/archivio_old/${encodeURIComponent(filename)}/viaggi`);
        if (!response.ok) {
            throw new Error(`Errore nel recupero dei dettagli: ${response.statusText}`);
        }
        const data = await response.json();
        if (!data.success) {
            throw new Error(data.message || 'Errore sconosciuto');
        }
        const viaggi = data.viaggi;

        if (viaggi.length === 0) {
            modalContent.innerHTML = '<p>Nessun viaggio trovato nel file JSON.</p>';
        } else {
            let tableHtml = `
                <div class="table-responsive">
                    <table class="table table-striped table-bordered">
                        <thead>
                            <tr>
                                <th>Codice Viaggio</th>
                                <th>Data Arrivo</th>
                                <th>Data Partenza</th>
                                <th>Azioni</th>
                            </tr>
                        </thead>
                        <tbody>
            `;

            viaggi.forEach(viaggio => {
                const viaggioData = viaggio.viaggio_data || {};
                const codiceViaggio = viaggioData.codice_viaggio || 'N/A';
                const dataArrivo = formatDate(viaggioData.data_arrivo) || 'N/A';
                const dataPartenza = formatDate(viaggioData.data_partenza) || 'N/A';
                
                tableHtml += `
                    <tr>
                        <td><strong>${codiceViaggio}</strong></td>
                        <td>${dataArrivo}</td>
                        <td>${dataPartenza}</td>
                        <td>
                            <button class="btn btn-sm btn-primary" onclick="visualizzaDettagliViaggio(${viaggio.id_viaggio})">
                                <i class="fas fa-eye me-1"></i> Dettagli
                            </button>
                        </td>
                    </tr>
                `;
            });

            tableHtml += '</tbody></table></div>';
            modalContent.innerHTML = tableHtml;
        }
    } catch (error) {
        console.error('❌ Errore caricamento dettagli JSON:', error);
        const modalContent = document.getElementById('modalDettagliContent');
        modalContent.innerHTML = `
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle me-2"></i>
                Errore nel caricamento dei dettagli: ${error.message}
            </div>
        `;
    }
}

/**
 * Genera HTML per i dettagli del SOF
 */
function generaHTMLDettagli(data) {
    // Debug logging per capire la struttura dei dati
    console.log('🔍 Dati ricevuti per dettagli:', data);

    const viaggio = data.viaggio || {};
    const metadata = data.metadata || {};

    // Gestisci struttura import_data: {total_records: X, records: [...]}
    let importData = [];
    if (data.import_data) {
        if (Array.isArray(data.import_data)) {
            importData = data.import_data;
        } else if (data.import_data.records && Array.isArray(data.import_data.records)) {
            importData = data.import_data.records;
        }
    }

    // Gestisci struttura export_data: {total_records: X, records: [...]}
    let exportData = [];
    if (data.export_data) {
        if (Array.isArray(data.export_data)) {
            exportData = data.export_data;
        } else if (data.export_data.records && Array.isArray(data.export_data.records)) {
            exportData = data.export_data.records;
        }
    }

    // Gestisci orari: può essere oggetto o array
    let orariArray = [];
    if (data.orari) {
        if (Array.isArray(data.orari)) {
            orariArray = data.orari;
        } else if (typeof data.orari === 'object') {
            // Converti oggetto orari in array con un singolo elemento
            orariArray = [data.orari];
        }
    }

    // Ottieni mappatura porti ATLAS
    const atlasMapping = data.atlas_mapping || {};
    console.log('🗺️ Mappatura ATLAS caricata:', Object.keys(atlasMapping).length, 'porti');

    // Debug specifico per i dati processati
    console.log('📊 Orari processati:', orariArray, 'Lunghezza:', orariArray.length);
    console.log('📥 Import processati:', importData, 'Lunghezza:', importData.length);
    console.log('📤 Export processati:', exportData, 'Lunghezza:', exportData.length);
    
    return `
        <div class="row">
            <!-- Informazioni Viaggio -->
            <div class="col-md-6">
                <div class="card mb-3">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-ship me-2"></i>
                            Informazioni Viaggio
                        </h6>
                    </div>
                    <div class="card-body">
                        <table class="table table-sm">
                            <tr><td><strong>Codice Viaggio:</strong></td><td>${viaggio.codice_viaggio || 'N/A'}</td></tr>
                            <tr><td><strong>Data Arrivo:</strong></td><td>${formatDate(viaggio.data_arrivo)}</td></tr>
                            <tr><td><strong>Data Partenza:</strong></td><td>${formatDate(viaggio.data_partenza)}</td></tr>
                        </table>
                    </div>
                </div>
            </div>
            
            <!-- Metadata Archivio -->
            <div class="col-md-6">
                <div class="card mb-3">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-archive me-2"></i>
                            Informazioni Archivio
                        </h6>
                    </div>
                    <div class="card-body">
                        <table class="table table-sm">
                            <tr><td><strong>Archiviato il:</strong></td><td>${formatDateTime(metadata.archiviato_il)}</td></tr>
                            <tr><td><strong>Archiviato da:</strong></td><td>${metadata.archiviato_da || 'N/A'}</td></tr>
                            <tr><td><strong>Nome File:</strong></td><td>${metadata.filename || 'N/A'}</td></tr>
                            <tr><td><strong>Record Import:</strong></td><td>${importData.length}</td></tr>
                            <tr><td><strong>Record Export:</strong></td><td>${exportData.length}</td></tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Debug info mappatura ATLAS -->
        ${Object.keys(atlasMapping).length > 0 ?
            `<div class="alert alert-info alert-sm mb-3">
                <i class="fas fa-info-circle me-2"></i>
                <small>Mappatura ATLAS caricata: ${Object.keys(atlasMapping).length} porti disponibili</small>
            </div>` :
            `<div class="alert alert-warning alert-sm mb-3">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <small>Attenzione: Mappatura ATLAS non disponibile - verranno mostrati i codici porti</small>
            </div>`
        }

        <!-- Tab Navigation -->
        <ul class="nav nav-tabs" id="dettagliTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="orari-tab" data-bs-toggle="tab" data-bs-target="#orari" type="button" role="tab">
                    <i class="fas fa-clock me-1"></i>
                    Orari Completi (${orariArray.length})
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="carburanti-tab" data-bs-toggle="tab" data-bs-target="#carburanti" type="button" role="tab">
                    <i class="fas fa-gas-pump me-1"></i>
                    Carburanti
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="import-tab" data-bs-toggle="tab" data-bs-target="#import" type="button" role="tab">
                    <i class="fas fa-download me-1"></i>
                    Import (${importData.length})
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="export-tab" data-bs-toggle="tab" data-bs-target="#export" type="button" role="tab">
                    <i class="fas fa-upload me-1"></i>
                    Export (${exportData.length})
                </button>
            </li>
        </ul>

        <!-- Tab Content -->
        <div class="tab-content mt-3" id="dettagliTabsContent">
            <!-- Orari Completi -->
            <div class="tab-pane fade show active" id="orari" role="tabpanel">
                ${generaTabellaOrariCompleti(orariArray, atlasMapping)}
            </div>

            <!-- Carburanti -->
            <div class="tab-pane fade" id="carburanti" role="tabpanel">
                ${generaTabellaCarburanti(orariArray, atlasMapping)}
            </div>

            <!-- Import -->
            <div class="tab-pane fade" id="import" role="tabpanel">
                ${generaTabellaImport(importData)}
            </div>

            <!-- Export -->
            <div class="tab-pane fade" id="export" role="tabpanel">
                ${generaTabellaExport(exportData)}
            </div>
        </div>
    `;
}

/**
 * Genera tabella orari completi con mappatura nomi porti
 */
function generaTabellaOrariCompleti(orari, atlasMapping = {}) {
    // Verifica che orari sia un array
    if (!orari || !Array.isArray(orari) || orari.length === 0) {
        console.warn('Orari non è un array valido:', orari);
        return '<div class="alert alert-info">Nessun orario disponibile</div>';
    }

    let html = `
        <div class="table-responsive">
            <table class="table table-striped table-sm">
                <thead class="table-dark">
                    <tr>
                        <th>Nome Nave</th>
                        <th>Porto Gestione</th>
                        <th>Porto Arrivo</th>
                        <th>SBE</th>
                        <th>Pilota Arrivo</th>
                        <th>All Fast</th>
                        <th>Tug Arrivo</th>
                        <th>Draft (m)</th>
                        <th>SOC</th>
                        <th>Porto Destinazione</th>
                        <th>Pilota Partenza</th>
                        <th>Tug Partenza</th>
                        <th>FOC</th>
                    </tr>
                </thead>
                <tbody>
    `;

    try {
        orari.forEach(orario => {
            // Ottieni codici porti
            const porto_arrivo_codice = orario.porto_arrivo || orario.porto || 'N/A';
            const porto_destinazione_codice = orario.porto_di_destinazione || orario.porto_destinazione || 'N/A';

            // Converti codici in nomi usando mappatura ATLAS
            const porto_arrivo_nome = atlasMapping[porto_arrivo_codice] || porto_arrivo_codice;
            const porto_destinazione_nome = atlasMapping[porto_destinazione_codice] || porto_destinazione_codice;

            // Log conversioni per debug
            if (porto_arrivo_codice !== 'N/A' && atlasMapping[porto_arrivo_codice]) {
                console.log(`🚢 Porto arrivo: ${porto_arrivo_codice} → ${porto_arrivo_nome}`);
            }
            if (porto_destinazione_codice !== 'N/A' && atlasMapping[porto_destinazione_codice]) {
                console.log(`🎯 Porto destinazione: ${porto_destinazione_codice} → ${porto_destinazione_nome}`);
            }

            // Ottieni nome nave e porto gestione (che variano a seconda del viaggio)
            const nome_nave = orario.nome_nave || 'N/A';
            const porto_gestione = orario.porto_gestione || 'N/A';

            // Altri campi
            const sbe = orario.sbe || 'N/A';
            const pilota_arrivo = orario.pilota_arrivo || 'N/A';
            const all_fast = orario.all_fast || 'N/A';
            const tug_arrivo = orario.tug_arrivo !== undefined ? orario.tug_arrivo : 'N/A';
            const draft = orario.draft !== undefined ? `${orario.draft}m` : 'N/A';
            const soc = orario.soc || 'N/A';
            const pilota_partenza = orario.pilota_partenza || 'N/A';
            const tug_partenza = orario.tug_partenza !== undefined ? orario.tug_partenza : 'N/A';
            const foc = orario.foc || 'N/A';

            html += `
                <tr>
                    <td><strong>${nome_nave}</strong></td>
                    <td><strong>${porto_gestione}</strong></td>
                    <td><strong>${porto_arrivo_nome}</strong>
                        ${porto_arrivo_codice !== porto_arrivo_nome ? `<br><small class="text-muted">(${porto_arrivo_codice})</small>` : ''}
                    </td>
                    <td>${formatDateTime(sbe)}</td>
                    <td>${formatDateTime(pilota_arrivo)}</td>
                    <td>${formatDateTime(all_fast)}</td>
                    <td class="text-center">${tug_arrivo}</td>
                    <td class="text-center">${draft}</td>
                    <td>${formatDateTime(soc)}</td>
                    <td><strong>${porto_destinazione_nome}</strong>
                        ${porto_destinazione_codice !== porto_destinazione_nome ? `<br><small class="text-muted">(${porto_destinazione_codice})</small>` : ''}
                    </td>
                    <td>${formatDateTime(pilota_partenza)}</td>
                    <td class="text-center">${tug_partenza}</td>
                    <td>${formatDateTime(foc)}</td>
                </tr>
            `;
        });
    } catch (error) {
        console.error('Errore generazione tabella orari completi:', error);
        return '<div class="alert alert-danger">Errore nella visualizzazione degli orari completi</div>';
    }

    html += '</tbody></table></div>';
    return html;
}

/**
 * Genera tabella carburanti con mappatura nomi porti
 */
function generaTabellaCarburanti(orari, atlasMapping = {}) {
    // Verifica che orari sia un array
    if (!orari || !Array.isArray(orari) || orari.length === 0) {
        console.warn('Orari non è un array valido per carburanti:', orari);
        return '<div class="alert alert-info">Nessun dato carburante disponibile</div>';
    }

    // Filtra solo gli orari che hanno dati carburanti
    const orariConCarburanti = orari.filter(orario =>
        orario.fo !== undefined || orario.do !== undefined || orario.lo !== undefined
    );

    if (orariConCarburanti.length === 0) {
        return '<div class="alert alert-info">Nessun dato carburante disponibile</div>';
    }

    let html = `
        <div class="table-responsive">
            <table class="table table-striped table-sm">
                <thead class="table-success">
                    <tr>
                        <th>Nome Nave</th>
                        <th>Porto Gestione</th>
                        <th>Porto</th>
                        <th>FO (Fuel Oil)</th>
                        <th>DO (Diesel Oil)</th>
                        <th>LO (Lube Oil)</th>
                        <th>Totale Carburante</th>
                    </tr>
                </thead>
                <tbody>
    `;

    try {
        let totaleFO = 0;
        let totaleDO = 0;
        let totaleLO = 0;

        orariConCarburanti.forEach(orario => {
            // Ottieni codice porto e convertilo in nome
            const porto_codice = orario.porto_arrivo || orario.porto || 'Porto';
            const porto_nome = atlasMapping[porto_codice] || porto_codice;

            // Ottieni nome nave e porto gestione
            const nome_nave = orario.nome_nave || 'N/A';
            const porto_gestione = orario.porto_gestione || 'N/A';

            const fo = orario.fo !== undefined ? parseFloat(orario.fo) : 0;
            const do_val = orario.do !== undefined ? parseFloat(orario.do) : 0;
            const lo = orario.lo !== undefined ? parseFloat(orario.lo) : 0;
            const totale = fo + do_val + lo;

            totaleFO += fo;
            totaleDO += do_val;
            totaleLO += lo;

            html += `
                <tr>
                    <td><strong>${nome_nave}</strong></td>
                    <td><strong>${porto_gestione}</strong></td>
                    <td><strong>${porto_nome}</strong>
                        ${porto_codice !== porto_nome ? `<br><small class="text-muted">(${porto_codice})</small>` : ''}
                    </td>
                    <td class="text-end">${fo > 0 ? fo.toFixed(1) + ' L' : '-'}</td>
                    <td class="text-end">${do_val > 0 ? do_val.toFixed(1) + ' L' : '-'}</td>
                    <td class="text-end">${lo > 0 ? lo.toFixed(1) + ' L' : '-'}</td>
                    <td class="text-end"><strong>${totale > 0 ? totale.toFixed(1) + ' L' : '-'}</strong></td>
                </tr>
            `;
        });

        // Aggiungi riga totali
        const totaleTotale = totaleFO + totaleDO + totaleLO;
        html += `
            <tr class="table-warning">
                <td colspan="3"><strong>TOTALI</strong></td>
                <td class="text-end"><strong>${totaleFO > 0 ? totaleFO.toFixed(1) + ' L' : '-'}</strong></td>
                <td class="text-end"><strong>${totaleDO > 0 ? totaleDO.toFixed(1) + ' L' : '-'}</strong></td>
                <td class="text-end"><strong>${totaleLO > 0 ? totaleLO.toFixed(1) + ' L' : '-'}</strong></td>
                <td class="text-end"><strong>${totaleTotale > 0 ? totaleTotale.toFixed(1) + ' L' : '-'}</strong></td>
            </tr>
        `;

    } catch (error) {
        console.error('Errore generazione tabella carburanti:', error);
        return '<div class="alert alert-danger">Errore nella visualizzazione dei carburanti</div>';
    }

    html += '</tbody></table></div>';
    return html;
}

/**
 * Genera tabella import
 */
function generaTabellaImport(importData) {
    if (!importData || !Array.isArray(importData) || importData.length === 0) {
        return '<div class="alert alert-info">Nessun dato import disponibile</div>';
    }

    let html = `
        <div class="table-responsive">
            <table class="table table-striped table-sm">
                <thead class="table-primary">
                    <tr>
                        <th>Tipo</th>
                        <th>Descrizione</th>
                        <th>Quantità</th>
                        <th>Unità</th>
                        <th>Note</th>
                    </tr>
                </thead>
                <tbody>
    `;

    try {
        importData.forEach(item => {
            html += `
                <tr>
                    <td>${item.tipo || 'N/A'}</td>
                    <td>${item.descrizione || 'N/A'}</td>
                    <td class="text-end">${item.quantita || 'N/A'}</td>
                    <td>${item.unita || 'N/A'}</td>
                    <td>${item.note || '-'}</td>
                </tr>
            `;
        });
    } catch (error) {
        console.error('Errore generazione tabella import:', error);
        return '<div class="alert alert-danger">Errore nella visualizzazione dei dati import</div>';
    }

    html += '</tbody></table></div>';
    return html;
}

/**
 * Genera tabella export
 */
function generaTabellaExport(exportData) {
    if (!exportData || !Array.isArray(exportData) || exportData.length === 0) {
        return '<div class="alert alert-info">Nessun dato export disponibile</div>';
    }

    let html = `
        <div class="table-responsive">
            <table class="table table-striped table-sm">
                <thead class="table-warning">
                    <tr>
                        <th>Tipo</th>
                        <th>Descrizione</th>
                        <th>Quantità</th>
                        <th>Unità</th>
                        <th>Note</th>
                    </tr>
                </thead>
                <tbody>
    `;

    try {
        exportData.forEach(item => {
            html += `
                <tr>
                    <td>${item.tipo || 'N/A'}</td>
                    <td>${item.descrizione || 'N/A'}</td>
                    <td class="text-end">${item.quantita || 'N/A'}</td>
                    <td>${item.unita || 'N/A'}</td>
                    <td>${item.note || '-'}</td>
                </tr>
            `;
        });
    } catch (error) {
        console.error('Errore generazione tabella export:', error);
        return '<div class="alert alert-danger">Errore nella visualizzazione dei dati export</div>';
    }

    html += '</tbody></table></div>';
    return html;
}

/**
 * Formatta una data
 */
function formatDate(dateString) {
    if (!dateString) return 'N/A';
    try {
        const date = new Date(dateString);
        return date.toLocaleDateString('it-IT');
    } catch (error) {
        return dateString;
    }
}

/**
 * Formatta data e ora
 */
function formatDateTime(dateTimeString) {
    if (!dateTimeString) return 'N/A';
    try {
        const date = new Date(dateTimeString);
        return date.toLocaleString('it-IT');
    } catch (error) {
        return dateTimeString;
    }
}

/**
 * Verifica stato autenticazione per upload
 */
function checkAuthenticationStatus() {
    // Placeholder per verifica autenticazione
    console.log('🔐 Verifica stato autenticazione...');
}

/**
 * Mostra/nasconde visualizzazioni
 */
function toggleVisualizationsVisibility(show) {
    // Placeholder per toggle visualizzazioni
    console.log(`👁️ Toggle visualizzazioni: ${show ? 'mostra' : 'nascondi'}`);
}

/**
 * Forza re-layout delle card
 */
function forceCardReLayout() {
    // Placeholder per re-layout
    console.log('🔄 Forza re-layout delle card');
}

/**
 * Visualizza dettagli viaggio completi da archivio JSON
 */
window.visualizzaDettagliViaggio = async function(idViaggio) {
    try {
        console.log(`🚢 Caricamento dettagli viaggio: ${idViaggio}`);
        
        // Mostra loading nel modal
        const modalContent = document.getElementById('modalDettagliContent');
        modalContent.innerHTML = `
            <div class="text-center py-4">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Caricamento...</span>
                </div>
                <div class="mt-2">Caricamento dettagli viaggio...</div>
            </div>
        `;
        
        // Carica dettagli via API
        const response = await fetch(`/api/operativo/sof/archiviati/viaggio/${idViaggio}/dati`);
        
        if (!response.ok) {
            throw new Error(`Errore HTTP: ${response.status}`);
        }
        
        const result = await response.json();
        
        if (result.success) {
            const viaggio = result.viaggio;
            
            // Aggiorna il titolo del modal
            const modalTitle = document.getElementById('modalDettagliTitle');
            if (modalTitle) {
                modalTitle.innerHTML = `
                    <i class="fas fa-ship me-2"></i>
                    Dettagli Viaggio: ${viaggio.viaggio_data?.codice_viaggio || viaggio.viaggio_id}
                `;
            }
            
            // Genera HTML con tutti i dettagli del viaggio
            modalContent.innerHTML = generaHTMLDettagliViaggio(viaggio);
        } else {
            throw new Error(result.message || 'Errore sconosciuto');
        }
        
    } catch (error) {
        console.error('❌ Errore caricamento dettagli viaggio:', error);
        
        const modalContent = document.getElementById('modalDettagliContent');
        modalContent.innerHTML = `
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle me-2"></i>
                Errore nel caricamento dei dettagli: ${error.message}
            </div>
        `;
    }
}

/**
 * Genera HTML per i dettagli completi del viaggio
 */
function generaHTMLDettagliViaggio(viaggio) {
    const viaggioData = viaggio.viaggio_data || {};
    const orariData = viaggio.orari_data || [];
    const importData = viaggio.import_data || [];
    const exportData = viaggio.export_data || [];
    
    // Informazioni principali del viaggio
    const nomeNave = viaggioData.nome_nave || 'N/A';
    const portoGestione = viaggioData.porto_gestione || 'N/A';
    const codiceViaggio = viaggioData.codice_viaggio || 'N/A';
    const dataArrivo = formatDate(viaggioData.data_arrivo);
    const dataPartenza = formatDate(viaggioData.data_partenza);
    
    return `
        <div class="row">
            <!-- Informazioni Viaggio -->
            <div class="col-md-6">
                <div class="card mb-3">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-ship me-2"></i>
                            Informazioni Viaggio
                        </h6>
                    </div>
                    <div class="card-body">
                        <table class="table table-sm">
                            <tr><td><strong>Nome Nave:</strong></td><td>${nomeNave}</td></tr>
                            <tr><td><strong>Porto Gestione:</strong></td><td>${portoGestione}</td></tr>
                            <tr><td><strong>Codice Viaggio:</strong></td><td>${codiceViaggio}</td></tr>
                            <tr><td><strong>Data Arrivo:</strong></td><td>${dataArrivo}</td></tr>
                            <tr><td><strong>Data Partenza:</strong></td><td>${dataPartenza}</td></tr>
                        </table>
                    </div>
                </div>
            </div>
            
            <!-- Statistiche -->
            <div class="col-md-6">
                <div class="card mb-3">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-chart-bar me-2"></i>
                            Statistiche
                        </h6>
                    </div>
                    <div class="card-body">
                        <table class="table table-sm">
                            <tr><td><strong>Record Orari:</strong></td><td>${orariData.length}</td></tr>
                            <tr><td><strong>Record Import:</strong></td><td>${importData.length}</td></tr>
                            <tr><td><strong>Record Export:</strong></td><td>${exportData.length}</td></tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Tab Navigation -->
        <ul class="nav nav-tabs" id="viaggioDettagliTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="viaggio-orari-tab" data-bs-toggle="tab" data-bs-target="#viaggio-orari" type="button" role="tab">
                    <i class="fas fa-clock me-1"></i>
                    Orari (${orariData.length})
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="viaggio-import-tab" data-bs-toggle="tab" data-bs-target="#viaggio-import" type="button" role="tab">
                    <i class="fas fa-download me-1"></i>
                    Import (${importData.length})
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="viaggio-export-tab" data-bs-toggle="tab" data-bs-target="#viaggio-export" type="button" role="tab">
                    <i class="fas fa-upload me-1"></i>
                    Export (${exportData.length})
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="viaggio-raw-tab" data-bs-toggle="tab" data-bs-target="#viaggio-raw" type="button" role="tab">
                    <i class="fas fa-code me-1"></i>
                    Dati Raw
                </button>
            </li>
        </ul>

        <!-- Tab Content -->
        <div class="tab-content mt-3" id="viaggioDettagliTabsContent">
            <!-- Orari -->
            <div class="tab-pane fade show active" id="viaggio-orari" role="tabpanel">
                ${generaTabellaOrariViaggio(orariData, nomeNave, portoGestione)}
            </div>

            <!-- Import -->
            <div class="tab-pane fade" id="viaggio-import" role="tabpanel">
                ${generaTabellaImportViaggio(importData, nomeNave, portoGestione)}
            </div>

            <!-- Export -->
            <div class="tab-pane fade" id="viaggio-export" role="tabpanel">
                ${generaTabellaExportViaggio(exportData, nomeNave, portoGestione)}
            </div>

            <!-- Dati Raw -->
            <div class="tab-pane fade" id="viaggio-raw" role="tabpanel">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-code me-2"></i>
                            Dati JSON Completi
                        </h6>
                    </div>
                    <div class="card-body">
                        <pre class="bg-light p-3 rounded" style="max-height: 400px; overflow-y: auto; font-size: 0.8rem;">${JSON.stringify(viaggio, null, 2)}</pre>
                    </div>
                </div>
            </div>
        </div>
    `;
}

/**
 * Genera tabella orari per singolo viaggio
 */
function generaTabellaOrariViaggio(orariData, nomeNave, portoGestione) {
    if (!orariData || !Array.isArray(orariData) || orariData.length === 0) {
        return '<div class="alert alert-info">Nessun orario disponibile per questo viaggio</div>';
    }

    let html = `
        <div class="table-responsive">
            <table class="table table-striped table-sm">
                <thead class="table-dark">
                    <tr>
                        <th>Nome Nave</th>
                        <th>Porto Gestione</th>
                        <th>Porto Arrivo</th>
                        <th>SBE</th>
                        <th>Pilota Arrivo</th>
                        <th>All Fast</th>
                        <th>SOC</th>
                        <th>Porto Destinazione</th>
                        <th>Pilota Partenza</th>
                        <th>FOC</th>
                    </tr>
                </thead>
                <tbody>
    `;

    try {
        orariData.forEach(orario => {
            html += `
                <tr>
                    <td><strong>${nomeNave}</strong></td>
                    <td><strong>${portoGestione}</strong></td>
                    <td>${orario.porto_arrivo || 'N/A'}</td>
                    <td>${formatDateTime(orario.sbe)}</td>
                    <td>${formatDateTime(orario.pilota_arrivo)}</td>
                    <td>${formatDateTime(orario.all_fast)}</td>
                    <td>${formatDateTime(orario.soc)}</td>
                    <td>${orario.porto_di_destinazione || 'N/A'}</td>
                    <td>${formatDateTime(orario.pilota_partenza)}</td>
                    <td>${formatDateTime(orario.foc)}</td>
                </tr>
            `;
        });
    } catch (error) {
        console.error('Errore generazione tabella orari viaggio:', error);
        return '<div class="alert alert-danger">Errore nella visualizzazione degli orari</div>';
    }

    html += '</tbody></table></div>';
    return html;
}

/**
 * Genera tabella import per singolo viaggio
 */
function generaTabellaImportViaggio(importData, nomeNave, portoGestione) {
    if (!importData || !Array.isArray(importData) || importData.length === 0) {
        return '<div class="alert alert-info">Nessun dato import disponibile per questo viaggio</div>';
    }

    let html = `
        <div class="table-responsive">
            <table class="table table-striped table-sm">
                <thead class="table-primary">
                    <tr>
                        <th>Nome Nave</th>
                        <th>Porto Gestione</th>
                        <th>Tipo</th>
                        <th>Descrizione</th>
                        <th>Quantità</th>
                        <th>Unità</th>
                        <th>Note</th>
                    </tr>
                </thead>
                <tbody>
    `;

    try {
        importData.forEach(item => {
            html += `
                <tr>
                    <td><strong>${nomeNave}</strong></td>
                    <td><strong>${portoGestione}</strong></td>
                    <td>${item.tipo || 'N/A'}</td>
                    <td>${item.descrizione || 'N/A'}</td>
                    <td class="text-end">${item.quantita || 'N/A'}</td>
                    <td>${item.unita || 'N/A'}</td>
                    <td>${item.note || '-'}</td>
                </tr>
            `;
        });
    } catch (error) {
        console.error('Errore generazione tabella import viaggio:', error);
        return '<div class="alert alert-danger">Errore nella visualizzazione dei dati import</div>';
    }

    html += '</tbody></table></div>';
    return html;
}

/**
 * Genera tabella export per singolo viaggio
 */
function generaTabellaExportViaggio(exportData, nomeNave, portoGestione) {
    if (!exportData || !Array.isArray(exportData) || exportData.length === 0) {
        return '<div class="alert alert-info">Nessun dato export disponibile per questo viaggio</div>';
    }

    let html = `
        <div class="table-responsive">
            <table class="table table-striped table-sm">
                <thead class="table-warning">
                    <tr>
                        <th>Nome Nave</th>
                        <th>Porto Gestione</th>
                        <th>Tipo</th>
                        <th>Descrizione</th>
                        <th>Quantità</th>
                        <th>Unità</th>
                        <th>Note</th>
                    </tr>
                </thead>
                <tbody>
    `;

    try {
        exportData.forEach(item => {
            html += `
                <tr>
                    <td><strong>${nomeNave}</strong></td>
                    <td><strong>${portoGestione}</strong></td>
                    <td>${item.tipo || 'N/A'}</td>
                    <td>${item.descrizione || 'N/A'}</td>
                    <td class="text-end">${item.quantita || 'N/A'}</td>
                    <td>${item.unita || 'N/A'}</td>
                    <td>${item.note || '-'}</td>
                </tr>
            `;
        });
    } catch (error) {
        console.error('Errore generazione tabella export viaggio:', error);
        return '<div class="alert alert-danger">Errore nella visualizzazione dei dati export</div>';
    }

    html += '</tbody></table></div>';
    return html;
}
