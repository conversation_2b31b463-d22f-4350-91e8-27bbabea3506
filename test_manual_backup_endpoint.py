#!/usr/bin/env python3
"""
Test endpoint backup manuale
"""

import requests
import json

def test_manual_backup_endpoint():
    print("TEST ENDPOINT BACKUP MANUALE")
    print("=" * 50)
    
    base_url = "http://localhost:8002"
    
    # Step 1: Login come admin
    print("1. Login come admin...")
    
    login_data = {
        "username": "<EMAIL>",
        "password": "271077"
    }
    
    session = requests.Session()
    
    # Login
    login_response = session.post(f"{base_url}/login", data=login_data)
    
    if login_response.status_code != 200:
        print(f"ERROR: Login fallito - {login_response.status_code}")
        return False
    
    print("   ✅ Login riuscito")
    
    # Step 2: Test backup manuale formato custom
    print("\n2. Test backup manuale formato custom...")
    
    try:
        backup_response = session.post(f"{base_url}/admin/backup/manual?format_type=custom")
        
        print(f"   Status code: {backup_response.status_code}")
        
        if backup_response.status_code == 200:
            backup_data = backup_response.json()
            
            if backup_data.get('success'):
                print("   ✅ Backup custom creato con successo!")
                print(f"   📁 File: {backup_data.get('backup_file')}")
                print(f"   📊 Dimensione: {backup_data.get('file_size')} bytes")
                print(f"   🔧 Formato: {backup_data.get('format')}")
                print(f"   👤 Creato da: {backup_data.get('created_by')}")
                print(f"   ⏰ Timestamp: {backup_data.get('timestamp')}")
            else:
                print(f"   ❌ Backup fallito: {backup_data}")
                return False
        else:
            print(f"   ❌ Errore HTTP: {backup_response.status_code}")
            print(f"   Risposta: {backup_response.text}")
            return False
            
    except Exception as e:
        print(f"   ❌ Errore durante backup custom: {e}")
        return False
    
    # Step 3: Test backup manuale formato SQL
    print("\n3. Test backup manuale formato SQL...")
    
    try:
        backup_response = session.post(f"{base_url}/admin/backup/manual?format_type=sql")
        
        print(f"   Status code: {backup_response.status_code}")
        
        if backup_response.status_code == 200:
            backup_data = backup_response.json()
            
            if backup_data.get('success'):
                print("   ✅ Backup SQL creato con successo!")
                print(f"   📁 File: {backup_data.get('backup_file')}")
                print(f"   📊 Dimensione: {backup_data.get('file_size')} bytes")
                print(f"   🔧 Formato: {backup_data.get('format')}")
            else:
                print(f"   ❌ Backup fallito: {backup_data}")
                return False
        else:
            print(f"   ❌ Errore HTTP: {backup_response.status_code}")
            print(f"   Risposta: {backup_response.text}")
            return False
            
    except Exception as e:
        print(f"   ❌ Errore durante backup SQL: {e}")
        return False
    
    # Step 4: Test stato backup
    print("\n4. Test stato backup...")
    
    try:
        status_response = session.get(f"{base_url}/admin/backup/status")
        
        print(f"   Status code: {status_response.status_code}")
        
        if status_response.status_code == 200:
            status_data = status_response.json()
            
            print("   ✅ Stato backup ottenuto!")
            print(f"   📁 Directory backup: {status_data.get('backup_directory')}")
            print(f"   📊 Totale backup: {status_data.get('total_backups')}")
            
            recent_backups = status_data.get('recent_backups', [])
            if recent_backups:
                print(f"   📋 Backup recenti:")
                for i, backup in enumerate(recent_backups[:3], 1):
                    print(f"      {i}. {backup['name']} ({backup['size']} bytes, {backup['format']})")
            
        else:
            print(f"   ❌ Errore stato backup: {status_response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ Errore durante stato backup: {e}")
        return False
    
    return True

def main():
    print("TEST COMPLETO BACKUP MANUALE")
    print("=" * 60)
    
    success = test_manual_backup_endpoint()
    
    print(f"\n" + "=" * 60)
    print("RISULTATO FINALE")
    print("=" * 60)
    
    if success:
        print("✅ TUTTI I TEST SUPERATI!")
        print()
        print("FUNZIONALITÀ VERIFICATE:")
        print("- ✅ Endpoint backup manuale custom")
        print("- ✅ Endpoint backup manuale SQL")
        print("- ✅ Endpoint stato backup")
        print("- ✅ Autenticazione admin")
        print("- ✅ Creazione file backup")
        print("- ✅ Logging audit")
        print()
        print("IL PULSANTE BACKUP MANUALE È FUNZIONANTE!")
        print()
        print("UTILIZZO NEL DASHBOARD:")
        print("1. Vai su /dashboard/amministrazione")
        print("2. Sezione 'Configurazioni Database'")
        print("3. Clicca 'Backup Manuale'")
        print("4. Il backup verrà creato automaticamente")
        print("5. Riceverai notifica di successo con dettagli")
    else:
        print("❌ ALCUNI TEST FALLITI")
        print()
        print("PROBLEMI POSSIBILI:")
        print("- Endpoint non registrato correttamente")
        print("- Errori di autenticazione")
        print("- Problemi con backup_manager")
        print("- Configurazione database errata")
        print()
        print("VERIFICA:")
        print("1. Server FastAPI in esecuzione")
        print("2. Database PostgreSQL connesso")
        print("3. Credenziali admin corrette")
        print("4. File admin_routes.py aggiornato")

if __name__ == "__main__":
    main()
