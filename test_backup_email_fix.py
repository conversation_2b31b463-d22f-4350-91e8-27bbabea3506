#!/usr/bin/env python3
"""
Test per verificare che il backup manager legga correttamente l'admin_email
"""

from datetime import datetime

def test_backup_email_configuration():
    """Test configurazione email per backup"""
    print("TEST CONFIGURAZIONE EMAIL BACKUP")
    print("=" * 50)
    print(f"Timestamp: {datetime.now()}")
    print()
    
    try:
        from backup_manager import BackupManager
        from config import settings
        
        # Crea BackupManager
        print("1. Creazione BackupManager...")
        backup_manager = BackupManager(settings.DATABASE_URL)
        print("   BackupManager creato con successo")
        
        # Ottieni configurazioni backup
        print("\n2. Lettura configurazioni backup...")
        config = backup_manager.get_backup_config()
        print(f"   Configurazioni trovate: {len(config)}")
        
        # Mostra tutte le configurazioni
        print("\n3. Configurazioni backup:")
        for key, value in config.items():
            if 'password' in key.lower():
                print(f"   {key}: ***")
            else:
                print(f"   {key}: {value}")
        
        # Verifica admin_email
        print("\n4. Verifica admin_email:")
        admin_email = config.get('admin_email', '')
        if admin_email:
            print(f"   TROVATO: {admin_email}")
            print("   Tipo:", type(admin_email))
            print("   Lunghezza:", len(admin_email))
            
            # Verifica formato email
            if '@' in admin_email and '.' in admin_email:
                print("   Formato email: VALIDO")
            else:
                print("   Formato email: INVALIDO")
        else:
            print("   NON TROVATO o VUOTO")
        
        # Test simulazione invio notifica
        print("\n5. Simulazione controllo invio notifica...")
        if not admin_email:
            print("   RISULTATO: Email amministratore non configurata - notifica non inviata")
            print("   AZIONE: Configurare admin_email nel database")
        else:
            print(f"   RISULTATO: Email amministratore configurata: {admin_email}")
            print("   AZIONE: Notifica backup verrebbe inviata")
        
        return admin_email
        
    except Exception as e:
        print(f"ERRORE: {e}")
        import traceback
        traceback.print_exc()
        return None

def analyze_email_config_in_database():
    """Analizza le configurazioni email nel database"""
    print("\n6. Analisi configurazioni email nel database:")
    print("-" * 50)
    
    try:
        # Simula query database
        print("   Query configurazioni email:")
        print("   SELECT config_key, config_value FROM SYSTEM_CONFIG")
        print("   WHERE config_key IN ('admin_email', 'email_admin_email')")
        print()
        
        print("   Possibili scenari:")
        print("   a) admin_email = '<EMAIL>' (formato diretto)")
        print("   b) email_admin_email = '<EMAIL>' (formato con prefisso)")
        print("   c) Entrambi presenti (usa quello trovato)")
        print("   d) Nessuno presente (usa valore predefinito vuoto)")
        
        return True
        
    except Exception as e:
        print(f"   ERRORE analisi: {e}")
        return False

def show_email_flow():
    """Mostra il flusso completo dell'invio email"""
    print("\n7. Flusso completo invio email backup:")
    print("-" * 50)
    
    steps = [
        "1. Backup automatico viene eseguito (create_backup)",
        "2. Backup completato con successo",
        "3. Chiamata _send_backup_notification(backup_path, file_size)",
        "4. get_backup_config() legge configurazioni dal database",
        "5. Cerca 'admin_email' e 'email_admin_email'",
        "6. Se email_admin_email trovato, lo usa come admin_email",
        "7. Controlla se admin_email e' configurato",
        "8. Se SI: importa send_email da main.py",
        "9. Crea oggetto e corpo email con dettagli backup",
        "10. Chiama send_email(admin_email, subject, body, db=db)",
        "11. send_email usa configurazioni SMTP dal database",
        "12. Invia email tramite server SMTP",
        "13. Log risultato: '[EMAIL] Notifica backup inviata' o errore"
    ]
    
    for step in steps:
        print(f"   {step}")

def main():
    print("VERIFICA COMPLETA SISTEMA EMAIL BACKUP")
    print("=" * 60)
    
    # Test configurazione
    admin_email = test_backup_email_configuration()
    
    # Analisi database
    analyze_email_config_in_database()
    
    # Flusso completo
    show_email_flow()
    
    # Conclusioni
    print("\n" + "=" * 60)
    print("CONCLUSIONI")
    print("=" * 60)
    
    if admin_email:
        print("STATO: CONFIGURATO")
        print(f"Admin email: {admin_email}")
        print("AZIONE: Le email di backup verranno inviate")
        print()
        print("PROSSIMI PASSI:")
        print("1. Verificare configurazioni SMTP nel dashboard")
        print("2. Testare invio email manuale")
        print("3. Eseguire backup manuale per testare notifica")
        print("4. Controllare log per conferma invio")
    else:
        print("STATO: NON CONFIGURATO")
        print("AZIONE: Configurare admin_email")
        print()
        print("COME CONFIGURARE:")
        print("1. Andare su /dashboard/amministrazione")
        print("2. Sezione 'Configurazione Email'")
        print("3. Impostare 'Email Amministratore'")
        print("4. Salvare configurazioni")
        print("5. Testare backup per verificare notifica")
    
    print("\nCORREZIONE APPLICATA:")
    print("+ Aggiunto supporto per 'email_admin_email' nella query")
    print("+ Mappatura automatica email_admin_email -> admin_email")
    print("+ Compatibilita' con entrambi i formati di configurazione")

if __name__ == "__main__":
    main()
