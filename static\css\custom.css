/* Custom styles for M.A.P. application */

/* Font grassetto globale per tutta l'applicazione */
body, .card, .card-body, .card-header, .btn, .form-control, .form-label,
.table, .nav-link, .modal-body, .modal-header, .modal-footer,
.alert, .badge, .dropdown-menu, .navbar, .breadcrumb,
input, select, textarea, label, p, div, span, h1, h2, h3, h4, h5, h6,
.list-group-item, .pagination, .progress, .toast {
    font-weight: bold !important;
}

/* Font extra grassetto per elementi importanti */
.card-title, .modal-title, .btn, .nav-link.active,
.table th, .form-label, .alert, .badge, .navbar-brand,
.dropdown-header, .list-group-item.active {
    font-weight: 900 !important;
}

/* Font grassetto per testo specifico */
.text-muted, .small, .form-text, .text-secondary {
    font-weight: 600 !important;
}

/* Navbar styling */
.navbar-custom {
    background-color: #1a237e !important;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.navbar-custom .navbar-brand {
    font-weight: bold;
    color: white;
    padding: 0.5rem 1rem;
    display: flex;
    align-items: center;
}

.navbar-custom .navbar-brand img {
    margin-right: 10px;
}

.navbar-custom .nav-link {
    color: rgba(255, 255, 255, 0.85) !important;
    font-weight: 500;
    padding: 0.8rem 1rem;
    transition: all 0.3s ease;
}

.navbar-custom .nav-link:hover,
.navbar-custom .nav-link.active {
    color: white !important;
    background-color: rgba(255, 255, 255, 0.1);
}

.navbar-custom .dropdown-menu {
    border: none;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    border-radius: 0.25rem;
}

.navbar-custom .dropdown-item {
    padding: 0.5rem 1.5rem;
    font-weight: 500;
}

.navbar-custom .dropdown-item.active,
.navbar-custom .dropdown-item:active {
    background-color: #3949ab;
    color: white;
}

.navbar-custom .dropdown-toggle::after {
    margin-left: 0.5rem;
}

/* Dashboard cards */
.stat-card {
    border-radius: 0.5rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    margin-bottom: 1.5rem;
    overflow: hidden;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}

.stat-card .card-body {
    padding: 1.5rem;
}

.stat-card .stat-icon {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    display: inline-block;
}

.stat-card .stat-number {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.stat-card .stat-label {
    color: #6c757d;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 1px;
}

/* Color variations for stat cards */
.stat-card.primary {
    background-color: #e8eaf6;
}
.stat-card.primary .stat-icon {
    color: #3f51b5;
}

.stat-card.success {
    background-color: #e8f5e9;
}
.stat-card.success .stat-icon {
    color: #4caf50;
}

.stat-card.warning {
    background-color: #fff8e1;
}
.stat-card.warning .stat-icon {
    color: #ff9800;
}

.stat-card.danger {
    background-color: #ffebee;
}
.stat-card.danger .stat-icon {
    color: #f44336;
}

/* Activity feed */
.activity-feed {
    padding: 1rem;
    max-height: 400px;
    overflow-y: auto;
}

.activity-item {
    padding: 1rem;
    border-left: 3px solid #3f51b5;
    background-color: #f8f9fa;
    margin-bottom: 1rem;
    border-radius: 0 0.25rem 0.25rem 0;
}

.activity-item .activity-time {
    font-size: 0.8rem;
    color: #6c757d;
}

.activity-item .activity-title {
    font-weight: bold;
    margin-bottom: 0.25rem;
}

/* Quick action buttons */
.quick-action {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 1rem;
    background-color: #f8f9fa;
    border-radius: 0.5rem;
    transition: all 0.3s ease;
    text-decoration: none;
    color: #212529;
    margin-bottom: 1rem;
}

.quick-action:hover {
    background-color: #e9ecef;
    transform: translateY(-3px);
}

.quick-action i {
    font-size: 2rem;
    margin-bottom: 0.5rem;
    color: #3f51b5;
}

/* Chart container */
.chart-container {
    position: relative;
    height: 300px;
    margin-bottom: 2rem;
}

/* Viaggi specific styles */
.status-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    display: inline-block;
    margin-left: 8px;
}

.status-green {
    background-color: #28a745;
    box-shadow: 0 0 5px rgba(40, 167, 69, 0.5);
}

.status-red {
    background-color: #dc3545;
    box-shadow: 0 0 5px rgba(220, 53, 69, 0.5);
}

.card-orari {
    min-height: 400px;
    transition: all 0.3s ease;
}

.card-orari:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.save-button-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 400px;
}

.save-button-container .btn {
    padding: 1.5rem 2rem;
    font-size: 1.1rem;
    font-weight: bold;
    text-align: center;
    line-height: 1.2;
    border-radius: 0.5rem;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
}

.save-button-container .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.3);
}

/* Tab styling with status indicators */
.nav-tabs .nav-link {
    position: relative;
    padding: 0.75rem 1.5rem;
    font-weight: 500;
}

.nav-tabs .nav-link .status-indicator {
    position: absolute;
    top: 8px;
    right: 8px;
}

/* Viaggi table styling */
.table-viaggi {
    border-radius: 0.5rem;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.table-viaggi th {
    background-color: #343a40;
    color: white;
    font-weight: 600;
    border: none;
    padding: 1rem;
}

.table-viaggi td {
    padding: 1rem;
    vertical-align: middle;
    border-color: #e9ecef;
}

.table-viaggi tbody tr:hover {
    background-color: #f8f9fa;
}

/* Modal improvements */
.modal-header {
    background-color: #f8f9fa;
    border-bottom: 2px solid #dee2e6;
}

.modal-title {
    font-weight: 600;
    color: #495057;
}

/* Form improvements */
.form-label {
    font-weight: 500;
    color: #495057;
    margin-bottom: 0.5rem;
}

.form-control:focus,
.form-select:focus {
    border-color: #3f51b5;
    box-shadow: 0 0 0 0.2rem rgba(63, 81, 181, 0.25);
}

/* Alert positioning */
.alert.position-fixed {
    top: 20px;
    right: 20px;
    z-index: 9999;
    min-width: 300px;
    max-width: 500px;
}

/* Empty state styling */
.empty-state {
    text-align: center;
    padding: 3rem 1rem;
}

.empty-state i {
    font-size: 4rem;
    color: #6c757d;
    margin-bottom: 1rem;
}

.empty-state h5 {
    color: #6c757d;
    margin-bottom: 0.5rem;
}

.empty-state p {
    color: #adb5bd;
    margin-bottom: 1.5rem;
}
