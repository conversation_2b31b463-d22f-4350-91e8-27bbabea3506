<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SOF Archiviati - SNIP</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- CSS Globali -->
    <link rel="stylesheet" href="{{ url_for('static', path='css/global-themes.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', path='css/maritime-theme-contrast-fixes.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', path='css/sof-archiviati-contrast-fixes.css') }}">
    
    <style>
        /* ===== TEMA MARITTIMO SPETTACOLARE - OCEANO PROFONDO ===== */

        /* Animazioni fluide per l'oceano */
        @keyframes waveMotion {
            0%, 100% { transform: translateX(0) translateY(0) rotate(0deg); }
            25% { transform: translateX(2px) translateY(-1px) rotate(0.5deg); }
            50% { transform: translateX(0) translateY(-2px) rotate(0deg); }
            75% { transform: translateX(-2px) translateY(-1px) rotate(-0.5deg); }
        }

        @keyframes shimmer {
            0% { background-position: -200% 0; }
            100% { background-position: 200% 0; }
        }

        @keyframes deepSea {
            0%, 100% { box-shadow: 0 8px 32px rgba(0, 119, 190, 0.3), 0 4px 16px rgba(0, 180, 216, 0.2); }
            50% { box-shadow: 0 12px 40px rgba(0, 119, 190, 0.4), 0 6px 20px rgba(0, 180, 216, 0.3); }
        }

        /* Card archivio - Profondità oceanica */
        .archive-card {
            position: relative;
            transition: all 0.6s cubic-bezier(0.23, 1, 0.32, 1);
            border: none;
            background: linear-gradient(145deg,
                rgba(255, 255, 255, 0.95) 0%,
                rgba(240, 249, 255, 0.9) 30%,
                rgba(225, 245, 254, 0.85) 70%,
                rgba(186, 230, 253, 0.8) 100%);
            border-radius: 20px;
            overflow: hidden;
            box-shadow:
                0 8px 32px rgba(0, 119, 190, 0.15),
                0 4px 16px rgba(0, 180, 216, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(20px);
        }

        .archive-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg,
                transparent,
                rgba(0, 180, 216, 0.1),
                transparent);
            transition: left 0.6s ease;
            z-index: 1;
        }

        .archive-card:hover::before {
            left: 100%;
        }

        .archive-card:hover {
            transform: translateY(-8px) scale(1.03);
            animation: deepSea 2s ease-in-out infinite;
            border: 2px solid rgba(0, 180, 216, 0.4);
        }

        .archive-card .card-header {
            background: linear-gradient(135deg,
                #0077be 0%,
                #00b4d8 25%,
                #0096c7 50%,
                #0077be 75%,
                #023e8a 100%);
            background-size: 300% 300%;
            animation: waveMotion 4s ease-in-out infinite;
            border-bottom: 4px solid #f4a261;
            color: #ffffff;
            font-weight: 700;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
            position: relative;
            z-index: 2;
        }

        .archive-card .card-header::after {
            content: '';
            position: absolute;
            bottom: -4px;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg,
                #f4a261 0%,
                #e76f51 50%,
                #f4a261 100%);
            background-size: 200% 100%;
            animation: shimmer 3s ease-in-out infinite;
        }

        .file-info {
            font-size: 0.85rem;
            color: #264653;
            font-weight: 600;
            text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);
        }

        /* Sezione filtro data - Laguna tropicale */
        .date-filter {
            background: linear-gradient(135deg,
                rgba(0, 119, 190, 0.08) 0%,
                rgba(0, 180, 216, 0.06) 25%,
                rgba(144, 224, 239, 0.04) 50%,
                rgba(255, 255, 255, 0.95) 75%,
                rgba(240, 249, 255, 0.9) 100%);
            border: 3px solid transparent;
            background-clip: padding-box;
            border-radius: 25px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow:
                0 10px 40px rgba(0, 119, 190, 0.15),
                0 5px 20px rgba(0, 180, 216, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(15px);
            position: relative;
        }

        .date-filter::before {
            content: '';
            position: absolute;
            top: -3px;
            left: -3px;
            right: -3px;
            bottom: -3px;
            background: linear-gradient(45deg,
                #0077be, #00b4d8, #90e0ef, #caf0f8, #0077be);
            background-size: 400% 400%;
            animation: waveMotion 6s ease-in-out infinite;
            border-radius: 25px;
            z-index: -1;
        }

        /* Card statistiche - Corallo vivace */
        .stats-card {
            background: linear-gradient(145deg,
                #caf0f8 0%,
                #90e0ef 25%,
                #00b4d8 50%,
                #0077be 75%,
                #03045e 100%);
            background-size: 300% 300%;
            animation: waveMotion 5s ease-in-out infinite;
            border: 3px solid rgba(0, 180, 216, 0.3);
            border-radius: 20px;
            transition: all 0.5s cubic-bezier(0.23, 1, 0.32, 1);
            box-shadow:
                0 8px 32px rgba(0, 119, 190, 0.2),
                0 4px 16px rgba(0, 180, 216, 0.15);
            color: #ffffff;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
            position: relative;
            overflow: hidden;
        }

        .stats-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg,
                rgba(255, 255, 255, 0.1) 0%,
                rgba(255, 255, 255, 0.05) 50%,
                rgba(255, 255, 255, 0.1) 100%);
            background-size: 200% 200%;
            animation: shimmer 4s ease-in-out infinite;
            pointer-events: none;
        }

        .stats-card:hover {
            transform: translateY(-6px) rotateX(5deg);
            box-shadow:
                0 15px 50px rgba(0, 119, 190, 0.3),
                0 8px 25px rgba(0, 180, 216, 0.2);
            border-color: rgba(244, 162, 97, 0.6);
        }

        /* Sezione upload - Reef colorato */
        .upload-section {
            background: linear-gradient(135deg,
                rgba(38, 70, 83, 0.05) 0%,
                rgba(42, 157, 143, 0.08) 25%,
                rgba(233, 196, 106, 0.06) 50%,
                rgba(244, 162, 97, 0.04) 75%,
                rgba(255, 255, 255, 0.95) 100%);
            border-radius: 20px;
            padding: 25px;
            border: 4px dashed #2a9d8f;
            transition: all 0.5s cubic-bezier(0.23, 1, 0.32, 1);
            box-shadow:
                0 8px 32px rgba(42, 157, 143, 0.15),
                0 4px 16px rgba(233, 196, 106, 0.1);
            position: relative;
            overflow: hidden;
        }

        .upload-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: radial-gradient(circle at 30% 70%,
                rgba(42, 157, 143, 0.1) 0%,
                transparent 50%),
                radial-gradient(circle at 70% 30%,
                rgba(244, 162, 97, 0.08) 0%,
                transparent 50%);
            animation: waveMotion 8s ease-in-out infinite;
            pointer-events: none;
        }

        .upload-section:hover {
            border-color: #264653;
            background: linear-gradient(135deg,
                rgba(38, 70, 83, 0.08) 0%,
                rgba(42, 157, 143, 0.12) 25%,
                rgba(233, 196, 106, 0.1) 50%,
                rgba(244, 162, 97, 0.08) 75%,
                rgba(255, 255, 255, 0.98) 100%);
            transform: translateY(-4px) scale(1.02);
            box-shadow:
                0 12px 40px rgba(42, 157, 143, 0.25),
                0 6px 20px rgba(233, 196, 106, 0.15);
        }

        /* Input file - Conchiglia marina */
        #fileInput {
            border: 3px solid #2a9d8f;
            border-radius: 15px;
            transition: all 0.4s cubic-bezier(0.23, 1, 0.32, 1);
            background: linear-gradient(135deg,
                rgba(255, 255, 255, 0.95) 0%,
                rgba(202, 240, 248, 0.3) 100%);
            backdrop-filter: blur(10px);
            font-weight: 500;
        }

        #fileInput:focus {
            border-color: #e76f51;
            box-shadow:
                0 0 0 0.4rem rgba(42, 157, 143, 0.25),
                0 8px 25px rgba(42, 157, 143, 0.2);
            background: linear-gradient(135deg,
                rgba(255, 255, 255, 1) 0%,
                rgba(202, 240, 248, 0.5) 100%);
            transform: translateY(-2px);
        }

        /* Pulsanti - Onde dell'oceano */
        .btn-primary {
            background: linear-gradient(135deg,
                #0077be 0%,
                #00b4d8 50%,
                #0096c7 100%);
            background-size: 200% 200%;
            animation: waveMotion 3s ease-in-out infinite;
            border: none;
            border-radius: 15px;
            font-weight: 700;
            transition: all 0.4s cubic-bezier(0.23, 1, 0.32, 1);
            box-shadow:
                0 6px 20px rgba(0, 119, 190, 0.4),
                0 3px 10px rgba(0, 180, 216, 0.3);
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
            position: relative;
            overflow: hidden;
        }

        .btn-primary::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg,
                transparent,
                rgba(255, 255, 255, 0.3),
                transparent);
            transition: left 0.6s ease;
        }

        .btn-primary:hover::before {
            left: 100%;
        }

        .btn-primary:hover {
            background: linear-gradient(135deg,
                #023e8a 0%,
                #0077be 50%,
                #00b4d8 100%);
            transform: translateY(-4px) scale(1.05);
            box-shadow:
                0 10px 30px rgba(0, 119, 190, 0.5),
                0 5px 15px rgba(0, 180, 216, 0.4);
        }

        .btn-success {
            background: linear-gradient(135deg,
                #2a9d8f 0%,
                #e9c46a 50%,
                #f4a261 100%);
            background-size: 200% 200%;
            animation: waveMotion 4s ease-in-out infinite;
            border: none;
            border-radius: 15px;
            font-weight: 700;
            transition: all 0.4s cubic-bezier(0.23, 1, 0.32, 1);
            box-shadow:
                0 6px 20px rgba(42, 157, 143, 0.4),
                0 3px 10px rgba(233, 196, 106, 0.3);
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
            position: relative;
            overflow: hidden;
        }

        .btn-success::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg,
                transparent,
                rgba(255, 255, 255, 0.3),
                transparent);
            transition: left 0.6s ease;
        }

        .btn-success:hover::before {
            left: 100%;
        }

        .btn-success:hover {
            background: linear-gradient(135deg,
                #264653 0%,
                #2a9d8f 50%,
                #e9c46a 100%);
            transform: translateY(-4px) scale(1.05);
            box-shadow:
                0 10px 30px rgba(42, 157, 143, 0.5),
                0 5px 15px rgba(233, 196, 106, 0.4);
        }

        .btn-outline-secondary {
            color: #264653;
            border: 3px solid #264653;
            border-radius: 15px;
            font-weight: 700;
            transition: all 0.4s cubic-bezier(0.23, 1, 0.32, 1);
            background: linear-gradient(135deg,
                rgba(255, 255, 255, 0.9) 0%,
                rgba(202, 240, 248, 0.3) 100%);
            backdrop-filter: blur(10px);
            position: relative;
            overflow: hidden;
        }

        .btn-outline-secondary::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg,
                transparent,
                rgba(38, 70, 83, 0.1),
                transparent);
            transition: left 0.6s ease;
        }

        .btn-outline-secondary:hover::before {
            left: 100%;
        }

        .btn-outline-secondary:hover {
            background: linear-gradient(135deg,
                #264653 0%,
                #2a9d8f 100%);
            color: #ffffff;
            transform: translateY(-4px) scale(1.05);
            box-shadow:
                0 10px 30px rgba(38, 70, 83, 0.4),
                0 5px 15px rgba(42, 157, 143, 0.3);
            border-color: #2a9d8f;
        }

        /* Badge - Perle marine */
        .badge.bg-info {
            background: linear-gradient(135deg,
                #0077be 0%,
                #00b4d8 50%,
                #90e0ef 100%) !important;
            background-size: 200% 200%;
            animation: shimmer 3s ease-in-out infinite;
            color: #ffffff;
            font-weight: 700;
            padding: 8px 16px;
            border-radius: 12px;
            box-shadow:
                0 4px 12px rgba(0, 119, 190, 0.4),
                0 2px 6px rgba(0, 180, 216, 0.3);
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
            border: 2px solid rgba(255, 255, 255, 0.3);
        }

        .badge.bg-primary {
            background: linear-gradient(135deg,
                #2a9d8f 0%,
                #e9c46a 50%,
                #f4a261 100%) !important;
            background-size: 200% 200%;
            animation: shimmer 4s ease-in-out infinite;
            color: #ffffff;
            font-weight: 700;
            padding: 8px 16px;
            border-radius: 12px;
            box-shadow:
                0 4px 12px rgba(42, 157, 143, 0.4),
                0 2px 6px rgba(233, 196, 106, 0.3);
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
            border: 2px solid rgba(255, 255, 255, 0.3);
        }

        /* Icone con effetto brillante */
        .fas, .far, .fab {
            transition: all 0.3s ease;
            filter: drop-shadow(0 2px 4px rgba(0, 119, 190, 0.3));
        }

        .text-primary {
            color: #0077be !important;
            text-shadow: 0 1px 2px rgba(0, 119, 190, 0.3);
        }

        .text-success {
            color: #2a9d8f !important;
            text-shadow: 0 1px 2px rgba(42, 157, 143, 0.3);
        }

        .text-info {
            color: #00b4d8 !important;
            text-shadow: 0 1px 2px rgba(0, 180, 216, 0.3);
        }

        .text-warning {
            color: #f4a261 !important;
            text-shadow: 0 1px 2px rgba(244, 162, 97, 0.3);
        }

        /* Effetti hover globali */
        .card:hover .fas,
        .card:hover .far,
        .card:hover .fab {
            transform: scale(1.1) rotate(5deg);
            filter: drop-shadow(0 4px 8px rgba(0, 119, 190, 0.5));
        }

        /* Responsive per dispositivi mobili */
        @media (max-width: 768px) {
            .archive-card:hover {
                transform: translateY(-4px) scale(1.01);
            }

            .stats-card:hover {
                transform: translateY(-3px);
            }

            .upload-section:hover {
                transform: translateY(-2px) scale(1.01);
            }
        }
    </style>
</head>
<body class="theme-{{ user_theme or 'default' }}">
    <!-- Navbar -->
    {% include "components/navbar.html" %}
    
    <div class="container-fluid mt-4">
        <div class="row">
            <!-- Main Content -->
            <div class="col-12">
                <!-- Tab Navigation -->
                <ul class="nav nav-tabs mb-4" id="archiveTab" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link {% if tab == 'archiviati' or not tab %}active{% endif %}"
                                id="archiviati-tab" data-bs-toggle="tab" data-bs-target="#archiviati"
                                type="button" role="tab" aria-controls="archiviati"
                                aria-selected="{% if tab == 'archiviati' or not tab %}true{% else %}false{% endif %}"
                                onclick="window.location.href='/operativo/sof/archiviati?tab=archiviati'">
                            <i class="fas fa-database me-2"></i>
                            Archiviati Database
                            <span class="badge bg-info ms-2">{{ archivio_files|length if archivio_files else 0 }}</span>
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link {% if tab == 'archivio_old' %}active{% endif %}"
                                id="archivio-old-tab" data-bs-toggle="tab" data-bs-target="#archivio-old"
                                type="button" role="tab" aria-controls="archivio-old"
                                aria-selected="{% if tab == 'archivio_old' %}true{% else %}false{% endif %}"
                                onclick="window.location.href='/operativo/sof/archiviati?tab=archivio_old'">
                            <i class="fas fa-file-archive me-2"></i>
                            Archivio JSON
                            <span class="badge bg-success ms-2">{{ archivio_old_files|length if archivio_old_files else 0 }}</span>
                        </button>
                    </li>
                    {% if is_admin %}
                    <li class="nav-item" role="presentation">
                        <button class="nav-link {% if tab == 'gestione' %}active{% endif %}"
                                id="gestione-tab" data-bs-toggle="tab" data-bs-target="#gestione"
                                type="button" role="tab" aria-controls="gestione"
                                aria-selected="{% if tab == 'gestione' %}true{% else %}false{% endif %}"
                                onclick="window.location.href='/operativo/sof/archiviati?tab=gestione'">
                            <i class="fas fa-cogs me-2"></i>
                            Gestione Archivio
                            <span class="badge bg-warning ms-2">ADMIN</span>
                            <span class="badge bg-primary ms-1">{{ viaggi_da_archiviare|length if viaggi_da_archiviare else 0 }}</span>
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link {% if tab == 'carica' %}active{% endif %}"
                                id="carica-tab" data-bs-toggle="tab" data-bs-target="#carica"
                                type="button" role="tab" aria-controls="carica"
                                aria-selected="{% if tab == 'carica' %}true{% else %}false{% endif %}"
                                onclick="window.location.href='/operativo/sof/archiviati?tab=carica'">
                            <i class="fas fa-cloud-upload-alt me-2"></i>
                            Carica File Archiviati
                            <span class="badge bg-danger ms-2">ADMIN</span>
                        </button>
                    </li>
                    {% endif %}
                </ul>

                <!-- Tab Content -->
                <div class="tab-content" id="archiveTabContent">

                    <!-- TAB 1: ARCHIVIATI DATABASE -->
                    <div class="tab-pane fade {% if tab == 'archiviati' or not tab %}show active{% endif %}"
                         id="archiviati" role="tabpanel" aria-labelledby="archiviati-tab">

                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h4 class="mb-0">
                                    <i class="fas fa-database text-primary me-2"></i>
                                    SOF Archiviati nel Database
                                </h4>
                                <div class="d-flex gap-2">
                                    <span class="badge bg-info">{{ archivio_files|length if archivio_files else 0 }} viaggi</span>
                                    <button class="btn btn-outline-primary btn-sm" onclick="location.reload()">
                                        <i class="fas fa-sync-alt"></i> Aggiorna
                                    </button>
                                    <a href="/operativo/sof/da-realizzare" class="btn btn-outline-secondary btn-sm">
                                        <i class="fas fa-arrow-left me-1"></i>
                                        Torna ai SOF
                                    </a>
                                </div>
                            </div>
                    
                    <div class="card-body">
                        {% if error %}
                            <div class="alert alert-danger">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                Errore: {{ error }}
                            </div>
                        {% endif %}
                        
                        <!-- Controlli: Filtro per Data e Upload File -->
                        <div class="date-filter">
                            <div class="row">
                                <!-- Sezione Filtro per Data -->
                                <div class="col-lg-8">
                                    {% if date_range.min_date and date_range.max_date %}
                                    <h6 class="mb-3">
                                        <i class="fas fa-calendar-alt me-2"></i>
                                        Filtra per Data
                                    </h6>
                                    <div class="row align-items-end">
                                        <div class="col-md-4">
                                            <label for="dataInizio" class="form-label">Data Inizio</label>
                                            <input type="date" class="form-control" id="dataInizio"
                                                   value="{{ date_range.min_date_str }}"
                                                   min="{{ date_range.min_date_str }}"
                                                   max="{{ date_range.max_date_str }}">
                                        </div>
                                        <div class="col-md-4">
                                            <label for="dataFine" class="form-label">Data Fine</label>
                                            <input type="date" class="form-control" id="dataFine"
                                                   value="{{ date_range.max_date_str }}"
                                                   min="{{ date_range.min_date_str }}"
                                                   max="{{ date_range.max_date_str }}">
                                        </div>
                                        <div class="col-md-4">
                                            <button class="btn btn-primary" onclick="filtraPerData()">
                                                <i class="fas fa-search me-1"></i>
                                                Filtra
                                            </button>
                                            <button class="btn btn-outline-secondary ms-2" onclick="resetFiltri()">
                                                <i class="fas fa-times me-1"></i>
                                                Reset
                                            </button>
                                        </div>
                                    </div>
                                    <div class="mt-2">
                                        <small class="text-muted">
                                            Range disponibile: {{ date_range.min_date_display }} - {{ date_range.max_date_display }}
                                        </small>
                                    </div>
                                    {% else %}
                                    <h6 class="mb-3">
                                        <i class="fas fa-calendar-alt me-2"></i>
                                        Filtro per Data
                                    </h6>
                                    <div class="alert alert-info">
                                        <i class="fas fa-info-circle me-2"></i>
                                        Nessun file disponibile per il filtro date. Importa file JSON per abilitare i filtri.
                                    </div>
                                    {% endif %}
                                </div>

                                <!-- Sezione Upload File JSON -->
                                <div class="col-lg-4">
                                    <h6 class="mb-3">
                                        <i class="fas fa-upload me-2"></i>
                                        Importa File JSON
                                    </h6>
                                    <div class="upload-section">
                                        <form id="uploadForm" enctype="multipart/form-data">
                                            <div class="mb-3">
                                                <label for="fileInput" class="form-label">Seleziona file JSON</label>
                                                <input type="file" class="form-control" id="fileInput"
                                                       accept=".json" multiple>
                                                <div class="form-text">
                                                    Formato: NOME_NAVE_DDMMYYYY.json
                                                </div>
                                            </div>
                                            <div class="d-grid">
                                                <button type="button" class="btn btn-success" onclick="uploadFiles()" id="uploadBtn">
                                                    <i class="fas fa-cloud-upload-alt me-1"></i>
                                                    Carica File
                                                </button>
                                            </div>
                                        </form>

                                        <!-- Progress Bar -->
                                        <div class="mt-3" id="uploadProgress" style="display: none;">
                                            <div class="progress">
                                                <div class="progress-bar" role="progressbar" style="width: 0%"></div>
                                            </div>
                                            <small class="text-muted mt-1" id="uploadStatus">Caricamento in corso...</small>
                                        </div>

                                        <!-- Risultati Upload -->
                                        <div class="mt-3" id="uploadResults" style="display: none;">
                                            <!-- Risultati dinamici -->
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Statistiche -->
                        {% if total_files > 0 %}
                        <div class="row mb-4">
                            <div class="col-md-3">
                                <div class="card stats-card">
                                    <div class="card-body text-center">
                                        <i class="fas fa-file-archive fa-2x text-primary mb-2"></i>
                                        <h5 class="mb-0">{{ total_files }}</h5>
                                        <small class="text-muted">File Archiviati</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card stats-card">
                                    <div class="card-body text-center">
                                        <i class="fas fa-ship fa-2x text-success mb-2"></i>
                                        <h5 class="mb-0" id="totalNavi">-</h5>
                                        <small class="text-muted">Navi Diverse</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card stats-card">
                                    <div class="card-body text-center">
                                        <i class="fas fa-download fa-2x text-info mb-2"></i>
                                        <h5 class="mb-0" id="totalImport">-</h5>
                                        <small class="text-muted">Record Import</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card stats-card">
                                    <div class="card-body text-center">
                                        <i class="fas fa-upload fa-2x text-warning mb-2"></i>
                                        <h5 class="mb-0" id="totalExport">-</h5>
                                        <small class="text-muted">Record Export</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endif %}
                        
                        <!-- Lista File Archiviati -->
                        {% if archivio_files %}
                        <div class="row" id="archivioContainer">
                            {% for file in archivio_files %}
                            <div class="col-md-6 col-lg-4 mb-3 archive-item" 
                                 data-date="{{ file.file_date.strftime('%Y-%m-%d') }}"
                                 data-nave="{{ file.nave_nome }}"
                                 data-import="{{ file.import_records }}"
                                 data-export="{{ file.export_records }}">
                                <div class="card archive-card h-100">
                                    <div class="card-header d-flex justify-content-between align-items-center">
                                        <h6 class="mb-0 text-truncate">
                                            <i class="fas fa-ship me-1"></i>
                                            {{ file.nave_nome }}
                                        </h6>
                                        <span class="badge bg-primary">{{ file.date_str }}</span>
                                    </div>
                                    <div class="card-body">
                                        <div class="mb-2">
                                            <strong>Viaggio:</strong> {{ file.viaggio_codice }}
                                        </div>
                                        <div class="mb-2">
                                            <strong>Porto:</strong> {{ file.porto_gestione }}
                                        </div>
                                        <div class="row text-center mb-3">
                                            <div class="col-6">
                                                <div class="border rounded p-2">
                                                    <i class="fas fa-download text-info"></i>
                                                    <div class="fw-bold">{{ file.import_records }}</div>
                                                    <small class="text-muted">Import Qt</small>
                                                    {% if file.import_count is defined %}
                                                    <div class="text-xs text-secondary">({{ file.import_count }} record)</div>
                                                    {% endif %}
                                                </div>
                                            </div>
                                            <div class="col-6">
                                                <div class="border rounded p-2">
                                                    <i class="fas fa-upload text-warning"></i>
                                                    <div class="fw-bold">{{ file.export_records }}</div>
                                                    <small class="text-muted">Export Qt</small>
                                                    {% if file.export_count is defined %}
                                                    <div class="text-xs text-secondary">({{ file.export_count }} record)</div>
                                                    {% endif %}
                                                </div>
                                            </div>
                                        </div>
                                        <div class="file-info mb-3">
                                            <div><i class="fas fa-file me-1"></i> {{ file.filename }}</div>
                                            <div><i class="fas fa-weight me-1"></i> {{ file.file_size }} KB</div>
                                            {% if file.archiviato_da != 'N/A' %}
                                            <div><i class="fas fa-user me-1"></i> {{ file.archiviato_da }}</div>
                                            {% endif %}
                                        </div>
                                    </div>
                                    <div class="card-footer">
                                        <div class="d-grid gap-2">
                                            <button class="btn btn-primary btn-sm"
                                                    onclick="visualizzaDettagli('{{ file.filename }}')">
                                                <i class="fas fa-eye me-1"></i>
                                                Visualizza Dettagli
                                            </button>
                                            <button class="btn btn-success btn-sm"
                                                    onclick="scaricaSOF('{{ file.filename }}')">
                                                <i class="fas fa-download me-1"></i>
                                                Scarica SOF
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                        {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-archive fa-4x text-muted mb-3"></i>
                            <h5 class="text-muted">Nessun SOF archiviato</h5>
                            <p class="text-muted">I SOF archiviati appariranno qui quando disponibili.</p>
                        </div>
                        {% endif %}
                        </div>
                    </div>
                    </div>
                    <!-- Fine TAB 1: ARCHIVIATI DATABASE -->

                    <!-- TAB 2: ARCHIVIO_OLD JSON -->
                    <div class="tab-pane fade {% if tab == 'archivio_old' %}show active{% endif %}"
                         id="archivio-old" role="tabpanel" aria-labelledby="archivio-old-tab">

                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h4 class="mb-0">
                                    <i class="fas fa-file-archive text-success me-2"></i>
                                    Archivio File JSON
                                </h4>
                                <div class="d-flex gap-2">
                                    <span class="badge bg-success">{{ archivio_old_files|length if archivio_old_files else 0 }} file</span>
                                    <button class="btn btn-outline-primary btn-sm" onclick="location.reload()">
                                        <i class="fas fa-sync-alt"></i> Aggiorna
                                    </button>
                                </div>
                            </div>

                            <div class="card-body">
                                {% if archivio_old_files %}
                                <div class="row" id="archivioOldContainer">
                                    {% for file in archivio_old_files %}
                                    <div class="col-md-6 col-lg-4 mb-3">
                                        <div class="card archive-card h-100">
                                            <div class="card-header d-flex justify-content-between align-items-center">
                                                <h6 class="mb-0 text-truncate">
                                                    <i class="fas fa-file-archive me-1"></i>
                                                    {{ file.filename }}
                                                </h6>
                                                <span class="badge bg-success">{{ file.date_str }}</span>
                                            </div>
                                            <div class="card-body">
                                                <div class="mb-2">
                                                    <strong>Periodo:</strong> {{ file.periodo_archiviazione }}
                                                </div>
                                                <div class="mb-2">
                                                    <strong>Viaggi:</strong> {{ file.viaggi_count }}
                                                </div>
                                                <div class="row text-center mb-3">
                                                    <div class="col-6">
                                                        <div class="border rounded p-2">
                                                            <i class="fas fa-download text-info"></i>
                                                            <div class="fw-bold">{{ file.import_count }}</div>
                                                            <small class="text-muted">Import</small>
                                                        </div>
                                                    </div>
                                                    <div class="col-6">
                                                        <div class="border rounded p-2">
                                                            <i class="fas fa-upload text-warning"></i>
                                                            <div class="fw-bold">{{ file.export_count }}</div>
                                                            <small class="text-muted">Export</small>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="file-info mb-3">
                                                    <div><i class="fas fa-weight me-1"></i> {{ "%.2f"|format(file.file_size / 1024) }} KB</div>
                                                    <div><i class="fas fa-user me-1"></i> {{ file.archiviato_da }}</div>
                                                </div>
                                            </div>
                                            <div class="card-footer">
                                                <div class="d-grid gap-2">
                                                    <button class="btn btn-primary btn-sm"
                                                            onclick="visualizzaDettagliJSON('{{ file.filename }}')">
                                                        <i class="fas fa-eye me-1"></i>
                                                        Visualizza Dettagli
                                                    </button>
                                                    <button class="btn btn-success btn-sm"
                                                            onclick="scaricaJSON('{{ file.filename }}')">
                                                        <i class="fas fa-download me-1"></i>
                                                        Scarica JSON
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    {% endfor %}
                                </div>
                                {% else %}
                                <div class="text-center py-5">
                                    <i class="fas fa-file-archive fa-4x text-muted mb-3"></i>
                                    <h5 class="text-muted">Nessun file JSON archiviato</h5>
                                    <p class="text-muted">I file JSON archiviati appariranno qui quando disponibili.</p>
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    <!-- Fine TAB 2: ARCHIVIO_OLD JSON -->

                    <!-- TAB 3: GESTIONE ARCHIVIO (Solo ADMIN) -->
                    {% if is_admin %}
                    <div class="tab-pane fade {% if tab == 'gestione' %}show active{% endif %}"
                         id="gestione" role="tabpanel" aria-labelledby="gestione-tab">

                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h4 class="mb-0">
                                    <i class="fas fa-cogs text-warning me-2"></i>
                                    Gestione Archivio - Archiviazione in JSON
                                </h4>
                                <div class="d-flex gap-2">
                                    <span class="badge bg-warning">ADMIN ONLY</span>
                                    <span class="badge bg-primary">{{ viaggi_da_archiviare|length if viaggi_da_archiviare else 0 }} viaggi</span>
                                </div>
                            </div>

                            <div class="card-body">
                                <div class="alert alert-warning">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    <strong>ATTENZIONE:</strong> L'archiviazione in JSON comporta la <strong>cancellazione definitiva</strong>
                                    dei viaggi dal database. Questa operazione è irreversibile.
                                </div>

                                <!-- Controlli di Archiviazione -->
                                <div class="row mb-4">
                                    <div class="col-md-6">
                                        <div class="card">
                                            <div class="card-header">
                                                <h6 class="mb-0">
                                                    <i class="fas fa-calendar-alt me-2"></i>
                                                    Archiviazione per Periodo
                                                </h6>
                                            </div>
                                            <div class="card-body">
                                                <div class="mb-3">
                                                    <label class="form-label">Tipo Periodo</label>
                                                    <select class="form-select" id="tipoPeriodo" onchange="aggiornaCampiPeriodo()">
                                                        <option value="">Seleziona tipo periodo</option>
                                                        <option value="anno">Anno</option>
                                                        <option value="mese">Mese</option>
                                                        <option value="trimestre">Trimestre</option>
                                                        <option value="giorni">Range di giorni</option>
                                                    </select>
                                                </div>

                                                <div id="campiPeriodo">
                                                    <!-- Campi dinamici in base al tipo periodo -->
                                                </div>

                                                <button class="btn btn-warning w-100" onclick="archiviaPerPeriodo()" id="btnArchiviaPeriodo" disabled>
                                                    <i class="fas fa-archive me-2"></i>
                                                    Archivia per Periodo
                                                </button>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <div class="card">
                                            <div class="card-header">
                                                <h6 class="mb-0">
                                                    <i class="fas fa-check-square me-2"></i>
                                                    Archiviazione Selettiva
                                                </h6>
                                            </div>
                                            <div class="card-body">
                                                <div class="mb-3">
                                                    <button class="btn btn-outline-primary btn-sm me-2" onclick="selezionaTutti()">
                                                        <i class="fas fa-check-double me-1"></i>
                                                        Seleziona Tutti
                                                    </button>
                                                    <button class="btn btn-outline-secondary btn-sm" onclick="deselezionaTutti()">
                                                        <i class="fas fa-times me-1"></i>
                                                        Deseleziona Tutti
                                                    </button>
                                                </div>

                                                <div class="mb-3">
                                                    <span class="badge bg-info" id="contatoreSelezionati">0 selezionati</span>
                                                </div>

                                                <button class="btn btn-danger w-100" onclick="archiviaSelezionati()" id="btnArchiviaSelezionati" disabled>
                                                    <i class="fas fa-archive me-2"></i>
                                                    Archivia Selezionati
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Lista Viaggi da Archiviare -->
                                {% if viaggi_da_archiviare %}
                                <div class="card">
                                    <div class="card-header">
                                        <h6 class="mb-0">
                                            <i class="fas fa-list me-2"></i>
                                            Viaggi Disponibili per Archiviazione
                                        </h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="table-responsive">
                                            <table class="table table-hover">
                                                <thead>
                                                    <tr>
                                                        <th width="50">
                                                            <input type="checkbox" id="selectAll" onchange="toggleSelectAll()">
                                                        </th>
                                                        <th>Viaggio</th>
                                                        <th>Nave</th>
                                                        <th>Data Arrivo</th>
                                                        <th>Porto</th>
                                                        <th>Import</th>
                                                        <th>Export</th>
                                                        <th>Anno</th>
                                                        <th>Trimestre</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    {% for viaggio in viaggi_da_archiviare %}
                                                    <tr>
                                                        <td>
                                                            <input type="checkbox" class="viaggio-checkbox"
                                                                   value="{{ viaggio.viaggio_id }}"
                                                                   data-anno="{{ viaggio.anno }}"
                                                                   data-mese="{{ viaggio.mese }}"
                                                                   data-trimestre="{{ viaggio.trimestre }}"
                                                                   onchange="aggiornaContatori()">
                                                        </td>
                                                        <td>{{ viaggio.viaggio_codice }}</td>
                                                        <td>{{ viaggio.nome_nave }}</td>
                                                        <td>{{ viaggio.date_str }}</td>
                                                        <td>{{ viaggio.porto_gestione }}</td>
                                                        <td>
                                                            <span class="badge bg-info">{{ viaggio.import_records }}</span>
                                                        </td>
                                                        <td>
                                                            <span class="badge bg-warning">{{ viaggio.export_records }}</span>
                                                        </td>
                                                        <td>{{ viaggio.anno }}</td>
                                                        <td>Q{{ viaggio.trimestre }}</td>
                                                    </tr>
                                                    {% endfor %}
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                                {% else %}
                                <div class="text-center py-5">
                                    <i class="fas fa-check-circle fa-4x text-success mb-3"></i>
                                    <h5 class="text-success">Nessun viaggio da archiviare</h5>
                                    <p class="text-muted">Tutti i viaggi realizzati sono già stati archiviati.</p>
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    {% endif %}
                    <!-- Fine TAB 3: GESTIONE ARCHIVIO -->

                    <!-- TAB 4: CARICA FILE ARCHIVIATI (Solo ADMIN) -->
                    {% if is_admin %}
                    <div class="tab-pane fade {% if tab == 'carica' %}show active{% endif %}"
                         id="carica" role="tabpanel" aria-labelledby="carica-tab">

                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h4 class="mb-0">
                                    <i class="fas fa-cloud-upload-alt text-danger me-2"></i>
                                    Carica File JSON Archiviati
                                </h4>
                                <div class="d-flex gap-2">
                                    <span class="badge bg-danger">ADMIN ONLY</span>
                                </div>
                            </div>

                            <div class="card-body">
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle me-2"></i>
                                    <strong>Informazioni:</strong> Utilizza questa sezione per caricare file JSON di archivio
                                    precedentemente esportati. I file verranno salvati nella cartella <code>archiviati/</code>
                                    e saranno visibili nella tab "Archivio JSON".
                                </div>

                                <!-- Form Upload -->
                                <div class="row">
                                    <div class="col-md-8">
                                        <div class="card upload-section">
                                            <div class="card-header">
                                                <h6 class="mb-0">
                                                    <i class="fas fa-file-upload me-2"></i>
                                                    Seleziona File JSON
                                                </h6>
                                            </div>
                                            <div class="card-body">
                                                <form id="uploadJsonForm" enctype="multipart/form-data">
                                                    <div class="mb-3">
                                                        <label for="jsonFileInput" class="form-label">
                                                            <i class="fas fa-file-code me-1"></i>
                                                            File JSON da caricare
                                                        </label>
                                                        <input type="file" class="form-control" id="jsonFileInput"
                                                               accept=".json" multiple>
                                                        <div class="form-text">
                                                            <i class="fas fa-lightbulb me-1"></i>
                                                            Puoi selezionare più file JSON contemporaneamente.
                                                            Solo file con estensione .json sono accettati.
                                                        </div>
                                                    </div>

                                                    <div class="mb-3">
                                                        <div class="form-check">
                                                            <input class="form-check-input" type="checkbox" id="validateJson" checked>
                                                            <label class="form-check-label" for="validateJson">
                                                                Valida formato JSON prima del caricamento
                                                            </label>
                                                        </div>
                                                    </div>

                                                    <div class="d-grid">
                                                        <button type="button" class="btn btn-danger" onclick="uploadJsonFiles()" id="uploadJsonBtn">
                                                            <i class="fas fa-cloud-upload-alt me-2"></i>
                                                            Carica File JSON
                                                        </button>
                                                    </div>
                                                </form>

                                                <!-- Progress Bar -->
                                                <div class="mt-3" id="uploadJsonProgress" style="display: none;">
                                                    <div class="progress">
                                                        <div class="progress-bar progress-bar-striped progress-bar-animated"
                                                             role="progressbar" style="width: 0%" id="uploadJsonProgressBar"></div>
                                                    </div>
                                                    <small class="text-muted mt-1" id="uploadJsonStatus">Caricamento in corso...</small>
                                                </div>

                                                <!-- Risultati Upload -->
                                                <div class="mt-3" id="uploadJsonResults" style="display: none;">
                                                    <!-- Risultati dinamici -->
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-4">
                                        <div class="card">
                                            <div class="card-header">
                                                <h6 class="mb-0">
                                                    <i class="fas fa-question-circle me-2"></i>
                                                    Formato File JSON
                                                </h6>
                                            </div>
                                            <div class="card-body">
                                                <p class="small mb-2">
                                                    <strong>Struttura richiesta:</strong>
                                                </p>
                                                <pre class="small bg-light p-2 rounded"><code>{
  "metadata": {
    "data_archiviazione": "...",
    "archiviato_da": "...",
    "periodo": "...",
    "totale_viaggi": 0
  },
  "viaggi": [
    {
      "viaggio_id": 123,
      "viaggio_data": {...},
      "orari_data": [...],
      "import_data": [...],
      "export_data": [...]
    }
  ]
}</code></pre>

                                                <div class="mt-3">
                                                    <h6 class="small">
                                                        <i class="fas fa-shield-alt me-1"></i>
                                                        Sicurezza
                                                    </h6>
                                                    <ul class="small mb-0">
                                                        <li>Validazione JSON automatica</li>
                                                        <li>Controllo estensione file</li>
                                                        <li>Prevenzione sovrascrittura</li>
                                                        <li>Logging completo operazioni</li>
                                                    </ul>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="card mt-3">
                                            <div class="card-header">
                                                <h6 class="mb-0">
                                                    <i class="fas fa-history me-2"></i>
                                                    Cronologia Upload
                                                </h6>
                                            </div>
                                            <div class="card-body">
                                                <div id="uploadHistory">
                                                    <p class="text-muted small mb-0">
                                                        <i class="fas fa-info-circle me-1"></i>
                                                        La cronologia degli upload apparirà qui dopo il primo caricamento.
                                                    </p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                    <!-- Fine TAB 4: CARICA FILE ARCHIVIATI -->

                </div>
                <!-- Fine Tab Content -->
            </div>
        </div>
    </div>

    <!-- Modal Dettagli SOF -->
    <div class="modal fade" id="modalDettagliSOF" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="modalDettagliTitle">
                        <i class="fas fa-info-circle me-2"></i>
                        Dettagli SOF Archiviato
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="modalDettagliContent">
                    <!-- Contenuto caricato dinamicamente -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Chiudi</button>
                    <button type="button" class="btn btn-success" id="btnScaricaSOFModal">
                        <i class="fas fa-download me-1"></i>
                        Scarica SOF
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal Dettagli Viaggio Completo (per archivio JSON) -->
    <div class="modal fade" id="modalDettagliViaggioCompleto" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="modalDettagliViaggioCompletoTitle">
                        <i class="fas fa-ship me-2"></i>
                        Dettagli Completi Viaggio
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="modalDettagliViaggioCompletoContent">
                    <!-- Contenuto caricato dinamicamente -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-outline-primary" onclick="chiudiModalDettagliCompleti()">
                        <i class="fas fa-arrow-left me-1"></i>
                        Torna alla Lista
                    </button>
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-1"></i>
                        Chiudi Tutto
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- JavaScript per SOF Archiviati -->
    <script src="{{ url_for('static', path='js/sof-archiviati.js') }}"></script>

    <!-- Debug Script -->
    <script>
        console.log('🔧 DEBUG: Pagina SOF Archiviati caricata');
        console.log('🔧 DEBUG: visualizzaDettagliJSON disponibile?', typeof window.visualizzaDettagliJSON);
        console.log('🔧 DEBUG: Bootstrap disponibile?', typeof bootstrap);

        // Test funzione quando la pagina è completamente caricata
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🔧 DEBUG: DOM caricato');
            console.log('🔧 DEBUG: Modal modalDettagliSOF esiste?', !!document.getElementById('modalDettagliSOF'));

            // Test pulsante se esiste
            const testButton = document.querySelector('button[onclick*="visualizzaDettagliJSON"]');
            if (testButton) {
                console.log('🔧 DEBUG: Trovato pulsante con onclick visualizzaDettagliJSON');
            } else {
                console.log('🔧 DEBUG: Nessun pulsante con onclick visualizzaDettagliJSON trovato');
            }
        });
    </script>

    <!-- JavaScript per Gestione Archivio -->
    <script>
        // Gestione campi dinamici per tipo periodo
        function aggiornaCampiPeriodo() {
            const tipoPeriodo = document.getElementById('tipoPeriodo').value;
            const campiPeriodo = document.getElementById('campiPeriodo');
            const btnArchivia = document.getElementById('btnArchiviaPeriodo');

            campiPeriodo.innerHTML = '';
            btnArchivia.disabled = true;
            
            // Reset della tabella - mostra tutte le righe
            resetTabellaViaggi();

            if (tipoPeriodo === 'anno') {
                campiPeriodo.innerHTML = `
                    <div class="mb-3">
                        <label class="form-label">Anno</label>
                        <select class="form-select" id="annoSelezionato" onchange="validaCampiPeriodo()">
                            <option value="">Seleziona anno</option>
                            {% for anno in anni_disponibili %}
                            <option value="{{ anno }}">{{ anno }}</option>
                            {% endfor %}
                        </select>
                    </div>
                `;
            } else if (tipoPeriodo === 'mese') {
                campiPeriodo.innerHTML = `
                    <div class="row">
                        <div class="col-6">
                            <label class="form-label">Anno</label>
                            <select class="form-select" id="annoMese" onchange="validaCampiPeriodo()">
                                <option value="">Anno</option>
                                {% for anno in anni_disponibili %}
                                <option value="{{ anno }}">{{ anno }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-6">
                            <label class="form-label">Mese</label>
                            <select class="form-select" id="meseSelezionato" onchange="validaCampiPeriodo()">
                                <option value="">Mese</option>
                                <option value="1">Gennaio</option>
                                <option value="2">Febbraio</option>
                                <option value="3">Marzo</option>
                                <option value="4">Aprile</option>
                                <option value="5">Maggio</option>
                                <option value="6">Giugno</option>
                                <option value="7">Luglio</option>
                                <option value="8">Agosto</option>
                                <option value="9">Settembre</option>
                                <option value="10">Ottobre</option>
                                <option value="11">Novembre</option>
                                <option value="12">Dicembre</option>
                            </select>
                        </div>
                    </div>
                `;
            } else if (tipoPeriodo === 'trimestre') {
                campiPeriodo.innerHTML = `
                    <div class="row">
                        <div class="col-6">
                            <label class="form-label">Anno</label>
                            <select class="form-select" id="annoTrimestre" onchange="validaCampiPeriodo()">
                                <option value="">Anno</option>
                                {% for anno in anni_disponibili %}
                                <option value="{{ anno }}">{{ anno }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-6">
                            <label class="form-label">Trimestre</label>
                            <select class="form-select" id="trimestreSelezionato" onchange="validaCampiPeriodo()">
                                <option value="">Trimestre</option>
                                <option value="1">Q1 (Gen-Mar)</option>
                                <option value="2">Q2 (Apr-Giu)</option>
                                <option value="3">Q3 (Lug-Set)</option>
                                <option value="4">Q4 (Ott-Dic)</option>
                            </select>
                        </div>
                    </div>
                `;
            } else if (tipoPeriodo === 'giorni') {
                campiPeriodo.innerHTML = `
                    <div class="row">
                        <div class="col-6">
                            <label class="form-label">Data Inizio</label>
                            <input type="date" class="form-control" id="dataInizio" onchange="validaCampiPeriodo()">
                        </div>
                        <div class="col-6">
                            <label class="form-label">Data Fine</label>
                            <input type="date" class="form-control" id="dataFine" onchange="validaCampiPeriodo()">
                        </div>
                    </div>
                `;
            }
        }

        function validaCampiPeriodo() {
            const tipoPeriodo = document.getElementById('tipoPeriodo').value;
            const btnArchivia = document.getElementById('btnArchiviaPeriodo');
            let valido = false;

            if (tipoPeriodo === 'anno') {
                valido = document.getElementById('annoSelezionato').value !== '';
            } else if (tipoPeriodo === 'mese') {
                valido = document.getElementById('annoMese').value !== '' &&
                        document.getElementById('meseSelezionato').value !== '';
            } else if (tipoPeriodo === 'trimestre') {
                valido = document.getElementById('annoTrimestre').value !== '' &&
                        document.getElementById('trimestreSelezionato').value !== '';
            } else if (tipoPeriodo === 'giorni') {
                valido = document.getElementById('dataInizio').value !== '' &&
                        document.getElementById('dataFine').value !== '';
            }

            btnArchivia.disabled = !valido;
            
            // Se i campi sono validi, aggiorna la tabella con i dati filtrati
            if (valido) {
                filtraViaggiPerPeriodo();
            }
        }
        
        // Funzione per resettare la tabella e mostrare tutte le righe
        function resetTabellaViaggi() {
            const tableBody = document.querySelector('.table tbody');
            if (!tableBody) return;
            
            // Mostra tutte le righe
            const rows = tableBody.querySelectorAll('tr');
            rows.forEach(row => {
                row.style.display = '';
            });
            
            // Rimuovi il messaggio "nessun dato"
            const tableContainer = document.querySelector('.table-responsive');
            if (tableContainer) {
                const noDataMessage = tableContainer.querySelector('.no-data-message');
                if (noDataMessage) {
                    noDataMessage.style.display = 'none';
                }
            }
            
            // Deseleziona tutte le checkbox
            document.getElementById('selectAll').checked = false;
            const checkboxes = document.querySelectorAll('.viaggio-checkbox');
            checkboxes.forEach(checkbox => {
                checkbox.checked = false;
            });
            
            // Aggiorna contatore
            aggiornaContatori();
        }
        
        // Funzione per filtrare i viaggi in base al periodo selezionato
        function filtraViaggiPerPeriodo() {
            const tipoPeriodo = document.getElementById('tipoPeriodo').value;
            const tableBody = document.querySelector('.table tbody');
            const rows = tableBody.querySelectorAll('tr');
            let filteredRows = [];
            
            // Ottieni i valori dei filtri in base al tipo di periodo
            let filtroAnno, filtroMese, filtroTrimestre, filtroDataInizio, filtroDataFine;
            
            if (tipoPeriodo === 'anno') {
                filtroAnno = document.getElementById('annoSelezionato').value;
            } else if (tipoPeriodo === 'mese') {
                filtroAnno = document.getElementById('annoMese').value;
                filtroMese = document.getElementById('meseSelezionato').value;
            } else if (tipoPeriodo === 'trimestre') {
                filtroAnno = document.getElementById('annoTrimestre').value;
                filtroTrimestre = document.getElementById('trimestreSelezionato').value;
            } else if (tipoPeriodo === 'giorni') {
                filtroDataInizio = new Date(document.getElementById('dataInizio').value);
                filtroDataFine = new Date(document.getElementById('dataFine').value);
            }
            
            // Filtra le righe in base al tipo di periodo
            rows.forEach(row => {
                const checkbox = row.querySelector('.viaggio-checkbox');
                if (!checkbox) return;
                
                const rowAnno = checkbox.getAttribute('data-anno');
                const rowMese = checkbox.getAttribute('data-mese');
                const rowTrimestre = checkbox.getAttribute('data-trimestre');
                const rowData = row.querySelector('td:nth-child(4)').textContent; // Data Arrivo
                
                let include = false;
                
                if (tipoPeriodo === 'anno') {
                    include = rowAnno === filtroAnno;
                } else if (tipoPeriodo === 'mese') {
                    include = rowAnno === filtroAnno && rowMese === filtroMese;
                } else if (tipoPeriodo === 'trimestre') {
                    include = rowAnno === filtroAnno && rowTrimestre === filtroTrimestre;
                } else if (tipoPeriodo === 'giorni') {
                    const parts = rowData.split('/');
                    if (parts.length === 3) {
                        const rowDate = new Date(parts[2], parts[1] - 1, parts[0]);
                        include = rowDate >= filtroDataInizio && rowDate <= filtroDataFine;
                    }
                }
                
                row.style.display = include ? '' : 'none';
                if (include) {
                    filteredRows.push(row);
                }
            });
            
            // Mostra un messaggio se non ci sono risultati
            const tableContainer = document.querySelector('.table-responsive');
            let noDataMessage = tableContainer.querySelector('.no-data-message');
            
            if (filteredRows.length === 0) {
                if (!noDataMessage) {
                    noDataMessage = document.createElement('div');
                    noDataMessage.className = 'no-data-message alert alert-info mt-3';
                    noDataMessage.innerHTML = '<i class="fas fa-info-circle me-2"></i>Nessun viaggio disponibile per il periodo selezionato.';
                    tableContainer.appendChild(noDataMessage);
                }
                noDataMessage.style.display = 'block';
            } else if (noDataMessage) {
                noDataMessage.style.display = 'none';
            }
            
            // Aggiorna il contatore selezionati
            aggiornaContatori();
        }

        // Gestione selezione viaggi
        function toggleSelectAll() {
            const selectAll = document.getElementById('selectAll');
            const checkboxes = document.querySelectorAll('.viaggio-checkbox');

            checkboxes.forEach(checkbox => {
                checkbox.checked = selectAll.checked;
            });

            aggiornaContatori();
        }

        function selezionaTutti() {
            document.getElementById('selectAll').checked = true;
            toggleSelectAll();
        }

        function deselezionaTutti() {
            document.getElementById('selectAll').checked = false;
            toggleSelectAll();
        }

        function aggiornaContatori() {
            const checkboxes = document.querySelectorAll('.viaggio-checkbox:checked');
            const contatore = document.getElementById('contatoreSelezionati');
            const btnArchivia = document.getElementById('btnArchiviaSelezionati');

            contatore.textContent = checkboxes.length + ' selezionati';
            btnArchivia.disabled = checkboxes.length === 0;
        }

        // Funzioni di archiviazione
        async function archiviaPerPeriodo() {
            const tipoPeriodo = document.getElementById('tipoPeriodo').value;

            if (!tipoPeriodo) {
                showAlert('Seleziona un tipo di periodo', 'warning');
                return;
            }

            let parametri = { periodo_tipo: tipoPeriodo };
            let descrizioneOperazione = '';

            if (tipoPeriodo === 'anno') {
                const anno = document.getElementById('annoSelezionato').value;
                if (!anno) {
                    showAlert('Seleziona un anno', 'warning');
                    return;
                }
                parametri.periodo_valore = anno;
                descrizioneOperazione = `tutti i viaggi dell'anno ${anno}`;
            } else if (tipoPeriodo === 'mese') {
                const anno = document.getElementById('annoMese').value;
                const mese = document.getElementById('meseSelezionato').value;
                if (!anno || !mese) {
                    showAlert('Seleziona anno e mese', 'warning');
                    return;
                }
                parametri.anno = anno;
                parametri.mese = mese;
                const nomiMesi = ['Gennaio', 'Febbraio', 'Marzo', 'Aprile', 'Maggio', 'Giugno',
                                'Luglio', 'Agosto', 'Settembre', 'Ottobre', 'Novembre', 'Dicembre'];
                descrizioneOperazione = `tutti i viaggi di ${nomiMesi[parseInt(mese)-1]} ${anno}`;
            } else if (tipoPeriodo === 'trimestre') {
                const anno = document.getElementById('annoTrimestre').value;
                const trimestre = document.getElementById('trimestreSelezionato').value;
                if (!anno || !trimestre) {
                    showAlert('Seleziona anno e trimestre', 'warning');
                    return;
                }
                parametri.anno = anno;
                parametri.trimestre = trimestre;
                descrizioneOperazione = `tutti i viaggi del Q${trimestre} ${anno}`;
            } else if (tipoPeriodo === 'giorni') {
                const dataInizio = document.getElementById('dataInizio').value;
                const dataFine = document.getElementById('dataFine').value;
                if (!dataInizio || !dataFine) {
                    showAlert('Seleziona data inizio e fine', 'warning');
                    return;
                }
                parametri.data_inizio = dataInizio;
                parametri.data_fine = dataFine;
                descrizioneOperazione = `tutti i viaggi dal ${dataInizio} al ${dataFine}`;
            }

            // Conferma con descrizione dettagliata
            const conferma = confirm(`ATTENZIONE: Stai per archiviare ${descrizioneOperazione}.\n\nQuesta operazione:\n- Cancellerà DEFINITIVAMENTE i viaggi dal database\n- Creerà un file JSON di backup\n- NON può essere annullata\n\nSei sicuro di voler continuare?`);

            if (!conferma) {
                return;
            }

            console.log('Parametri archiviazione:', parametri);
            await eseguiArchiviazione(parametri);
        }

        async function archiviaSelezionati() {
            const checkboxes = document.querySelectorAll('.viaggio-checkbox:checked');
            const viaggiIds = Array.from(checkboxes).map(cb => parseInt(cb.value));

            if (viaggiIds.length === 0) {
                alert('Seleziona almeno un viaggio');
                return;
            }

            if (!confirm(`ATTENZIONE: Questa operazione cancellerà definitivamente ${viaggiIds.length} viaggi dal database. Continuare?`)) {
                return;
            }

            const parametri = {
                periodo_tipo: 'specifici',
                viaggi_ids: viaggiIds
            };

            await eseguiArchiviazione(parametri);
        }

        async function eseguiArchiviazione(parametri) {
            const btnArchivia = document.getElementById('btnArchiviaPeriodo');
            const originalText = btnArchivia.innerHTML;

            try {
                // Disabilita pulsante e mostra loading
                btnArchivia.disabled = true;
                btnArchivia.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Archiviazione in corso...';

                // Mostra progress dettagliato
                showProgressModal('Archiviazione in corso...', 'Preparazione dei dati...');

                console.log('Invio richiesta archiviazione con parametri:', parametri);

                const response = await fetch('/api/operativo/sof/archivia-json', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(parametri)
                });

                console.log('Response status:', response.status);
                console.log('Response headers:', response.headers);

                const result = await response.json();
                console.log('Response data:', result);

                hideProgressModal();

                if (response.ok && result.success) {
                    showSuccessModal(
                        'Archiviazione Completata!',
                        `✅ File creato: ${result.filename}\n📊 Viaggi archiviati: ${result.viaggi_archiviati}\n📁 Percorso: ${result.file_path}`,
                        () => {
                            window.location.reload();
                        }
                    );
                } else {
                    showErrorModal(
                        'Errore Archiviazione',
                        `❌ ${result.message || 'Errore sconosciuto'}\n\nDettagli tecnici:\nStatus: ${response.status}\nResponse: ${JSON.stringify(result, null, 2)}`,
                        () => {
                            window.location.reload();
                        }
                    );
                }

            } catch (error) {
                console.error('Errore archiviazione:', error);
                hideProgressModal();
                showErrorModal(
                    'Errore di Connessione',
                    `❌ Impossibile completare l'archiviazione.\n\nDettagli tecnici:\n${error.message}\n\nControlla la console per maggiori dettagli.`,
                    () => {
                        window.location.reload();
                    }
                );
            } finally {
                // Ripristina pulsante
                btnArchivia.disabled = false;
                btnArchivia.innerHTML = originalText;
            }
        }

        // Funzioni per visualizzazione JSON - Rimosse dal template HTML
        // Le funzioni sono ora gestite dal file JavaScript esterno sof-archiviati.js

        function scaricaJSON(filename) {
            window.open('/api/operativo/sof/download-json/' + encodeURIComponent(filename), '_blank');
        }

        // Funzioni mancanti per il funzionamento dei pulsanti
        function uploadFiles() {
            console.log('Upload files function called');
            // Implementazione placeholder - da completare se necessario
        }

        function scaricaSOF(filename) {
            window.open('/api/sof/archiviati/' + encodeURIComponent(filename) + '/download', '_blank');
        }

        // La funzione visualizzaDettagliJSON è ora gestita dal file JavaScript esterno sof-archiviati.js

        /**
         * Gestisce la chiusura corretta del modal dettagli completi
         */
        function chiudiModalDettagliCompleti() {
            console.log('🔄 Chiusura modal dettagli completi');

            // Chiudi il modal dei dettagli completi
            const modalCompleto = bootstrap.Modal.getInstance(document.getElementById('modalDettagliViaggioCompleto'));
            if (modalCompleto) {
                modalCompleto.hide();
            }

            // Aspetta che il modal si chiuda completamente prima di riaprire quello precedente
            setTimeout(() => {
                // Ripristina il contenuto del modal lista viaggi dalle variabili globali
                const modalContent = document.getElementById('modalDettagliContent');
                const modalTitle = document.getElementById('modalDettagliTitle');

                if (modalContent && window.contenutoModalListaViaggi) {
                    modalContent.innerHTML = window.contenutoModalListaViaggi;
                    console.log('✅ Contenuto modal lista viaggi ripristinato');
                }

                if (modalTitle && window.titoloModalListaViaggi) {
                    modalTitle.innerHTML = window.titoloModalListaViaggi;
                    console.log('✅ Titolo modal lista viaggi ripristinato');
                }

                // Riapri il modal della lista viaggi
                const modalLista = new bootstrap.Modal(document.getElementById('modalDettagliSOF'));
                modalLista.show();

                console.log('✅ Modal lista viaggi riaperto con contenuto');
            }, 300); // Aspetta 300ms per la transizione
        }

        // Gestione eventi modal per prevenire blocchi
        document.addEventListener('DOMContentLoaded', function() {
            // Gestione chiusura modal principale
            const modalPrincipale = document.getElementById('modalDettagliSOF');
            if (modalPrincipale) {
                modalPrincipale.addEventListener('hidden.bs.modal', function() {
                    console.log('🔄 Modal principale chiuso');
                    // NON pulire il contenuto se stiamo navigando verso il modal dettagli
                    // Il contenuto verrà gestito dalle funzioni specifiche
                });
            }

            // Gestione chiusura modal dettagli completi
            const modalCompleto = document.getElementById('modalDettagliViaggioCompleto');
            if (modalCompleto) {
                modalCompleto.addEventListener('hidden.bs.modal', function() {
                    console.log('🔄 Modal dettagli completi chiuso');
                    // Pulisci solo il contenuto del modal dettagli completi
                    const content = document.getElementById('modalDettagliViaggioCompletoContent');
                    if (content) {
                        content.innerHTML = '';
                    }
                });
            }
        });

        function visualizzaDettagliViaggio(idViaggio) {
            console.log('Visualizza dettagli viaggio:', idViaggio);
            
            // Mostra loading
            const modalContent = document.getElementById('modalDettagliContent');
            modalContent.innerHTML = `
                <div class="text-center py-4">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Caricamento...</span>
                    </div>
                    <div class="mt-2">Caricamento dettagli viaggio...</div>
                </div>
            `;
            
            // Carica dettagli via API
            fetch(`/api/operativo/sof/archiviati/viaggio/${idViaggio}/dati`)
                .then(response => response.json())
                .then(result => {
                    if (result.success) {
                        const viaggio = result.viaggio;
                        const viaggioData = viaggio.viaggio_data || {};
                        const orariData = viaggio.orari_data || [];
                        const importData = viaggio.import_data || [];
                        const exportData = viaggio.export_data || [];
                        
                        const nomeNave = viaggioData.nome_nave || 'N/A';
                        const portoGestione = viaggioData.porto_gestione || 'N/A';
                        const codiceViaggio = viaggioData.codice_viaggio || 'N/A';
                        
                        // Aggiorna il titolo del modal
                        const modalTitle = document.getElementById('modalDettagliTitle');
                        if (modalTitle) {
                            modalTitle.innerHTML = `
                                <i class="fas fa-ship me-2"></i>
                                Dettagli Viaggio: ${codiceViaggio}
                            `;
                        }
                        
                        // Genera HTML semplificato
                        modalContent.innerHTML = `
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="card mb-3">
                                        <div class="card-header">
                                            <h6 class="mb-0">
                                                <i class="fas fa-ship me-2"></i>
                                                Informazioni Viaggio
                                            </h6>
                                        </div>
                                        <div class="card-body">
                                            <table class="table table-sm">
                                                <tr><td><strong>Nome Nave:</strong></td><td>${nomeNave}</td></tr>
                                                <tr><td><strong>Porto Gestione:</strong></td><td>${portoGestione}</td></tr>
                                                <tr><td><strong>Codice Viaggio:</strong></td><td>${codiceViaggio}</td></tr>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card mb-3">
                                        <div class="card-header">
                                            <h6 class="mb-0">
                                                <i class="fas fa-chart-bar me-2"></i>
                                                Statistiche
                                            </h6>
                                        </div>
                                        <div class="card-body">
                                            <table class="table table-sm">
                                                <tr><td><strong>Record Orari:</strong></td><td>${orariData.length}</td></tr>
                                                <tr><td><strong>Record Import:</strong></td><td>${importData.length}</td></tr>
                                                <tr><td><strong>Record Export:</strong></td><td>${exportData.length}</td></tr>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0">
                                        <i class="fas fa-code me-2"></i>
                                        Dati JSON Completi
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <pre class="bg-light p-3 rounded" style="max-height: 400px; overflow-y: auto; font-size: 0.8rem;">${JSON.stringify(viaggio, null, 2)}</pre>
                                </div>
                            </div>
                        `;
                    } else {
                        modalContent.innerHTML = `
                            <div class="alert alert-danger">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                Errore nel caricamento dei dettagli: ${result.message || 'Errore sconosciuto'}
                            </div>
                        `;
                    }
                })
                .catch(error => {
                    console.error('Errore caricamento dettagli viaggio:', error);
                    modalContent.innerHTML = `
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            Errore nel caricamento dei dettagli: ${error.message}
                        </div>
                    `;
                });
        }

        // Funzioni per upload file JSON
        async function uploadJsonFiles() {
            const fileInput = document.getElementById('jsonFileInput');
            const validateJson = document.getElementById('validateJson').checked;
            const uploadBtn = document.getElementById('uploadJsonBtn');
            const progressDiv = document.getElementById('uploadJsonProgress');
            const progressBar = document.getElementById('uploadJsonProgressBar');
            const statusDiv = document.getElementById('uploadJsonStatus');
            const resultsDiv = document.getElementById('uploadJsonResults');

            if (!fileInput.files || fileInput.files.length === 0) {
                alert('Seleziona almeno un file JSON');
                return;
            }

            // Validazione client-side se richiesta
            if (validateJson) {
                for (let file of fileInput.files) {
                    if (!file.name.endsWith('.json')) {
                        alert(`File ${file.name} non è un file JSON`);
                        return;
                    }

                    // Validazione dimensione (max 50MB)
                    if (file.size > 50 * 1024 * 1024) {
                        alert(`File ${file.name} troppo grande (max 50MB)`);
                        return;
                    }
                }
            }

            try {
                // Mostra progress
                uploadBtn.disabled = true;
                progressDiv.style.display = 'block';
                resultsDiv.style.display = 'none';
                progressBar.style.width = '0%';
                statusDiv.textContent = 'Preparazione upload...';

                // Prepara FormData
                const formData = new FormData();
                for (let file of fileInput.files) {
                    formData.append('files', file);
                }

                // Simula progresso
                let progress = 0;
                const progressInterval = setInterval(() => {
                    progress += Math.random() * 20;
                    if (progress > 90) progress = 90;
                    progressBar.style.width = progress + '%';
                    statusDiv.textContent = `Caricamento in corso... ${Math.round(progress)}%`;
                }, 200);

                // Upload
                const response = await fetch('/api/operativo/sof/upload-json', {
                    method: 'POST',
                    body: formData
                });

                clearInterval(progressInterval);
                progressBar.style.width = '100%';
                statusDiv.textContent = 'Elaborazione completata';

                const result = await response.json();

                // Mostra risultati
                setTimeout(() => {
                    progressDiv.style.display = 'none';
                    resultsDiv.style.display = 'block';

                    if (result.success) {
                        resultsDiv.innerHTML = `
                            <div class="alert alert-success">
                                <h6><i class="fas fa-check-circle me-2"></i>Upload completato con successo!</h6>
                                <p class="mb-2">${result.message}</p>
                                <ul class="mb-0">
                                    <li>File caricati: ${result.successful_uploads}</li>
                                    <li>Errori: ${result.failed_uploads}</li>
                                    <li>Totale file processati: ${result.total_files}</li>
                                </ul>
                            </div>
                        `;

                        // Mostra dettagli file caricati
                        if (result.files_uploaded && result.files_uploaded.length > 0) {
                            let filesHtml = '<div class="mt-3"><h6>File caricati:</h6><div class="row">';
                            result.files_uploaded.forEach(file => {
                                filesHtml += `
                                    <div class="col-md-6 mb-2">
                                        <div class="card border-success">
                                            <div class="card-body p-2">
                                                <h6 class="card-title small mb-1">${file.filename}</h6>
                                                <p class="card-text small mb-1">
                                                    <i class="fas fa-ship me-1"></i>${file.viaggi_count} viaggi |
                                                    <i class="fas fa-download me-1"></i>${file.import_count} import |
                                                    <i class="fas fa-upload me-1"></i>${file.export_count} export
                                                </p>
                                                <p class="card-text small mb-0 text-muted">
                                                    <i class="fas fa-weight me-1"></i>${(file.size / 1024).toFixed(1)} KB
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                `;
                            });
                            filesHtml += '</div></div>';
                            resultsDiv.innerHTML += filesHtml;
                        }

                        // Mostra errori se presenti
                        if (result.errors && result.errors.length > 0) {
                            let errorsHtml = '<div class="mt-3"><h6 class="text-warning">Errori:</h6><ul class="small">';
                            result.errors.forEach(error => {
                                errorsHtml += `<li class="text-warning">${error}</li>`;
                            });
                            errorsHtml += '</ul></div>';
                            resultsDiv.innerHTML += errorsHtml;
                        }

                        // Aggiorna cronologia
                        updateUploadHistory(result.files_uploaded);

                        // Reset form
                        fileInput.value = '';

                    } else {
                        resultsDiv.innerHTML = `
                            <div class="alert alert-danger">
                                <h6><i class="fas fa-exclamation-triangle me-2"></i>Errore durante l'upload</h6>
                                <p class="mb-0">${result.message}</p>
                            </div>
                        `;
                    }
                }, 1000);

            } catch (error) {
                console.error('Errore upload:', error);
                progressDiv.style.display = 'none';
                resultsDiv.style.display = 'block';
                resultsDiv.innerHTML = `
                    <div class="alert alert-danger">
                        <h6><i class="fas fa-exclamation-triangle me-2"></i>Errore di connessione</h6>
                        <p class="mb-0">Impossibile completare l'upload. Riprova più tardi.</p>
                    </div>
                `;
            } finally {
                uploadBtn.disabled = false;
            }
        }

        function updateUploadHistory(uploadedFiles) {
            const historyDiv = document.getElementById('uploadHistory');

            if (!uploadedFiles || uploadedFiles.length === 0) return;

            const now = new Date().toLocaleString('it-IT');
            let historyHtml = `
                <div class="border-bottom pb-2 mb-2">
                    <small class="text-muted">${now}</small>
                    <div class="small">
                        <strong>${uploadedFiles.length} file caricati:</strong>
                    </div>
                    <ul class="small mb-0">
            `;

            uploadedFiles.forEach(file => {
                historyHtml += `<li>${file.filename} (${file.viaggi_count} viaggi)</li>`;
            });

            historyHtml += '</ul></div>';

            // Aggiungi in cima alla cronologia
            if (historyDiv.innerHTML.includes('La cronologia')) {
                historyDiv.innerHTML = historyHtml;
            } else {
                historyDiv.innerHTML = historyHtml + historyDiv.innerHTML;
            }
        }

        // Funzioni helper per UI
        function showAlert(message, type = 'info') {
            const alertHtml = `
                <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                    <i class="fas fa-${type === 'warning' ? 'exclamation-triangle' : type === 'success' ? 'check-circle' : 'info-circle'} me-2"></i>
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;

            // Trova il container per gli alert
            let alertContainer = document.getElementById('alertContainer');
            if (!alertContainer) {
                alertContainer = document.createElement('div');
                alertContainer.id = 'alertContainer';
                alertContainer.className = 'position-fixed top-0 end-0 p-3';
                alertContainer.style.zIndex = '9999';
                document.body.appendChild(alertContainer);
            }

            alertContainer.innerHTML = alertHtml;

            // Auto-rimuovi dopo 5 secondi
            setTimeout(() => {
                const alert = alertContainer.querySelector('.alert');
                if (alert) {
                    alert.classList.remove('show');
                    setTimeout(() => alert.remove(), 150);
                }
            }, 5000);
        }

        function showProgressModal(title, message) {
            const modalHtml = `
                <div class="modal fade" id="progressModal" tabindex="-1" data-bs-backdrop="static" data-bs-keyboard="false">
                    <div class="modal-dialog modal-dialog-centered">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">${title}</h5>
                            </div>
                            <div class="modal-body text-center">
                                <div class="spinner-border text-primary mb-3" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                                <p id="progressMessage">${message}</p>
                                <div class="progress">
                                    <div class="progress-bar progress-bar-striped progress-bar-animated"
                                         role="progressbar" style="width: 100%"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // Rimuovi modal esistente
            const existingModal = document.getElementById('progressModal');
            if (existingModal) existingModal.remove();

            document.body.insertAdjacentHTML('beforeend', modalHtml);
            const modal = new bootstrap.Modal(document.getElementById('progressModal'));
            modal.show();
        }

        function hideProgressModal() {
            const modal = document.getElementById('progressModal');
            if (modal) {
                const bsModal = bootstrap.Modal.getInstance(modal);
                if (bsModal) bsModal.hide();
                setTimeout(() => modal.remove(), 300);
            }
        }

        function showSuccessModal(title, message, callback) {
            const modalHtml = `
                <div class="modal fade" id="successModal" tabindex="-1">
                    <div class="modal-dialog modal-dialog-centered">
                        <div class="modal-content">
                            <div class="modal-header bg-success text-white">
                                <h5 class="modal-title">
                                    <i class="fas fa-check-circle me-2"></i>${title}
                                </h5>
                            </div>
                            <div class="modal-body">
                                <pre class="bg-light p-3 rounded">${message}</pre>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-success" onclick="closeSuccessModal()">
                                    <i class="fas fa-check me-2"></i>OK
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            document.body.insertAdjacentHTML('beforeend', modalHtml);
            const modal = new bootstrap.Modal(document.getElementById('successModal'));
            modal.show();

            window.closeSuccessModal = function() {
                modal.hide();
                setTimeout(() => {
                    document.getElementById('successModal').remove();
                    if (callback) callback();
                }, 300);
            };
        }

        function showErrorModal(title, message, callback) {
            const modalHtml = `
                <div class="modal fade" id="errorModal" tabindex="-1">
                    <div class="modal-dialog modal-dialog-centered modal-lg">
                        <div class="modal-content">
                            <div class="modal-header bg-danger text-white">
                                <h5 class="modal-title">
                                    <i class="fas fa-exclamation-triangle me-2"></i>${title}
                                </h5>
                            </div>
                            <div class="modal-body">
                                <pre class="bg-light p-3 rounded" style="max-height: 300px; overflow-y: auto;">${message}</pre>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-danger" onclick="closeErrorModal()">
                                    <i class="fas fa-times me-2"></i>Chiudi
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            document.body.insertAdjacentHTML('beforeend', modalHtml);
            const modal = new bootstrap.Modal(document.getElementById('errorModal'));
            modal.show();

            window.closeErrorModal = function() {
                modal.hide();
                setTimeout(() => {
                    document.getElementById('errorModal').remove();
                    if (callback) callback();
                }, 300);
            };
        }

        // Inizializzazione
        document.addEventListener('DOMContentLoaded', function() {
            // Inizializzazione completata - valori unici gestiti lato server
            console.log('Sistema archiviazione inizializzato');

            // Gestione drag & drop per upload JSON
            const jsonFileInput = document.getElementById('jsonFileInput');
            if (jsonFileInput) {
                const uploadSection = jsonFileInput.closest('.upload-section');

                ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
                    uploadSection.addEventListener(eventName, preventDefaults, false);
                });

                function preventDefaults(e) {
                    e.preventDefault();
                    e.stopPropagation();
                }

                ['dragenter', 'dragover'].forEach(eventName => {
                    uploadSection.addEventListener(eventName, highlight, false);
                });

                ['dragleave', 'drop'].forEach(eventName => {
                    uploadSection.addEventListener(eventName, unhighlight, false);
                });

                function highlight(e) {
                    uploadSection.classList.add('border-primary', 'bg-light');
                }

                function unhighlight(e) {
                    uploadSection.classList.remove('border-primary', 'bg-light');
                }

                uploadSection.addEventListener('drop', handleDrop, false);

                function handleDrop(e) {
                    const dt = e.dataTransfer;
                    const files = dt.files;
                    jsonFileInput.files = files;
                }
            }
        });
    </script>
</body>
</html>
